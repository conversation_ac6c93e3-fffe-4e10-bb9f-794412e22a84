import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationService } from './organization.service';
import { Repository } from 'typeorm';
import { Organization } from '@/entities/organization.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { NotFoundException } from '@nestjs/common';

const mockOrgEntity = {
  organization_id: 'org-123',
  name: 'MACRA Ltd.',
  registration_number: 'REG123456',
  website: 'https://macra.mw',
  email: '<EMAIL>',
  phone: '+265123456789',
  fax: '+265123456788',
  date_incorporation: new Date('2020-01-01'),
  place_incorporation: 'Malawi',
  created_at: new Date(),
  updated_at: new Date(),
};

describe('OrganizationService', () => {
  let service: OrganizationService;
  let repo: Repository<Organization>;

  const mockRepo = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    merge: jest.fn(),
    softRemove: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationService,
        {
          provide: getRepositoryToken(Organization),
          useValue: mockRepo,
        },
      ],
    }).compile();

    service = module.get<OrganizationService>(OrganizationService);
    repo = module.get<Repository<Organization>>(getRepositoryToken(Organization));
  });

  afterEach(() => jest.clearAllMocks());

  describe('create', () => {
    it('should create and return a new organization', async () => {
      const dto: CreateOrganizationDto = {
        ...mockOrgEntity,
      };

      mockRepo.create.mockReturnValue(dto);
      mockRepo.save.mockResolvedValue(mockOrgEntity);

      const result = await service.create(dto);
      expect(mockRepo.create).toHaveBeenCalledWith(dto);
      expect(mockRepo.save).toHaveBeenCalledWith(dto);
      expect(result).toEqual(mockOrgEntity);
    });
  });

  describe('findAll', () => {
    it('should return an array of organizations', async () => {
      mockRepo.find.mockResolvedValue([mockOrgEntity]);
      const result = await service.findAll();
      expect(result).toEqual([mockOrgEntity]);
      expect(mockRepo.find).toHaveBeenCalledWith({
        relations: expect.any(Array),
        order: { created_at: 'DESC' },
      });
    });
  });

  describe('findOne', () => {
    it('should return a single organization by ID', async () => {
      mockRepo.findOne.mockResolvedValue(mockOrgEntity);
      const result = await service.findOne('org-123');
      expect(mockRepo.findOne).toHaveBeenCalledWith({
        where: { organization_id: 'org-123' },
        relations: expect.any(Array),
      });
      expect(result).toEqual(mockOrgEntity);
    });

    it('should throw NotFoundException if not found', async () => {
      mockRepo.findOne.mockResolvedValue(null);
      await expect(service.findOne('org-404')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update and return the organization', async () => {
      const updateDto: UpdateOrganizationDto = { name: 'New Name' };

      mockRepo.findOne.mockResolvedValue(mockOrgEntity);
      mockRepo.merge.mockReturnValue({ ...mockOrgEntity, ...updateDto });
      mockRepo.save.mockResolvedValue({ ...mockOrgEntity, ...updateDto });

      const result = await service.update('org-123', updateDto);
      expect(result).toEqual(expect.objectContaining({ name: 'New Name' }));
      expect(mockRepo.merge).toHaveBeenCalled();
      expect(mockRepo.save).toHaveBeenCalled();
    });
  });

  describe('remove (soft delete)', () => {
    it('should soft delete an organization', async () => {
      mockRepo.findOne.mockResolvedValue(mockOrgEntity);
      mockRepo.softRemove.mockResolvedValue(mockOrgEntity);

      await service.remove('org-123');

      expect(mockRepo.softRemove).toHaveBeenCalledWith(mockOrgEntity);
    });
  });

  describe('hardDelete', () => {
    it('should permanently delete an organization', async () => {
      mockRepo.findOne.mockResolvedValue(mockOrgEntity);
      mockRepo.remove.mockResolvedValue(mockOrgEntity);

      await service.hardDelete('org-123');

      expect(mockRepo.remove).toHaveBeenCalledWith(mockOrgEntity);
    });
  });
});
