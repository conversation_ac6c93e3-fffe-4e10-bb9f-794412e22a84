{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4n5daKV9Ewpw8Kfqeq4IquDdUeRzCzOFuvNHTlP8FGc=", "__NEXT_PREVIEW_MODE_ID": "1734f6d9578930d22e0a17226e3cea03", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cbd63c954ca2193e428319e4f6694cd2152395b2b8d2f4d00644a79e70d36901", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7a4b5f2d3e199b0fb1756778ebb232bef6721edde1562e13f5305e7c07046614"}}}, "sortedMiddleware": ["/"], "functions": {}}