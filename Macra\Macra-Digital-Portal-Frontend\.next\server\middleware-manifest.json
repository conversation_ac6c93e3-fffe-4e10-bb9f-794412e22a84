{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4n5daKV9Ewpw8Kfqeq4IquDdUeRzCzOFuvNHTlP8FGc=", "__NEXT_PREVIEW_MODE_ID": "d069192343426e404899988123c68a91", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7b0e29cde1149abd4576f8fb859ae71631c7006d05c32d91d119bfef074e76b7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "72ed51e15a9b1f73ba28cc758f672a1a66f7c220f42658b2cca19e98090d5e48"}}}, "sortedMiddleware": ["/"], "functions": {}}