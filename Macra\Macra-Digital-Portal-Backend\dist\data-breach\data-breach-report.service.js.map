{"version": 3, "file": "data-breach-report.service.js", "sourceRoot": "", "sources": ["../../src/data-breach/data-breach-report.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAwG;AACxG,6CAAmD;AACnD,qCAAyD;AACzD,2EAKqC;AAW9B,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAGxB;IAEA;IAEA;IANV,YAEU,gBAA8C,EAE9C,oBAA4D,EAE5D,uBAAkE;QAJlE,qBAAgB,GAAhB,gBAAgB,CAA8B;QAE9C,yBAAoB,GAApB,oBAAoB,CAAwC;QAE5D,4BAAuB,GAAvB,uBAAuB,CAA2C;IACzE,CAAC;IAEJ,KAAK,CAAC,MAAM,CACV,SAAoC,EACpC,UAAkB;QAElB,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;YAC1C,GAAG,SAAS;YACZ,aAAa,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;YAChD,WAAW,EAAE,UAAU;YACvB,UAAU,EAAE,UAAU;SACvB,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAG7D,MAAM,IAAI,CAAC,mBAAmB,CAC5B,WAAW,CAAC,SAAS,EACrB,4CAAgB,CAAC,SAAS,EAC1B,8BAA8B,EAC9B,UAAU,CACX,CAAC;QAEF,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,OAAO,CACX,SAAoC,EACpC,MAAc,EACd,UAAmB,KAAK;QAExB,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,OAAO,GAAG,YAAY,EACtB,UAAU,GAAG,MAAM,EACnB,GAAG,OAAO,EACX,GAAG,SAAS,CAAC;QAEd,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAG/C,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAGzC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QACpE,CAAC;QAGD,YAAY,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,UAAU,CAAC,CAAC;QAGtD,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEtC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;QAE9D,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;QAElE,OAAO;YACL,IAAI;YACJ,KAAK;YACL,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;YAClB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;SACrB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO,CACX,QAAgB,EAChB,MAAc,EACd,UAAmB,KAAK;QAExB,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE;aAC3C,KAAK,CAAC,8BAA8B,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QAGvD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,MAAM,EAAE,CAAC;QAE3C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,MAAM,CACV,QAAgB,EAChB,SAAoC,EACpC,MAAc,EACd,UAAmB,KAAK;QAExB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE;YAC9B,SAAS,EAAE,CAAC,UAAU,CAAC;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;QAC9D,CAAC;QAGD,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC;YAC9C,MAAM,IAAI,2BAAkB,CAAC,sCAAsC,CAAC,CAAC;QACvE,CAAC;QAGD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,uBAAuB,EAAE,qBAAqB,EAAE,kBAAkB,CAAC,CAAC;YAC5J,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAEnF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,IAAI,4BAAmB,CAAC,yCAAyC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrG,CAAC;QACH,CAAC;QAGD,IAAI,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;YAC3D,MAAM,IAAI,CAAC,mBAAmB,CAC5B,QAAQ,EACR,SAAS,CAAC,MAAM,EAChB,uBAAuB,MAAM,CAAC,MAAM,OAAO,SAAS,CAAC,MAAM,EAAE,EAC7D,MAAM,CACP,CAAC;YAEF,IAAI,SAAS,CAAC,MAAM,KAAK,4CAAgB,CAAC,QAAQ,EAAE,CAAC;gBACnD,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YACrC,CAAC;QACH,CAAC;QAGD,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;YAC5B,SAAS,CAAC,aAAa,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAQ,CAAC;QACrE,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACjC,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC;QAE3B,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,MAAM,CACV,QAAgB,EAChB,MAAc,EACd,UAAmB,KAAK;QAExB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;QAC9D,CAAC;QAGD,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC;YAC9C,MAAM,IAAI,2BAAkB,CAAC,sCAAsC,CAAC,CAAC;QACvE,CAAC;QAGD,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,aAAkD,EAClD,MAAc;QAEd,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE,EAAE,SAAS,EAAE,aAAa,CAAC,SAAS,EAAE;SAC9C,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;YAClD,GAAG,aAAa;YAChB,WAAW,EAAE,MAAM;SACpB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,QAAgB,EAChB,SAA0C,EAC1C,MAAc;QAEd,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACjD,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,0BAAiB,CAAC,8BAA8B,CAAC,CAAC;QAC9D,CAAC;QAGD,MAAM,IAAI,CAAC,mBAAmB,CAC5B,QAAQ,EACR,SAAS,CAAC,MAAM,EAChB,SAAS,CAAC,OAAO,EACjB,MAAM,CACP,CAAC;QAGF,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QACjC,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC;QAE3B,IAAI,SAAS,CAAC,MAAM,KAAK,4CAAgB,CAAC,QAAQ,EAAE,CAAC;YACnD,MAAM,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAClC,CAAC;QAED,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,QAAgB,EAChB,MAAwB,EACxB,OAA2B,EAC3B,MAAc;QAEd,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;YACxD,SAAS,EAAE,QAAQ;YACnB,MAAM;YACN,OAAO;YACP,UAAU,EAAE,MAAM;SACnB,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACzD,CAAC;IAEO,kBAAkB;QACxB,OAAO,IAAI,CAAC,gBAAgB;aACzB,kBAAkB,CAAC,QAAQ,CAAC;aAC5B,iBAAiB,CAAC,iBAAiB,EAAE,UAAU,CAAC;aAChD,iBAAiB,CAAC,iBAAiB,EAAE,UAAU,CAAC;aAChD,iBAAiB,CAAC,oBAAoB,EAAE,aAAa,CAAC;aACtD,iBAAiB,CAAC,uBAAuB,EAAE,gBAAgB,CAAC;aAC5D,iBAAiB,CAAC,wBAAwB,EAAE,iBAAiB,CAAC,CAAC;IACpE,CAAC;IAEO,YAAY,CAClB,YAAkD,EAClD,OAA2C;QAE3C,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,YAAY,CAAC,QAAQ,CAAC,yBAAyB,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,YAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC/B,YAAY,CAAC,QAAQ,CAAC,6CAA6C,EAAE,EAAE,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC3H,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,YAAY,CAAC,QAAQ,CAAC,2CAA2C,EAAE,EAAE,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACrH,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,YAAY,CAAC,QAAQ,CACnB,gHAAgH,EAChH,EAAE,MAAM,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,CAClC,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,MAAwB;QAC/C,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,qBAAqB,EAAE,MAAM,CAAC,qBAAqB;YACnD,mBAAmB,EAAE,MAAM,CAAC,mBAAmB;YAC/C,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;YACzC,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC1B,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO;gBAChC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU;gBACtC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS;gBACpC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK;aAC7B,CAAC,CAAC,CAAC,SAAS;YACb,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC1B,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO;gBAChC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU;gBACtC,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS;gBACpC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK;aAC7B,CAAC,CAAC,CAAC,SAAS;YACb,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;gBAClD,aAAa,EAAE,UAAU,CAAC,aAAa;gBACvC,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,WAAW,EAAE,UAAU,CAAC,WAAW;aACpC,CAAC,CAAC;YACH,cAAc,EAAE,MAAM,CAAC,cAAc,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACrD,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,OAAO,EAAE;oBACP,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO;oBAChC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU;oBACtC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS;iBACrC;aACF,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;CACF,CAAA;AAtWY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,4CAAgB,CAAC,CAAA;IAElC,WAAA,IAAA,0BAAgB,EAAC,sDAA0B,CAAC,CAAA;IAE5C,WAAA,IAAA,0BAAgB,EAAC,yDAA6B,CAAC,CAAA;qCAHtB,oBAAU;QAEN,oBAAU;QAEP,oBAAU;GAPlC,uBAAuB,CAsWnC"}