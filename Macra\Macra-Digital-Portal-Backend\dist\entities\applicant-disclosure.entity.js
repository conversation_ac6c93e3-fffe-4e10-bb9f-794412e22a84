"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicantDisclosure = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const applicant_entity_1 = require("./applicant.entity");
let ApplicantDisclosure = class ApplicantDisclosure {
    applicant_disclosure_id;
    applicant_id;
    censured;
    disciplined;
    penalized;
    suspended;
    prosecuted;
    convicted_warned_conduct;
    investigated_subjected;
    failed_debt_issued;
    litigation;
    adjudged_insolvent;
    creditor_compromise;
    liquidator_receiver_property_judicial_manager;
    voluntary_winding_up;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    applicant;
    creator;
    updater;
    generateId() {
        if (!this.applicant_disclosure_id) {
            this.applicant_disclosure_id = (0, uuid_1.v4)();
        }
    }
};
exports.ApplicantDisclosure = ApplicantDisclosure;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "applicant_disclosure_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "applicant_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "censured", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "disciplined", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "penalized", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "suspended", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "prosecuted", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "convicted_warned_conduct", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "investigated_subjected", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "failed_debt_issued", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "litigation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "adjudged_insolvent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "creditor_compromise", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "liquidator_receiver_property_judicial_manager", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "voluntary_winding_up", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ApplicantDisclosure.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ApplicantDisclosure.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ApplicantDisclosure.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], ApplicantDisclosure.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => applicant_entity_1.Applicants),
    (0, typeorm_1.JoinColumn)({ name: 'applicant_id' }),
    __metadata("design:type", applicant_entity_1.Applicants)
], ApplicantDisclosure.prototype, "applicant", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ApplicantDisclosure.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], ApplicantDisclosure.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApplicantDisclosure.prototype, "generateId", null);
exports.ApplicantDisclosure = ApplicantDisclosure = __decorate([
    (0, typeorm_1.Entity)('applicant_disclosure')
], ApplicantDisclosure);
//# sourceMappingURL=applicant-disclosure.entity.js.map