import { Address } from '../entities/address.entity';
import { AddressService } from './address.service';
import { CreateAddressDto } from '../dto/address/create.dto';
import { UpdateAddressDto } from '../dto/address/update.dto';
export declare class AddressController {
    private readonly addressService;
    constructor(addressService: AddressService);
    createAddress(createAddressDto: CreateAddressDto, req: any): Promise<Address>;
    editAddress(updateDto: UpdateAddressDto, req: any): Promise<Address>;
    getAllAddresses(origin?: string, type?: string): Promise<Address[]>;
    getAddressById(id: string): Promise<Address>;
    softDeleteAddress(id: string, req: any): Promise<void>;
    restoreAddress(id: string): Promise<void>;
    hardDeleteAddress(id: string): Promise<void>;
}
