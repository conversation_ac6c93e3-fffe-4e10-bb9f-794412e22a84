'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useLicenseData } from '@/hooks/useLicenseData';
import { LicenseType } from '@/services/licenseTypeService';

const NewApplicationPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const { licenseTypes, loading: licenseLoading, getCategoriesByType } = useLicenseData();

  const [selectedLicenseType, setSelectedLicenseType] = useState<LicenseType | null>(null);
  const [selectedCategories, setSelectedCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // Get URL parameters
  const typeId = searchParams.get('type');
  const category = searchParams.get('category');

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }

    if (licenseTypes && typeId) {
      const licenseType = licenseTypes.find(lt => lt.license_type_id === typeId);
      if (licenseType) {
        setSelectedLicenseType(licenseType);
        // Get categories for this license type
        const typeCategories = getCategoriesByType(typeId);
        setSelectedCategories(typeCategories);
      }
    }
  }, [isAuthenticated, authLoading, router, licenseTypes, typeId, getCategoriesByType]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // For now, redirect to courier form as it's the only working form
      // TODO: Implement specific forms for each license type
      router.push('/customer/applications/courier/new');
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (authLoading || licenseLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={() => router.back()}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
            >
              <i className="ri-arrow-left-line text-xl"></i>
            </button>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              New License Application
            </h1>
          </div>
          
          {selectedLicenseType && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                {selectedLicenseType.name}
              </h2>
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                {selectedLicenseType.description}
              </p>
              {category && (
                <div className="mt-2">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                    Category: {category}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Application Form */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-8">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 dark:bg-yellow-900/20 mb-4">
                <i className="ri-tools-line text-yellow-600 dark:text-yellow-400 text-xl"></i>
              </div>
              
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Select License Type and Category
              </h3>

              <p className="text-gray-600 dark:text-gray-400 mb-6">
                To begin your application, please first select a license type from the main applications page,
                then choose the appropriate license category for your needs.
              </p>

              <div className="space-y-4">
                <button
                  onClick={() => router.push('/customer/applications')}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Back to License Types
                </button>

                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Please select a license type and category to begin your application.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Available Categories */}
        {selectedLicenseType && selectedCategories && selectedCategories.length > 0 && (
          <div className="mt-8 bg-white dark:bg-gray-800 shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Available Categories
              </h3>
            </div>
            <div className="p-6">
              <div className="grid gap-4 md:grid-cols-2">
                {selectedCategories.map((cat) => (
                  <div
                    key={cat.license_category_id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-primary transition-colors"
                  >
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                      {cat.name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {cat.description}
                    </p>
                    <div className="text-sm font-medium text-primary">
                      Fee: {cat.fee}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </CustomerLayout>
  );
};

export default NewApplicationPage;
