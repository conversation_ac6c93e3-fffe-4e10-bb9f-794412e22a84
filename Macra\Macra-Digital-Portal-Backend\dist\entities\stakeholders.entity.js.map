{"version": 3, "file": "stakeholders.entity.js", "sourceRoot": "", "sources": ["../../src/entities/stakeholders.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAQ8B;AAC9B,+BAAoC;AACpC,+CAAqC;AACrC,yDAAgD;AAChD,uDAA6C;AAE7C,IAAY,mBAKX;AALD,WAAY,mBAAmB;IAC7B,kCAAW,CAAA;IACX,kDAA2B,CAAA;IAC3B,0CAAmB,CAAA;IACnB,wCAAiB,CAAA;AACnB,CAAC,EALW,mBAAmB,mCAAnB,mBAAmB,QAK9B;AAGM,IAAM,WAAW,GAAjB,MAAM,WAAW;IAOtB,cAAc,CAAS;IAGvB,YAAY,CAAS;IAGrB,UAAU,CAAS;IAGnB,SAAS,CAAS;IAGlB,WAAW,CAAU;IAGrB,UAAU,CAAS;IAGnB,WAAW,CAAS;IAMpB,QAAQ,CAAsB;IAG9B,OAAO,CAAS;IAGhB,cAAc,CAAS;IAGvB,UAAU,CAAO;IAGjB,UAAU,CAAS;IAGnB,UAAU,CAAO;IAGjB,UAAU,CAAU;IAGpB,UAAU,CAAQ;IAKlB,SAAS,CAAa;IAItB,OAAO,CAAW;IAIlB,OAAO,CAAO;IAId,OAAO,CAAQ;IAGf,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG,IAAA,SAAM,GAAE,CAAC;QACjC,CAAC;IACH,CAAC;CACF,CAAA;AA7EY,kCAAW;AAOtB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,IAAI;KACb,CAAC;;mDACqB;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;iDACJ;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;+CACtB;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;8CACvB;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACpC;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;+CACN;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;;gDACpB;AAMpB;IAJC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,mBAAmB;KAC1B,CAAC;;6CAC4B;AAG9B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;4CACzB;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;mDACF;AAGvB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;+CAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;+CACN;AAGnB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;+CAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CACrB;AAGpB;IADC,IAAA,0BAAgB,GAAE;8BACN,IAAI;+CAAC;AAKlB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,6BAAU,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAC,CAAC;IACnD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;8BAC1B,6BAAU;8CAAC;AAItB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,0BAAQ,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAC,CAAC;IAClD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BAC1B,0BAAQ;4CAAC;AAIlB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;IAC/C,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BAC1B,kBAAI;4CAAC;AAId;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;IAC/D,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BACzB,kBAAI;4CAAC;AAGf;IADC,IAAA,sBAAY,GAAE;;;;6CAKd;sBA5EU,WAAW;IADvB,IAAA,gBAAM,EAAC,cAAc,CAAC;GACV,WAAW,CA6EvB"}