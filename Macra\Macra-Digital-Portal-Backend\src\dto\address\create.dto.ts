import { <PERSON>In, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

export class CreateAddressDto {
  @IsString({ message: "Address type invalid!" })
  @IsIn(['physical', 'postal'], { message: "Address type must be one of the following: physical, postal" })
  @IsNotEmpty({ message: "Address type is required!"})
  address_type: string;

  @IsString({ message: "Address origin invalid!" })
  @IsIn(['applicant', 'stakeholder', 'contact_person', 'user'], { message: "Address origin must be one of the following: applicant, stakeholder, contact_person, user" })
  @IsNotEmpty({ message: "Address origin is required!"})
  address_origin: string;

  @IsString({ message: "Address line 1 invalid!" })
  @IsNotEmpty({ message: "Address line 1 is required!"})
  address_line_1: string;

  @IsOptional()
  @IsString({ message: "Address line 2 invalid!" })
  address_line_2?: string;

  @IsOptional()
  @IsString({ message: "Address line 3 invalid!" })
  address_line_3?: string;

  @IsNotEmpty({ message: "Postal code is required!"})
  @IsString({ message: "Postal code invalid!" })
  @MinLength(6, { message: "Postal code must be at least 6 characters long!" })
  @MaxLength(9, { message: "Postal code must not exceed 9 characters!" })
  postal_code: string;

  @IsNotEmpty({ message: "Country is required!"})
  @IsString({ message: "Country invalid!" })
  @MinLength(3, { message: "Country must be at least 3 characters long!" })
  @MaxLength(50, { message: "Country must not exceed 50 characters!" })
  country: string;

  @IsNotEmpty({ message: "City is required!"})
  @IsString({ message: "City invalid!" })
  @MinLength(3, { message: "City must be at least 3 characters long!" })
  @MaxLength(50, { message: "City must not exceed 50 characters!" })
  city: string;
}
