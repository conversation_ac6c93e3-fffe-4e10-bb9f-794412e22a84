'use client';

import React, { useState } from 'react';

interface ReviewSubmitProps {
  formData: any;
  allFormData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  onSubmit: () => Promise<void>;
  errors: Record<string, string>;
  applicationId?: string;
  isLoading?: boolean;
  isSubmitting?: boolean;
}

const ReviewSubmit: React.FC<ReviewSubmitProps> = ({
  formData,
  allFormData,
  onChange,
  onSave,
  onSubmit,
  errors,
  applicationId,
  isLoading = false,
  isSubmitting = false
}) => {
  const [isReviewing, setIsReviewing] = useState(false);

  const handleSubmit = async () => {
    setIsReviewing(true);
    try {
      await onSubmit();
    } catch (error) {
      console.error('Error submitting application:', error);
    } finally {
      setIsReviewing(false);
    }
  };

  const renderSectionSummary = (title: string, data: any) => {
    if (!data || Object.keys(data).length === 0) {
      return null;
    }

    return (
      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
          {title}
        </h4>
        <div className="space-y-2">
          {Object.entries(data).map(([key, value]) => {
            if (!value || value === '' || value === false) return null;
            
            const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            
            return (
              <div key={key} className="flex flex-col sm:flex-row">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 sm:w-1/3">
                  {label}:
                </span>
                <span className="text-sm text-gray-900 dark:text-gray-100 sm:w-2/3">
                  {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value)}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Review & Submit Application
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Please review all information before submitting your application.
        </p>
        {applicationId && (
          <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <p className="text-sm text-green-700 dark:text-green-300">
              <i className="ri-check-line mr-1"></i>
              Application ID: {applicationId}
            </p>
          </div>
        )}
      </div>

      {/* Application Summary */}
      <div className="space-y-6">
        {renderSectionSummary('Applicant Information', allFormData.applicantInfo)}
        {renderSectionSummary('Company Profile', allFormData.companyProfile)}
        {renderSectionSummary('Business Information', allFormData.businessInfo)}
        {renderSectionSummary('Service Scope', allFormData.serviceScope)}
        {renderSectionSummary('Business Plan', allFormData.businessPlan)}
        {renderSectionSummary('Legal History', allFormData.legalHistory)}
      </div>

      {/* Final Declaration */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <h4 className="text-md font-medium text-blue-900 dark:text-blue-100 mb-2">
            <i className="ri-information-line mr-2"></i>
            Important Information
          </h4>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Your application will be reviewed by MACRA within 30 business days</li>
            <li>• You will receive email notifications about the status of your application</li>
            <li>• Additional documentation may be requested during the review process</li>
            <li>• Application fees are non-refundable</li>
            <li>• You can track your application status in your dashboard</li>
          </ul>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-center pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSubmit}
          disabled={isSubmitting || isReviewing || isLoading}
          className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting || isReviewing ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Submitting Application...
            </>
          ) : (
            <>
              <i className="ri-send-plane-line mr-2"></i>
              Submit Application
            </>
          )}
        </button>
      </div>

      {/* Warning */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
        <div className="flex">
          <i className="ri-alert-line text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"></i>
          <div>
            <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Before Submitting
            </h4>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
              Please ensure all information is accurate and complete. Once submitted, 
              you will not be able to modify your application without contacting MACRA support.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewSubmit;
