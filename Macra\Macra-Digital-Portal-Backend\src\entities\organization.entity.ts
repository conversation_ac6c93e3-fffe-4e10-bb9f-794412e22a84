import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
  Index,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Address } from "./address.entity";
import { Contacts } from "./contacts.entity";
import { User } from "./user.entity";


@Entity('organizations')
export class Organization {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  organization_id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Index()
  @Column({ type: 'varchar', unique: true })
  registration_number: string;

  @Column({ type: 'varchar' })
  website: string;

  @Column({ type: 'varchar', unique: true })
  email: string;

  @Column({ type: 'varchar', length: 20 })
  phone: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  fax?: string;

  @Column({ type: 'uuid', nullable: true })
  physical_address_id?: string;

  @Column({ type: 'uuid', nullable: true })
  postal_address_id?: string;

  @Column({ type: 'uuid', nullable: true })
  contact_id?: string;

  @Column({ type: 'date' })
  date_incorporation: Date;

  @Column({ type: 'varchar' })
  place_incorporation: string;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at?: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at?: Date;

  @DeleteDateColumn({ type: 'timestamp' })
  deleted_at?: Date;

  @ManyToOne(() => Address)
  @JoinColumn({ name: 'physical_address_id' })
  physical_address?: Address;

  @ManyToOne(() => Address)
  @JoinColumn({ name: 'postal_address_id' })
  postal_address?: Address;

  @ManyToOne(() => Contacts)
  @JoinColumn({ name: 'contact_id' })
  contact?: Contacts;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.organization_id) {
      this.organization_id = uuidv4();
    }
  }
}