export { User, UserStatus } from './user.entity';
export { Role, RoleName } from './role.entity';
export { Permission } from './permission.entity';
export { Address } from './address.entity';
export { IdentificationType } from './identification-type.entity';
export { UserIdentification } from './user-identification.entity';
export { Employee } from './employee.entity';
export { Applicants } from './applicant.entity';
export { AuditTrail, AuditAction, AuditModule, AuditStatus } from './audit-trail.entity';

// Contact-related entities
export { Contacts } from './contacts.entity';
export { ContactPersons } from './contact-persons.entity';
export { EmployeeRoles } from './employee-roles.entity';

// Stakeholder entities
export { Stakeholder, StakeholderPosition } from './stakeholders.entity';
export { ShareholderDetails } from './shareholder-details.entity';

// License framework entities
export { LicenseTypes } from './license-types.entity';
export { LicenseCategories } from './license-categories.entity';
export { LicenseCategoryDocument } from './license-category-document.entity';

// Application entities
export { Applications, ApplicationStatus } from './applications.entity';
export { ApplicantDisclosure } from './applicant-disclosure.entity';
export { ScopeOfService } from './scope-of-service.entity';


// Document management
export { Documents, DocumentType } from './documents.entity';

// Evaluation entities
export { Evaluations, EvaluationType, EvaluationStatus, EvaluationRecommendation } from './evaluations.entity';
export { EvaluationCriteria } from './evaluation-criteria.entity';

// License entity
export { Licenses, LicenseStatus } from './licenses.entity';

// Financial entities
export { Payments, TransactionType, PaymentStatus, PaymentMethod } from './payments.entity';
export { Invoices, InvoiceStatus } from './invoices.entity';

// Notifications
export { Notifications, NotificationType, NotificationPriority } from './notifications.entity';

// Data Protection entities
export {
  ConsumerAffairsComplaint,
  ConsumerAffairsComplaintAttachment,
  ConsumerAffairsComplaintStatusHistory,
  ComplaintCategory,
  ComplaintStatus,
  ComplaintPriority
} from '../consumer-affairs/consumer-affairs-complaint.entity';

export {
  DataBreachReport,
  DataBreachReportAttachment,
  DataBreachReportStatusHistory,
  DataBreachCategory,
  DataBreachSeverity,
  DataBreachStatus,
  DataBreachPriority
} from '../data-breach/data-breach-report.entity';
