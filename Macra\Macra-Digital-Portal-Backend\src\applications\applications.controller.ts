import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  ParseEnumPipe,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ApplicationsService } from './applications.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateApplicationDto } from '../dto/application/create-application.dto';
import { UpdateApplicationDto } from '../dto/application/update-application.dto';
import { Applications } from '../entities/applications.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('applications')
@Controller('applications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ApplicationsController {
  constructor(private readonly applicationsService: ApplicationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new application' })
  @ApiResponse({
    status: 201,
    description: 'Application created successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.CREATE,
    resourceType: 'Application',
    description: 'Created new application',
  })
  async create(
    @Body() createApplicationDto: CreateApplicationDto,
    @Request() req: any,
  ): Promise<Applications> {
    return this.applicationsService.create(createApplicationDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all applications with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Applications retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed applications list',
  })
  async findAll(@Paginate() query: PaginateQuery, @Request() req: any): Promise<PaginatedResult<Applications>> {
    // Extract user roles from the request
    const userRoles = req.user?.roles || [];
    const result = await this.applicationsService.findAll(query, userRoles);
    return PaginationTransformer.transform<Applications>(result);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get application statistics' })
  @ApiResponse({
    status: 200,
    description: 'Application statistics retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed application statistics',
  })
  async getStats(): Promise<any> {
    return this.applicationsService.getApplicationStats();
  }

  @Get('by-applicant/:applicantId')
  @ApiOperation({ summary: 'Get applications by applicant' })
  @ApiParam({ name: 'applicantId', description: 'Applicant UUID' })
  @ApiResponse({
    status: 200,
    description: 'Applications retrieved successfully',
    type: [Applications],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed applications by applicant',
  })
  async findByApplicant(@Param('applicantId', ParseUUIDPipe) applicantId: string): Promise<Applications[]> {
    return this.applicationsService.findByApplicant(applicantId);
  }

  @Get('by-status/:status')
  @ApiOperation({ summary: 'Get applications by status' })
  @ApiParam({ name: 'status', description: 'Application status' })
  @ApiResponse({
    status: 200,
    description: 'Applications retrieved successfully',
    type: [Applications],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed applications by status',
  })
  async findByStatus(
    @Param('status') status: string,
  ): Promise<Applications[]> {
    return this.applicationsService.findByStatus(status);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get application by ID' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Application retrieved successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Viewed application details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Applications> {
    return this.applicationsService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update application' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Application updated successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Updated application',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateApplicationDto: UpdateApplicationDto,
    @Request() req: any,
  ): Promise<Applications> {
    return this.applicationsService.update(id, updateApplicationDto, req.user.userId);
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update application status' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiQuery({ name: 'status', description: 'New status' })
  @ApiResponse({
    status: 200,
    description: 'Application status updated successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Updated application status',
  })
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('status') status: string,
    @Request() req: any,
  ): Promise<Applications> {
    return this.applicationsService.updateStatus(id, status, req.user.userId);
  }

  @Put(':id/progress')
  @ApiOperation({ summary: 'Update application progress' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiQuery({ name: 'currentStep', description: 'Current step (1-6)' })
  @ApiQuery({ name: 'progressPercentage', description: 'Progress percentage (0-100)' })
  @ApiResponse({
    status: 200,
    description: 'Application progress updated successfully',
    type: Applications,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Updated application progress',
  })
  async updateProgress(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('currentStep', ParseIntPipe) currentStep: number,
    @Query('progressPercentage', ParseIntPipe) progressPercentage: number,
    @Request() req: any,
  ): Promise<Applications> {
    return this.applicationsService.updateProgress(id, currentStep, progressPercentage, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete application' })
  @ApiParam({ name: 'id', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Application deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Application',
    description: 'Deleted application',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.applicationsService.remove(id);
    return { message: 'Application deleted successfully' };
  }
}
