const mysql = require('mysql2/promise');

async function fixStakeholdersTable() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'macra_db'
    });

    try {
        console.log('🔧 Final fix for stakeholders table foreign key constraints...\n');

        // Step 1: Check current table structure
        console.log('1. Checking current table structure...');
        const [columns] = await connection.execute(`
            SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'macra_db' AND TABLE_NAME = 'stakeholders'
            AND COLUMN_NAME IN ('contact_id', 'created_by', 'updated_by')
            ORDER BY ORDINAL_POSITION
        `);

        console.log('Current problematic columns:');
        columns.forEach(col => {
            const length = col.CHARACTER_MAXIMUM_LENGTH ? `(${col.CHARACTER_MAXIMUM_LENGTH})` : '';
            const nullable = col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL';
            console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE}${length} ${nullable}`);
        });

        // Step 2: Drop all existing foreign key constraints on stakeholders table
        console.log('\n2. Dropping all existing foreign key constraints...');
        
        const [existingConstraints] = await connection.execute(`
            SELECT CONSTRAINT_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'macra_db' 
                AND TABLE_NAME = 'stakeholders'
                AND REFERENCED_TABLE_NAME IS NOT NULL
        `);

        for (const constraint of existingConstraints) {
            try {
                console.log(`Dropping constraint: ${constraint.CONSTRAINT_NAME}`);
                await connection.execute(`
                    ALTER TABLE stakeholders 
                    DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}
                `);
                console.log(`✅ Dropped ${constraint.CONSTRAINT_NAME}`);
            } catch (error) {
                console.log(`⚠️ Could not drop ${constraint.CONSTRAINT_NAME}: ${error.message}`);
            }
        }

        // Step 3: Fix column data types
        console.log('\n3. Fixing column data types...');
        
        console.log('Updating contact_id column to varchar(36)...');
        await connection.execute(`
            ALTER TABLE stakeholders 
            MODIFY COLUMN contact_id varchar(36) NULL
        `);
        console.log('✅ contact_id updated');

        console.log('Updating created_by column to varchar(36)...');
        await connection.execute(`
            ALTER TABLE stakeholders 
            MODIFY COLUMN created_by varchar(36) NULL
        `);
        console.log('✅ created_by updated');

        console.log('Updating updated_by column to varchar(36)...');
        await connection.execute(`
            ALTER TABLE stakeholders 
            MODIFY COLUMN updated_by varchar(36) NULL
        `);
        console.log('✅ updated_by updated');

        // Step 4: Add foreign key constraints back
        console.log('\n4. Adding foreign key constraints...');

        try {
            console.log('Adding contact_id foreign key constraint...');
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT FK_75516ad5098e0aada3ffe364bf2 
                FOREIGN KEY (contact_id) REFERENCES contacts(contact_id) 
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('✅ Contact foreign key constraint added');
        } catch (error) {
            console.log(`⚠️ Error adding contact constraint: ${error.message}`);
        }

        try {
            console.log('Adding created_by foreign key constraint...');
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT FK_5525cda345b76b7633dba45bc3d 
                FOREIGN KEY (created_by) REFERENCES users(user_id) 
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('✅ Created_by foreign key constraint added');
        } catch (error) {
            console.log(`⚠️ Error adding created_by constraint: ${error.message}`);
        }

        try {
            console.log('Adding updated_by foreign key constraint...');
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT FK_5518ae16ebb22019f47827a3127 
                FOREIGN KEY (updated_by) REFERENCES users(user_id) 
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('✅ Updated_by foreign key constraint added');
        } catch (error) {
            console.log(`⚠️ Error adding updated_by constraint: ${error.message}`);
        }

        // Step 5: Verify the fix
        console.log('\n5. Verifying the fix...');
        
        const [finalColumns] = await connection.execute(`
            SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'macra_db' AND TABLE_NAME = 'stakeholders'
            AND COLUMN_NAME IN ('contact_id', 'created_by', 'updated_by')
            ORDER BY ORDINAL_POSITION
        `);

        console.log('Final column types:');
        finalColumns.forEach(col => {
            const length = col.CHARACTER_MAXIMUM_LENGTH ? `(${col.CHARACTER_MAXIMUM_LENGTH})` : '';
            const nullable = col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL';
            console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE}${length} ${nullable}`);
        });

        const [finalConstraints] = await connection.execute(`
            SELECT 
                kcu.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME,
                rc.DELETE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
                ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
                AND kcu.CONSTRAINT_SCHEMA = rc.CONSTRAINT_SCHEMA
            WHERE kcu.TABLE_SCHEMA = 'macra_db' 
                AND kcu.TABLE_NAME = 'stakeholders'
                AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        `);

        console.log('\nFinal foreign key constraints:');
        finalConstraints.forEach(constraint => {
            console.log(`✅ ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME} (DELETE: ${constraint.DELETE_RULE})`);
        });

        console.log('\n🎉 Stakeholders table foreign key constraints fixed successfully!');

    } catch (error) {
        console.error('❌ Error:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        await connection.end();
    }
}

fixStakeholdersTable();
