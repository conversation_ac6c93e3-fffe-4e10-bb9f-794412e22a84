'use client';

import React, { useState, useEffect, useCallback } from 'react';
import TextInput from '@/components/common/TextInput';
import Select from '@/components/common/Select';
import { validateSection } from '@/utils/formValidation';

interface CompanyProfileProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  errors: Record<string, string>;
  applicationId?: string;
  isLoading?: boolean;
}

const CompanyProfile: React.FC<CompanyProfileProps> = ({
  formData,
  onChange,
  onSave,
  errors,
  applicationId,
  isLoading = false
}) => {
  const [localData, setLocalData] = useState({
    company_name: '',
    business_registration_number: '',
    tax_number: '',
    company_type: '',
    incorporation_date: '',
    incorporation_place: '',
    company_email: '',
    company_phone: '',
    company_address: '',
    company_city: '',
    company_district: '',
    website: '',
    number_of_employees: '',
    annual_revenue: '',
    business_description: '',
    ...formData
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Sync formData to localData only when formData changes and is different
  useEffect(() => {
    if (formData && Object.keys(formData).length > 0) {
      setLocalData((prev: any) => {
        const newData = { ...prev, ...formData };
        // Only update if there are actual changes to prevent infinite loops
        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);
        return hasChanges ? newData : prev;
      });
    }
  }, [formData]);

  const handleLocalChange = useCallback((field: string, value: any) => {
    setLocalData((prev: any) => ({ ...prev, [field]: value }));

    // Call onChange with the field and value
    if (onChange) {
      onChange(field, value);
    }

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }
  }, [onChange, validationErrors]);

  const validateForm = () => {
    const validation = validateSection(localData, 'companyProfile');
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      await onSave(localData);
      console.log('Company profile saved');
    } catch (error) {
      console.error('Error saving company profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleBlur = (field: string) => {
    if (localData[field] && !validationErrors[field]) {
      onChange(field, localData[field]);
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Company Profile
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide your company registration and business details.
        </p>
      </div>

      {/* Basic Company Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <TextInput
            label="Company Name"
            value={localData.company_name || ''}
            onChange={(e) => handleLocalChange('company_name', e.target.value)}
            onBlur={() => handleBlur('company_name')}
            required
            error={validationErrors.company_name || errors.company_name}
          />
        </div>

        <TextInput
          label="Business Registration Number"
          value={localData.business_registration_number || ''}
          onChange={(e) => handleLocalChange('business_registration_number', e.target.value)}
          onBlur={() => handleBlur('business_registration_number')}
          placeholder="**********"
          required
          error={validationErrors.business_registration_number || errors.business_registration_number}
        />

        <TextInput
          label="Tax Number (TPIN)"
          value={localData.tax_number || ''}
          onChange={(e) => handleLocalChange('tax_number', e.target.value)}
          onBlur={() => handleBlur('tax_number')}
          placeholder="TP123456789"
          required
          error={validationErrors.tax_number || errors.tax_number}
        />

        <Select
          label="Company Type"
          value={localData.company_type || ''}
          onChange={(value) => handleLocalChange('company_type', value)}
          options={[
            { value: 'private_limited', label: 'Private Limited Company' },
            { value: 'public_limited', label: 'Public Limited Company' },
            { value: 'partnership', label: 'Partnership' },
            { value: 'sole_proprietorship', label: 'Sole Proprietorship' },
            { value: 'ngo', label: 'Non-Governmental Organization' },
            { value: 'other', label: 'Other' }
          ]}
          required
          error={validationErrors.company_type || errors.company_type}
        />

        <TextInput
          label="Date of Incorporation"
          type="date"
          value={localData.incorporation_date || ''}
          onChange={(e) => handleLocalChange('incorporation_date', e.target.value)}
          onBlur={() => handleBlur('incorporation_date')}
          required
          error={validationErrors.incorporation_date || errors.incorporation_date}
        />

        <TextInput
          label="Place of Incorporation"
          value={localData.incorporation_place || ''}
          onChange={(e) => handleLocalChange('incorporation_place', e.target.value)}
          onBlur={() => handleBlur('incorporation_place')}
          required
          error={validationErrors.incorporation_place || errors.incorporation_place}
        />
      </div>

      {/* Contact Information */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Company Contact Information
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TextInput
            label="Company Email"
            type="email"
            value={localData.company_email || ''}
            onChange={(e) => handleLocalChange('company_email', e.target.value)}
            onBlur={() => handleBlur('company_email')}
            required
            error={validationErrors.company_email || errors.company_email}
          />

          <TextInput
            label="Company Phone"
            value={localData.company_phone || ''}
            onChange={(e) => handleLocalChange('company_phone', e.target.value)}
            onBlur={() => handleBlur('company_phone')}
            placeholder="+265 1 234 567"
            required
            error={validationErrors.company_phone || errors.company_phone}
          />

          <TextInput
            label="Website"
            value={localData.website || ''}
            onChange={(e) => handleLocalChange('website', e.target.value)}
            onBlur={() => handleBlur('website')}
            placeholder="https://www.company.com"
            error={validationErrors.website || errors.website}
          />

          <div className="md:col-span-2">
            <TextInput
              label="Company Address"
              value={localData.company_address || ''}
              onChange={(e) => handleLocalChange('company_address', e.target.value)}
              onBlur={() => handleBlur('company_address')}
              required
              error={validationErrors.company_address || errors.company_address}
            />
          </div>

          <TextInput
            label="City"
            value={localData.company_city || ''}
            onChange={(e) => handleLocalChange('company_city', e.target.value)}
            onBlur={() => handleBlur('company_city')}
            required
            error={validationErrors.company_city || errors.company_city}
          />

          <Select
            label="District"
            value={localData.company_district || ''}
            onChange={(value) => handleLocalChange('company_district', value)}
            options={[
              { value: 'Blantyre', label: 'Blantyre' },
              { value: 'Lilongwe', label: 'Lilongwe' },
              { value: 'Mzuzu', label: 'Mzuzu' },
              { value: 'Zomba', label: 'Zomba' },
              { value: 'Other', label: 'Other' }
            ]}
            required
            error={validationErrors.company_district || errors.company_district}
          />
        </div>
      </div>

      {/* Business Details */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Business Details
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Number of Employees"
            value={localData.number_of_employees || ''}
            onChange={(value) => handleLocalChange('number_of_employees', value)}
            options={[
              { value: '1-10', label: '1-10 employees' },
              { value: '11-50', label: '11-50 employees' },
              { value: '51-100', label: '51-100 employees' },
              { value: '101-500', label: '101-500 employees' },
              { value: '500+', label: '500+ employees' }
            ]}
            required
            error={validationErrors.number_of_employees || errors.number_of_employees}
          />

          <Select
            label="Annual Revenue (MWK)"
            value={localData.annual_revenue || ''}
            onChange={(value) => handleLocalChange('annual_revenue', value)}
            options={[
              { value: 'under_1m', label: 'Under 1 Million' },
              { value: '1m_10m', label: '1-10 Million' },
              { value: '10m_50m', label: '10-50 Million' },
              { value: '50m_100m', label: '50-100 Million' },
              { value: 'over_100m', label: 'Over 100 Million' }
            ]}
            required
            error={validationErrors.annual_revenue || errors.annual_revenue}
          />

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Business Description *
            </label>
            <textarea
              value={localData.business_description || ''}
              onChange={(e) => handleLocalChange('business_description', e.target.value)}
              onBlur={() => handleBlur('business_description')}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe your business activities and operations..."
            />
            {(validationErrors.business_description || errors.business_description) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.business_description || errors.business_description}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving || isLoading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Company Profile
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default CompanyProfile;
