import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LoginDto } from '../dto/auth/login.dto';
import { RegisterDto } from '../dto/auth/register.dto';
import { ForgotPasswordDto, ResetPasswordDto } from '../dto/auth/forgot-password.dto';
import { TwoFactorDto, RequestTwoFactorDto } from '../dto/auth/two-factor.dto';
import { Throttle } from '@nestjs/throttler';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'User login' })
  @ApiBody({ type: LoginDto })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    schema: {
      example: {
        access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        user: {
          user_id: 'uuid',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe',
          roles: ['customer']
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @Post('register')
  @ApiOperation({ summary: 'User registration' })
  @ApiBody({ type: RegisterDto })
  @ApiResponse({
    status: 201,
    description: 'Registration successful',
    schema: {
      example: {
        access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        user: {
          user_id: 'uuid',
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe',
          roles: ['customer']
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'User already exists' })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('forgot-password')
  @Throttle({ default: { limit: 5, ttl: 60000 } })
  @ApiOperation({ summary: 'Request password reset' })
  @ApiBody({ type: ForgotPasswordDto })
  @ApiResponse({
    status: 200,
    description: 'Password reset email sent',
    schema: {
      example: {
        message: 'If the email exists, a password reset link has been sent.'
      }
    }
  })
  @HttpCode(HttpStatus.OK)
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @Post('verify-email')
  @HttpCode(HttpStatus.OK)
  async verifyEmail(@Body() twoFactorDto: TwoFactorDto) {
    return this.authService.verifyTwoFactorCode(twoFactorDto);
  }

  @Post('setup-2fa')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  async setupTwoFactorAuth(@Body() setup2FA: RequestTwoFactorDto) {
    return this.authService.setupTwoFactorAuth(setup2FA);
  }


  @Post('verify-2fa')
  @HttpCode(200)
  @ApiBearerAuth('JWT-auth')
  async verifyTwoFactorCode(@Body() twoFactorDto: TwoFactorDto) {
    return this.authService.verifyTwoFactorCode(twoFactorDto);
  }

  @Post('refresh')
  @UseGuards(JwtAuthGuard)
  async refresh(@Request() req) {
    const user = await this.authService.validateJwtPayload({
      email: req.user.email,
      sub: req.user.userId,
      roles: req.user.roles,
    });

    // Generate new token without password validation
    const payload = {
      email: user?.email,
      sub: user?.user_id,
      roles: user?.roles,
    };

    return {
      access_token: this.authService['jwtService'].sign(payload),
      user: {
        user_id: user?.user_id,
        email: user?.email,
        first_name: user?.first_name,
        last_name: user?.last_name,
        roles: user?.roles,
      },
    };
  }
}
