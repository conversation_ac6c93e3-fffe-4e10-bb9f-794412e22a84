import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applicants } from './applicant.entity';
import { Contacts } from './contacts.entity';

export enum StakeholderPosition {
  CEO = 'CEO',
  SHAREHOLDER = 'Shareholder',
  AUDITOR = 'Auditor',
  LAWYER = 'Lawyer',
}

@Entity('stakeholders')
export class Stakeholders {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  stakeholder_id: string;

  @Column({ type: 'uuid' })
  applicant_id: string;

  @Column({ type: 'varchar', length: 100 })
  first_name: string;

  @Column({ type: 'varchar', length: 100 })
  last_name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  middle_name?: string;

  @Column({ type: 'uuid' })
  contact_id: string;

  @Column({ type: 'varchar', length: 50 })
  nationality: string;

  @Column({
    type: 'enum',
    enum: StakeholderPosition,
  })
  position: StakeholderPosition;

  @Column({ type: 'varchar', length: 300 })
  profile: string;

  @Column({ type: 'uuid' })
  cv_document_id: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applicants)
  @JoinColumn({ name: 'applicant_id' })
  applicant: Applicants;

  @ManyToOne(() => Contacts)
  @JoinColumn({ name: 'contact_id' })
  contact: Contacts;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.stakeholder_id) {
      this.stakeholder_id = uuidv4();
    }
  }
}
