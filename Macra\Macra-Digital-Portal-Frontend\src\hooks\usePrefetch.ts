'use client';

import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

interface PrefetchOptions {
  priority?: boolean;
  preload?: boolean;
}

export const usePrefetch = () => {
  const router = useRouter();

  const prefetchPage = useCallback((href: string, options: PrefetchOptions = {}) => {
    try {
      // Use the options parameter to avoid unused variable warning
      const prefetchKind = options.priority ? 'full' : 'auto';
      router.prefetch(href);
      
      // Log prefetch activity in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔗 Prefetching page: ${href} (${prefetchKind})`);
      }
    } catch (error) {
      console.warn('Failed to prefetch:', href, error);
    }
  }, [router]);

  const prefetchCustomerPages = useCallback(() => {
    // Prefetch commonly visited customer pages
    const customerPages = [
      '/customer',
      '/customer/applications',
      '/customer/payments',
      '/customer/licenses',
      '/customer/procurement',
      '/customer/profile',
      '/customer/consumer-affairs'
    ];

    customerPages.forEach(page => {
      prefetchPage(page, { priority: false });
    });
  }, [prefetchPage]);

  const prefetchAdminPages = useCallback(() => {
    // Prefetch commonly visited admin pages
    const adminPages = [
      '/dashboard',
      '/users',
      '/roles',
      '/permissions',
      '/audit-trail'
    ];

    adminPages.forEach(page => {
      prefetchPage(page, { priority: false });
    });
  }, [prefetchPage]);

  const prefetchAuthPages = useCallback(() => {
    // Prefetch authentication related pages
    const authPages = [
      '/auth/login',
      '/auth/signup',
      '/auth/forgot-password'
    ];

    authPages.forEach(page => {
      prefetchPage(page, { priority: true });
    });
  }, [prefetchPage]);

  return {
    prefetchPage,
    prefetchCustomerPages,
    prefetchAdminPages,
    prefetchAuthPages
  };
};

export default usePrefetch;