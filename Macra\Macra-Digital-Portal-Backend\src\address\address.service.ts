import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateAddressDto } from '../dto/address/create.dto';
import { UpdateAddressDto } from '../dto/address/update.dto';
import { Address } from '../entities/address.entity';
import { DataSource, Repository } from 'typeorm';

@Injectable()
export class AddressService {
  constructor(
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
    private readonly dataSource: DataSource,
  ) { }

  /**
   * Create a new address entry
   * @param createAddressDto Address DTO
   * @param createdBy User ID
   * @returns Address Object
   */
  async createAddress(
    createAddressDto: CreateAddressDto,
    createdBy: string,
  ): Promise<Address> {
    const { address_origin, address_type, address_line_1 } = createAddressDto;

    return this.dataSource.transaction(async (manager) => {
      const repo = manager.getRepository(Address);

      const existing = await repo
        .createQueryBuilder('address')
        .setLock('pessimistic_read')
        .where('address.address_origin = :address_origin', { address_origin })
        .andWhere('address.address_type = :address_type', { address_type })
        .andWhere('address.address_line_1 = :address_line_1', { address_line_1 })
        .andWhere('address.created_by = :created_by', { created_by: createdBy })
        .getOne();

      if (existing) {
        throw new ConflictException('Address already exists');
      }

      const address = repo.create({
        ...createAddressDto,
        created_by: createdBy,
      });

      try {
        return await repo.save(address);
      } catch (error) {
        // Optional: stricter check if not already covered by DB constraint
        if (error.code === '23505') {
          throw new ConflictException('Duplicate address not allowed');
        }
        throw error;
      }
    });
  }

  /**
   * Edit an existing address
   * @param updateAddressDto Update Address DTO
   * @param updatedBy User ID
   * @returns Address Object
   */
  async editAddress(
    updateAddressDto: UpdateAddressDto,
    updatedBy: string,
  ): Promise<Address> {
    const { address_id } = updateAddressDto;

    const address = await this.addressRepository.findOne({
      where: { address_id },
    });

    if (!address) {
      throw new NotFoundException('Address not found!');
    }

    Object.assign(address, updateAddressDto, { updated_by: updatedBy });

    return this.addressRepository.save(address);
  }

  /**
 * Get all addresses (optionally filtered by origin or type)
 */
  async findAll(filter?: { origin?: string; type?: string }): Promise<Address[]> {
    const query = this.addressRepository.createQueryBuilder('address');

    if (filter?.origin) {
      query.andWhere('address.address_origin = :origin', { origin: filter.origin });
    }

    if (filter?.type) {
      query.andWhere('address.address_type = :type', { type: filter.type });
    }

    return query.getMany();
  }

  /**
   * Get a single address by ID
   */
  async findOneById(id: string): Promise<Address> {
    const address = await this.addressRepository.findOne({
      where: { address_id: id },
    });

    if (!address) {
      throw new NotFoundException(`Address with ID ${id} not found`);
    }

    return address;
  }

  /**
   * Soft delete an address by ID
   */
  async softDelete(id: string, deletedBy: string): Promise<void> {
    const address = await this.findOneById(id);

    address.updated_by = deletedBy;
    await this.addressRepository.save(address);
    await this.addressRepository.softDelete(id);
  }

  /**
   * Restore a soft-deleted address
   */
  async restore(id: string): Promise<void> {
    const address = await this.addressRepository
      .createQueryBuilder('address')
      .withDeleted()
      .where('address.address_id = :id', { id })
      .getOne();

    if (!address) {
      throw new NotFoundException(`Deleted address with ID ${id} not found`);
    }

    await this.addressRepository.restore(id);
  }

  /**
   * Permanently remove an address
   * Use with caution!
   */
  async hardDelete(id: string): Promise<void> {
    const address = await this.findOneById(id);
    await this.addressRepository.remove(address);
  }

}
