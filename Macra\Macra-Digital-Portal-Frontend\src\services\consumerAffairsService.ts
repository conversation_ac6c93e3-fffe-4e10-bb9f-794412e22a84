import { apiClient } from '@/lib/apiClient';
import { documentService } from './documentService';

export interface ConsumerAffairsComplaint {
  complaint_id: string;
  complaint_number: string;
  complainant_id: string;
  title: string;
  description: string;
  category: string;
  status: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assigned_to?: string;
  resolution?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  complainant?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  attachments?: {
    attachment_id: string;
    file_name: string;
    file_type: string;
    file_size: number;
    uploaded_at: string;
  }[];
  status_history?: {
    history_id: string;
    status: string;
    comment?: string;
    created_at: string;
    creator: {
      user_id: string;
      first_name: string;
      last_name: string;
    };
  }[];
}

export interface CreateConsumerAffairsComplaintData {
  title: string;
  description: string;
  category: string;
  priority?: string;
  attachments?: File[];
}

export interface UpdateConsumerAffairsComplaintData {
  title?: string;
  description?: string;
  category?: string;
  status?: string;
  priority?: string;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
}

export interface ConsumerAffairsComplaintFilter {
  category?: string;
  status?: string;
  priority?: string;
  complainant_id?: string;
  assigned_to?: string;
  from_date?: string;
  to_date?: string;
  search?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

export interface ConsumerAffairsComplaintResponse {
  success: boolean;
  message: string;
  data?: ConsumerAffairsComplaint;
  total?: number;
  page?: number;
  limit?: number;
}

export interface ConsumerAffairsComplaintListResponse {
  success: boolean;
  message: string;
  data: ConsumerAffairsComplaint[];
  total: number;
  page: number;
  limit: number;
}

class ConsumerAffairsService {
  private readonly baseUrl = '/consumer-affairs-complaints';

  async createComplaint(data: CreateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaintResponse> {
    try {
      console.log('🔄 Creating consumer affairs complaint:', { ...data, attachments: data.attachments?.length || 0 });

      const formData = new FormData();
      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('category', data.category);
      
      if (data.priority) {
        formData.append('priority', data.priority);
      }

      // Add attachments if any
      if (data.attachments && data.attachments.length > 0) {
        data.attachments.forEach((file, index) => {
          formData.append('attachments', file);
        });
      }

      const response = await apiClient.post(this.baseUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Consumer affairs complaint created successfully:', response.data);

      // Upload documents to polymorphic document table if complaint was created successfully
      if (response.data.success && data.attachments && data.attachments.length > 0) {
        try {
          const complaintId = response.data.data.complaint_id;
          await documentService.uploadMultipleDocuments(
            data.attachments,
            'consumer_affairs_complaint',
            complaintId,
            'COMPLAINT_ATTACHMENT',
            false
          );
          console.log('✅ Complaint attachments uploaded to document table');
        } catch (docError) {
          console.warn('⚠️ Failed to upload documents to document table:', docError);
          // Don't fail the complaint creation if document upload fails
        }
      }

      return response.data;
    } catch (error) {
      console.error('❌ Error creating consumer affairs complaint:', error);
      throw error;
    }
  }

  async getComplaints(filter: ConsumerAffairsComplaintFilter = {}): Promise<ConsumerAffairsComplaintListResponse> {
    try {
      console.log('🔄 Fetching consumer affairs complaints with filter:', filter);

      const params = new URLSearchParams();
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`${this.baseUrl}?${params.toString()}`);

      console.log('✅ Consumer affairs complaints fetched successfully:', {
        total: response.data.total,
        count: response.data.data?.length || 0
      });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching consumer affairs complaints:', error);
      throw error;
    }
  }

  async getComplaint(complaintId: string): Promise<ConsumerAffairsComplaintResponse> {
    try {
      console.log('🔄 Fetching consumer affairs complaint:', complaintId);

      const response = await apiClient.get(`${this.baseUrl}/${complaintId}`);

      console.log('✅ Consumer affairs complaint fetched successfully:', response.data.data?.complaint_number);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching consumer affairs complaint:', error);
      throw error;
    }
  }

  async updateComplaint(
    complaintId: string, 
    data: UpdateConsumerAffairsComplaintData
  ): Promise<ConsumerAffairsComplaintResponse> {
    try {
      console.log('🔄 Updating consumer affairs complaint:', complaintId, data);

      const response = await apiClient.put(`${this.baseUrl}/${complaintId}`, data);

      console.log('✅ Consumer affairs complaint updated successfully:', response.data.data?.complaint_number);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating consumer affairs complaint:', error);
      throw error;
    }
  }

  async deleteComplaint(complaintId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 Deleting consumer affairs complaint:', complaintId);

      const response = await apiClient.delete(`${this.baseUrl}/${complaintId}`);

      console.log('✅ Consumer affairs complaint deleted successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error deleting consumer affairs complaint:', error);
      throw error;
    }
  }

  async updateComplaintStatus(
    complaintId: string, 
    status: string, 
    comment?: string
  ): Promise<ConsumerAffairsComplaintResponse> {
    try {
      console.log('🔄 Updating consumer affairs complaint status:', complaintId, status);

      const response = await apiClient.put(`${this.baseUrl}/${complaintId}/status`, {
        status,
        comment
      });

      console.log('✅ Consumer affairs complaint status updated successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error updating consumer affairs complaint status:', error);
      throw error;
    }
  }

  async assignComplaint(complaintId: string, assignedTo: string): Promise<ConsumerAffairsComplaintResponse> {
    try {
      console.log('🔄 Assigning consumer affairs complaint:', complaintId, assignedTo);

      const response = await apiClient.put(`${this.baseUrl}/${complaintId}/assign`, {
        assigned_to: assignedTo
      });

      console.log('✅ Consumer affairs complaint assigned successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error assigning consumer affairs complaint:', error);
      throw error;
    }
  }

  async addAttachments(complaintId: string, files: File[]): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 Adding attachments to consumer affairs complaint:', complaintId, files.length);

      const formData = new FormData();
      files.forEach((file) => {
        formData.append('files', file);
      });

      const response = await apiClient.post(`${this.baseUrl}/${complaintId}/attachments`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Attachments added successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error adding attachments:', error);
      throw error;
    }
  }

  async deleteAttachment(complaintId: string, attachmentId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 Deleting attachment:', complaintId, attachmentId);

      const response = await apiClient.delete(`${this.baseUrl}/${complaintId}/attachments/${attachmentId}`);

      console.log('✅ Attachment deleted successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error deleting attachment:', error);
      throw error;
    }
  }

  async getStatsSummary(): Promise<{
    success: boolean;
    message: string;
    data: {
      total: number;
      by_status: Record<string, number>;
      by_category: Record<string, number>;
      by_priority: Record<string, number>;
    };
  }> {
    try {
      console.log('🔄 Fetching consumer affairs statistics summary');

      const response = await apiClient.get(`${this.baseUrl}/stats/summary`);

      console.log('✅ Statistics summary fetched successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching statistics summary:', error);
      throw error;
    }
  }

  // Helper method to get complaint categories
  getComplaintCategories(): string[] {
    return [
      'Billing & Charges',
      'Service Quality',
      'Network Issues',
      'Customer Service',
      'Contract Disputes',
      'Accessibility',
      'Fraud & Scams',
      'Other'
    ];
  }

  // Helper method to get complaint statuses
  getComplaintStatuses(): string[] {
    return [
      'submitted',
      'under_review',
      'investigating',
      'resolved',
      'closed'
    ];
  }

  // Helper method to get priority levels
  getPriorityLevels(): string[] {
    return [
      'low',
      'medium',
      'high',
      'urgent'
    ];
  }
}

export const consumerAffairsService = new ConsumerAffairsService();
