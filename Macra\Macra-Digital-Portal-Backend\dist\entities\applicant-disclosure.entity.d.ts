import { User } from './user.entity';
import { Applicants } from './applicant.entity';
export declare class ApplicantDisclosure {
    applicant_disclosure_id: string;
    applicant_id: string;
    censured?: string;
    disciplined?: string;
    penalized?: string;
    suspended?: string;
    prosecuted?: string;
    convicted_warned_conduct?: string;
    investigated_subjected?: string;
    failed_debt_issued?: string;
    litigation?: string;
    adjudged_insolvent?: string;
    creditor_compromise?: string;
    liquidator_receiver_property_judicial_manager?: string;
    voluntary_winding_up?: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    applicant: Applicants;
    creator: User;
    updater?: User;
    generateId(): void;
}
