"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsumerAffairsComplaintController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const consumer_affairs_complaint_service_1 = require("./consumer-affairs-complaint.service");
const consumer_affairs_complaint_dto_1 = require("./consumer-affairs-complaint.dto");
let ConsumerAffairsComplaintController = class ConsumerAffairsComplaintController {
    complaintService;
    constructor(complaintService) {
        this.complaintService = complaintService;
    }
    async create(createDto, files, req) {
        const complaint = await this.complaintService.create(createDto, req.user.user_id);
        if (files && files.length > 0) {
        }
        return {
            success: true,
            message: 'Consumer affairs complaint submitted successfully',
            data: complaint,
        };
    }
    async findAll(filterDto, req) {
        const result = await this.complaintService.findAll(filterDto, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Consumer affairs complaints retrieved successfully',
            ...result,
        };
    }
    async findOne(id, req) {
        const complaint = await this.complaintService.findOne(id, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Consumer affairs complaint retrieved successfully',
            data: complaint,
        };
    }
    async update(id, updateDto, req) {
        const complaint = await this.complaintService.update(id, updateDto, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Consumer affairs complaint updated successfully',
            data: complaint,
        };
    }
    async delete(id, req) {
        await this.complaintService.delete(id, req.user.user_id, req.user.isStaff || false);
        return {
            success: true,
            message: 'Consumer affairs complaint deleted successfully',
        };
    }
    async updateStatus(id, statusDto, req) {
        const complaint = await this.complaintService.updateStatus(id, statusDto, req.user.user_id);
        return {
            success: true,
            message: 'Complaint status updated successfully',
            data: complaint,
        };
    }
    async assignComplaint(id, assignedTo, req) {
        const complaint = await this.complaintService.update(id, { assigned_to: assignedTo }, req.user.user_id, true);
        return {
            success: true,
            message: 'Complaint assigned successfully',
            data: complaint,
        };
    }
    async getStatsSummary(req) {
        return {
            success: true,
            message: 'Statistics retrieved successfully',
            data: {
                total: 0,
                by_status: {},
                by_category: {},
                by_priority: {},
            },
        };
    }
    async exportToCsv(filterDto, req) {
        return {
            success: true,
            message: 'Export functionality not yet implemented',
        };
    }
    async addAttachments(id, files, req) {
        return {
            success: true,
            message: 'File upload functionality not yet implemented',
        };
    }
    async deleteAttachment(id, attachmentId, req) {
        return {
            success: true,
            message: 'Attachment deletion functionality not yet implemented',
        };
    }
};
exports.ConsumerAffairsComplaintController = ConsumerAffairsComplaintController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('attachments', 5)),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(1, (0, common_1.UploadedFiles)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [consumer_affairs_complaint_dto_1.CreateConsumerAffairsComplaintDto, Array, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [consumer_affairs_complaint_dto_1.ConsumerAffairsComplaintFilterDto, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, consumer_affairs_complaint_dto_1.UpdateConsumerAffairsComplaintDto, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "delete", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, consumer_affairs_complaint_dto_1.UpdateConsumerAffairsComplaintStatusDto, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.Put)(':id/assign'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('assigned_to', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "assignComplaint", null);
__decorate([
    (0, common_1.Get)('stats/summary'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "getStatsSummary", null);
__decorate([
    (0, common_1.Get)('export/csv'),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [consumer_affairs_complaint_dto_1.ConsumerAffairsComplaintFilterDto, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "exportToCsv", null);
__decorate([
    (0, common_1.Post)(':id/attachments'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('files', 5)),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.UploadedFiles)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "addAttachments", null);
__decorate([
    (0, common_1.Delete)(':id/attachments/:attachmentId'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('attachmentId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ConsumerAffairsComplaintController.prototype, "deleteAttachment", null);
exports.ConsumerAffairsComplaintController = ConsumerAffairsComplaintController = __decorate([
    (0, common_1.Controller)('consumer-affairs-complaints'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [consumer_affairs_complaint_service_1.ConsumerAffairsComplaintService])
], ConsumerAffairsComplaintController);
//# sourceMappingURL=consumer-affairs-complaint.controller.js.map