Stack trace:
Frame         Function      Args
0007FFFF9B80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8A80) msys-2.0.dll+0x2118E
0007FFFF9B80  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9B80  0002100469F2 (00021028DF99, 0007FFFF9A38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9B80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9B80  00021006A545 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA0A7E0000 ntdll.dll
7FFA087E0000 KERNEL32.DLL
7FFA07930000 KERNELBASE.dll
7FFA09A30000 USER32.dll
7FFA08550000 win32u.dll
7FFA088B0000 GDI32.dll
7FFA08410000 gdi32full.dll
7FFA07F60000 msvcp_win.dll
7FFA082C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA096A0000 advapi32.dll
7FFA0A610000 msvcrt.dll
7FFA09980000 sechost.dll
7FFA09580000 RPCRT4.dll
7FFA06F30000 CRYPTBASE.DLL
7FFA08010000 bcryptPrimitives.dll
7FFA08580000 IMM32.DLL
