'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { validateSection } from '@/utils/formValidation';

interface ServiceScopeProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  errors: Record<string, string>;
  applicationId?: string;
  isLoading?: boolean;
}

const ServiceScope: React.FC<ServiceScopeProps> = ({
  formData,
  onChange,
  onSave,
  errors,
  applicationId,
  isLoading = false
}) => {
  const [localData, setLocalData] = useState({
    services_offered: '',
    geographic_coverage: '',
    service_categories: '',
    target_customers: '',
    service_capacity: '',
    ...formData
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Sync formData to localData only when formData changes and is different
  useEffect(() => {
    if (formData && Object.keys(formData).length > 0) {
      setLocalData((prev: any) => {
        const newData = { ...prev, ...formData };
        // Only update if there are actual changes to prevent infinite loops
        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);
        return hasChanges ? newData : prev;
      });
    }
  }, [formData]);

  const handleLocalChange = useCallback((field: string, value: any) => {
    setLocalData((prev: any) => ({ ...prev, [field]: value }));

    // Call onChange with the field and value
    if (onChange) {
      onChange(field, value);
    }

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }
  }, [onChange, validationErrors]);

  const validateForm = () => {
    const validation = validateSection(localData, 'serviceScope');
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      await onSave(localData);
      console.log('Service scope saved');
    } catch (error) {
      console.error('Error saving service scope:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Service Scope
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Define the scope of services you plan to offer and your coverage area.
        </p>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Services Offered *
          </label>
          <textarea
            value={localData.services_offered || ''}
            onChange={(e) => handleLocalChange('services_offered', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe the services you plan to offer..."
          />
          {(validationErrors.services_offered || errors.services_offered) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.services_offered || errors.services_offered}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Geographic Coverage *
          </label>
          <textarea
            value={localData.geographic_coverage || ''}
            onChange={(e) => handleLocalChange('geographic_coverage', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe the geographic areas you plan to serve..."
          />
          {(validationErrors.geographic_coverage || errors.geographic_coverage) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.geographic_coverage || errors.geographic_coverage}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Service Categories *
          </label>
          <textarea
            value={localData.service_categories || ''}
            onChange={(e) => handleLocalChange('service_categories', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="List and categorize your services..."
          />
          {(validationErrors.service_categories || errors.service_categories) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.service_categories || errors.service_categories}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Target Customers *
          </label>
          <textarea
            value={localData.target_customers || ''}
            onChange={(e) => handleLocalChange('target_customers', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your target customer segments..."
          />
          {(validationErrors.target_customers || errors.target_customers) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.target_customers || errors.target_customers}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Service Capacity *
          </label>
          <textarea
            value={localData.service_capacity || ''}
            onChange={(e) => handleLocalChange('service_capacity', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your capacity to deliver services..."
          />
          {(validationErrors.service_capacity || errors.service_capacity) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.service_capacity || errors.service_capacity}
            </p>
          )}
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving || isLoading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Service Scope
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ServiceScope;
