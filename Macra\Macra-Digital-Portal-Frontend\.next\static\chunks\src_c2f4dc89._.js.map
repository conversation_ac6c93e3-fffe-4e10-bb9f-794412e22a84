{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/documentService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\n\nexport interface Document {\n  document_id: string;\n  application_id?: string;\n  document_type: string;\n  file_name: string;\n  entity_type: string;\n  entity_id: string;\n  file_path: string;\n  file_size: number;\n  mime_type: string;\n  is_required: boolean;\n  created_at: string;\n  updated_at: string;\n  created_by: string;\n  updated_by?: string;\n}\n\nexport interface CreateDocumentData {\n  document_type: string;\n  file_name: string;\n  entity_type: string;\n  entity_id: string;\n  file_path: string;\n  file_size: number;\n  mime_type: string;\n  is_required?: boolean;\n  application_id?: string;\n}\n\nexport interface DocumentFilter {\n  entity_type?: string;\n  entity_id?: string;\n  document_type?: string;\n  is_required?: boolean;\n  page?: number;\n  limit?: number;\n  sort_by?: string;\n  sort_order?: 'ASC' | 'DESC';\n}\n\nexport interface DocumentListResponse {\n  success: boolean;\n  message: string;\n  data: Document[];\n  total: number;\n  page: number;\n  limit: number;\n}\n\nexport interface DocumentResponse {\n  success: boolean;\n  message: string;\n  data: Document;\n}\n\nclass DocumentService {\n  private baseUrl = '/documents';\n\n  async uploadDocument(\n    file: File,\n    entityType: string,\n    entityId: string,\n    documentType: string = 'OTHER',\n    isRequired: boolean = false,\n    applicationId?: string\n  ): Promise<DocumentResponse> {\n    try {\n      console.log('🔄 Uploading document:', {\n        fileName: file.name,\n        entityType,\n        entityId,\n        documentType\n      });\n\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('entity_type', entityType);\n      formData.append('entity_id', entityId);\n      formData.append('document_type', documentType);\n      formData.append('file_name', file.name);\n      formData.append('file_size', file.size.toString());\n      formData.append('mime_type', file.type);\n      formData.append('is_required', isRequired.toString());\n      \n      if (applicationId) {\n        formData.append('application_id', applicationId);\n      }\n\n      const response = await apiClient.post(`${this.baseUrl}/upload`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      console.log('✅ Document uploaded successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error uploading document:', error);\n      throw error;\n    }\n  }\n\n  async uploadMultipleDocuments(\n    files: File[],\n    entityType: string,\n    entityId: string,\n    documentType: string = 'OTHER',\n    isRequired: boolean = false,\n    applicationId?: string\n  ): Promise<DocumentResponse[]> {\n    try {\n      console.log('🔄 Uploading multiple documents:', {\n        fileCount: files.length,\n        entityType,\n        entityId,\n        documentType\n      });\n\n      const uploadPromises = files.map(file => \n        this.uploadDocument(file, entityType, entityId, documentType, isRequired, applicationId)\n      );\n\n      const results = await Promise.all(uploadPromises);\n      console.log('✅ All documents uploaded successfully');\n      return results;\n    } catch (error) {\n      console.error('❌ Error uploading multiple documents:', error);\n      throw error;\n    }\n  }\n\n  async getDocuments(filter: DocumentFilter = {}): Promise<DocumentListResponse> {\n    try {\n      console.log('🔄 Fetching documents with filter:', filter);\n\n      const params = new URLSearchParams();\n      Object.entries(filter).forEach(([key, value]) => {\n        if (value !== undefined && value !== null && value !== '') {\n          params.append(key, value.toString());\n        }\n      });\n\n      const response = await apiClient.get(`${this.baseUrl}?${params.toString()}`);\n\n      console.log('✅ Documents fetched successfully:', {\n        total: response.data.total,\n        count: response.data.data?.length || 0\n      });\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error fetching documents:', error);\n      throw error;\n    }\n  }\n\n  async getDocumentsByEntity(entityType: string, entityId: string): Promise<Document[]> {\n    try {\n      console.log('🔄 Fetching documents by entity:', { entityType, entityId });\n\n      const response = await apiClient.get(`${this.baseUrl}/by-entity/${entityType}/${entityId}`);\n\n      console.log('✅ Entity documents fetched successfully:', {\n        count: response.data?.length || 0\n      });\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error fetching entity documents:', error);\n      throw error;\n    }\n  }\n\n  async getDocument(documentId: string): Promise<DocumentResponse> {\n    try {\n      console.log('🔄 Fetching document:', documentId);\n\n      const response = await apiClient.get(`${this.baseUrl}/${documentId}`);\n\n      console.log('✅ Document fetched successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error fetching document:', error);\n      throw error;\n    }\n  }\n\n  async downloadDocument(documentId: string): Promise<Blob> {\n    try {\n      console.log('🔄 Downloading document:', documentId);\n\n      const response = await apiClient.get(`${this.baseUrl}/${documentId}/download`, {\n        responseType: 'blob'\n      });\n\n      console.log('✅ Document downloaded successfully');\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error downloading document:', error);\n      throw error;\n    }\n  }\n\n  async deleteDocument(documentId: string): Promise<void> {\n    try {\n      console.log('🔄 Deleting document:', documentId);\n\n      await apiClient.delete(`${this.baseUrl}/${documentId}`);\n\n      console.log('✅ Document deleted successfully');\n    } catch (error) {\n      console.error('❌ Error deleting document:', error);\n      throw error;\n    }\n  }\n\n  async updateDocument(\n    documentId: string,\n    updateData: Partial<CreateDocumentData>\n  ): Promise<DocumentResponse> {\n    try {\n      console.log('🔄 Updating document:', documentId, updateData);\n\n      const response = await apiClient.put(`${this.baseUrl}/${documentId}`, updateData);\n\n      console.log('✅ Document updated successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error updating document:', error);\n      throw error;\n    }\n  }\n\n  // Helper method to format file size\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  // Helper method to get file icon based on mime type\n  getFileIcon(mimeType: string): string {\n    if (mimeType.startsWith('image/')) return 'ri-image-line';\n    if (mimeType.includes('pdf')) return 'ri-file-pdf-line';\n    if (mimeType.includes('word') || mimeType.includes('document')) return 'ri-file-word-line';\n    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'ri-file-excel-line';\n    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'ri-file-ppt-line';\n    if (mimeType.startsWith('video/')) return 'ri-video-line';\n    if (mimeType.startsWith('audio/')) return 'ri-music-line';\n    return 'ri-file-line';\n  }\n}\n\nexport const documentService = new DocumentService();\n"], "names": [], "mappings": ";;;AAAA;;AAyDA,MAAM;IACI,UAAU,aAAa;IAE/B,MAAM,eACJ,IAAU,EACV,UAAkB,EAClB,QAAgB,EAChB,eAAuB,OAAO,EAC9B,aAAsB,KAAK,EAC3B,aAAsB,EACK;QAC3B,IAAI;YACF,QAAQ,GAAG,CAAC,0BAA0B;gBACpC,UAAU,KAAK,IAAI;gBACnB;gBACA;gBACA;YACF;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,eAAe;YAC/B,SAAS,MAAM,CAAC,aAAa;YAC7B,SAAS,MAAM,CAAC,iBAAiB;YACjC,SAAS,MAAM,CAAC,aAAa,KAAK,IAAI;YACtC,SAAS,MAAM,CAAC,aAAa,KAAK,IAAI,CAAC,QAAQ;YAC/C,SAAS,MAAM,CAAC,aAAa,KAAK,IAAI;YACtC,SAAS,MAAM,CAAC,eAAe,WAAW,QAAQ;YAElD,IAAI,eAAe;gBACjB,SAAS,MAAM,CAAC,kBAAkB;YACpC;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,UAAU;gBACxE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,qCAAqC,SAAS,IAAI;YAC9D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,wBACJ,KAAa,EACb,UAAkB,EAClB,QAAgB,EAChB,eAAuB,OAAO,EAC9B,aAAsB,KAAK,EAC3B,aAAsB,EACO;QAC7B,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;gBAC9C,WAAW,MAAM,MAAM;gBACvB;gBACA;gBACA;YACF;YAEA,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAC/B,IAAI,CAAC,cAAc,CAAC,MAAM,YAAY,UAAU,cAAc,YAAY;YAG5E,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,MAAM,aAAa,SAAyB,CAAC,CAAC,EAAiC;QAC7E,IAAI;YACF,QAAQ,GAAG,CAAC,sCAAsC;YAElD,MAAM,SAAS,IAAI;YACnB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;oBACzD,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACnC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;YAE3E,QAAQ,GAAG,CAAC,qCAAqC;gBAC/C,OAAO,SAAS,IAAI,CAAC,KAAK;gBAC1B,OAAO,SAAS,IAAI,CAAC,IAAI,EAAE,UAAU;YACvC;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,UAAkB,EAAE,QAAgB,EAAuB;QACpF,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;gBAAE;gBAAY;YAAS;YAEvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,UAAU;YAE1F,QAAQ,GAAG,CAAC,4CAA4C;gBACtD,OAAO,SAAS,IAAI,EAAE,UAAU;YAClC;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,MAAM,YAAY,UAAkB,EAA6B;QAC/D,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB;YAErC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,YAAY;YAEpE,QAAQ,GAAG,CAAC,oCAAoC,SAAS,IAAI;YAC7D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,UAAkB,EAAiB;QACxD,IAAI;YACF,QAAQ,GAAG,CAAC,4BAA4B;YAExC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE;gBAC7E,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,MAAM,eAAe,UAAkB,EAAiB;QACtD,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB;YAErC,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,YAAY;YAEtD,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,MAAM,eACJ,UAAkB,EAClB,UAAuC,EACZ;QAC3B,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB,YAAY;YAEjD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE;YAEtE,QAAQ,GAAG,CAAC,oCAAoC,SAAS,IAAI;YAC7D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,eAAe,KAAa,EAAU;QACpC,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,oDAAoD;IACpD,YAAY,QAAgB,EAAU;QACpC,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;QAC1C,IAAI,SAAS,QAAQ,CAAC,QAAQ,OAAO;QACrC,IAAI,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,aAAa,OAAO;QACvE,IAAI,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,gBAAgB,OAAO;QAC3E,IAAI,SAAS,QAAQ,CAAC,iBAAiB,SAAS,QAAQ,CAAC,iBAAiB,OAAO;QACjF,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;QAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;QAC1C,OAAO;IACT;AACF;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/consumerAffairsService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\nimport { documentService } from '../documentService';\n\nexport interface ConsumerAffairsComplaint {\n  complaint_id: string;\n  title: string;\n  description: string;\n  category: string;\n  status: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  created_at: string;\n  updated_at: string;\n  created_by: string;\n  updated_by?: string;\n}\n\nexport interface CreateConsumerAffairsComplaintData {\n  title: string;\n  description: string;\n  category: string;\n  priority?: 'low' | 'medium' | 'high' | 'urgent';\n  attachments?: File[];\n}\n\nexport interface ConsumerAffairsComplaintFilter {\n  status?: string;\n  category?: string;\n  priority?: string;\n  search?: string;\n  page?: number;\n  limit?: number;\n  sort_by?: string;\n  sort_order?: 'ASC' | 'DESC';\n}\n\nexport interface ConsumerAffairsComplaintListResponse {\n  success: boolean;\n  message: string;\n  data: ConsumerAffairsComplaint[];\n  total: number;\n  page: number;\n  limit: number;\n}\n\nexport interface ConsumerAffairsComplaintResponse {\n  success: boolean;\n  message: string;\n  data: ConsumerAffairsComplaint;\n}\n\nclass ConsumerAffairsService {\n  private baseUrl = '/consumer-affairs-complaints';\n\n  async createComplaint(data: CreateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaintResponse> {\n    try {\n      console.log('🔄 Creating consumer affairs complaint:', {\n        title: data.title,\n        category: data.category,\n        hasAttachments: data.attachments && data.attachments.length > 0\n      });\n\n      const formData = new FormData();\n      formData.append('title', data.title);\n      formData.append('description', data.description);\n      formData.append('category', data.category);\n      \n      if (data.priority) {\n        formData.append('priority', data.priority);\n      }\n\n      // Add attachments if provided\n      if (data.attachments && data.attachments.length > 0) {\n        data.attachments.forEach((file, index) => {\n          formData.append('attachments', file);\n        });\n      }\n\n      const response = await apiClient.post(this.baseUrl, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      console.log('✅ Consumer affairs complaint created successfully:', response.data);\n\n      // Upload documents to polymorphic document table if complaint was created successfully\n      if (response.data.success && data.attachments && data.attachments.length > 0) {\n        try {\n          const complaintId = response.data.data.complaint_id;\n          await documentService.uploadMultipleDocuments(\n            data.attachments,\n            'consumer_affairs_complaint',\n            complaintId,\n            'COMPLAINT_ATTACHMENT',\n            false\n          );\n          console.log('✅ Complaint attachments uploaded to document table');\n        } catch (docError) {\n          console.warn('⚠️ Failed to upload documents to document table:', docError);\n          // Don't fail the complaint creation if document upload fails\n        }\n      }\n\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error creating consumer affairs complaint:', error);\n      throw error;\n    }\n  }\n\n  async getComplaints(filter: ConsumerAffairsComplaintFilter = {}): Promise<ConsumerAffairsComplaintListResponse> {\n    try {\n      console.log('🔄 Fetching consumer affairs complaints with filter:', filter);\n\n      const params = new URLSearchParams();\n      Object.entries(filter).forEach(([key, value]) => {\n        if (value !== undefined && value !== null && value !== '') {\n          params.append(key, value.toString());\n        }\n      });\n\n      const response = await apiClient.get(`${this.baseUrl}?${params.toString()}`);\n\n      console.log('✅ Consumer affairs complaints fetched successfully:', {\n        total: response.data.total,\n        count: response.data.data?.length || 0\n      });\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error fetching consumer affairs complaints:', error);\n      throw error;\n    }\n  }\n\n  async getComplaint(complaintId: string): Promise<ConsumerAffairsComplaintResponse> {\n    try {\n      console.log('🔄 Fetching consumer affairs complaint:', complaintId);\n\n      const response = await apiClient.get(`${this.baseUrl}/${complaintId}`);\n\n      console.log('✅ Consumer affairs complaint fetched successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error fetching consumer affairs complaint:', error);\n      throw error;\n    }\n  }\n\n  async updateComplaint(\n    complaintId: string,\n    updateData: Partial<CreateConsumerAffairsComplaintData>\n  ): Promise<ConsumerAffairsComplaintResponse> {\n    try {\n      console.log('🔄 Updating consumer affairs complaint:', complaintId, updateData);\n\n      const response = await apiClient.put(`${this.baseUrl}/${complaintId}`, updateData);\n\n      console.log('✅ Consumer affairs complaint updated successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error updating consumer affairs complaint:', error);\n      throw error;\n    }\n  }\n\n  async updateComplaintStatus(\n    complaintId: string,\n    status: ConsumerAffairsComplaint['status']\n  ): Promise<ConsumerAffairsComplaintResponse> {\n    try {\n      console.log('🔄 Updating consumer affairs complaint status:', complaintId, status);\n\n      const response = await apiClient.patch(`${this.baseUrl}/${complaintId}/status`, { status });\n\n      console.log('✅ Consumer affairs complaint status updated successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Error updating consumer affairs complaint status:', error);\n      throw error;\n    }\n  }\n\n  async deleteComplaint(complaintId: string): Promise<void> {\n    try {\n      console.log('🔄 Deleting consumer affairs complaint:', complaintId);\n\n      await apiClient.delete(`${this.baseUrl}/${complaintId}`);\n\n      console.log('✅ Consumer affairs complaint deleted successfully');\n    } catch (error) {\n      console.error('❌ Error deleting consumer affairs complaint:', error);\n      throw error;\n    }\n  }\n\n  // Helper methods\n  getStatusColor(status: string): string {\n    switch (status?.toLowerCase()) {\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  }\n\n  getPriorityColor(priority: string): string {\n    switch (priority?.toLowerCase()) {\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  }\n\n  getStatusOptions(): Array<{ value: string; label: string }> {\n    return [\n      { value: 'submitted', label: 'Submitted' },\n      { value: 'under_review', label: 'Under Review' },\n      { value: 'investigating', label: 'Investigating' },\n      { value: 'resolved', label: 'Resolved' },\n      { value: 'closed', label: 'Closed' }\n    ];\n  }\n\n  getCategoryOptions(): Array<{ value: string; label: string }> {\n    return [\n      { value: 'Billing & Charges', label: 'Billing & Charges' },\n      { value: 'Service Quality', label: 'Service Quality' },\n      { value: 'Network Issues', label: 'Network Issues' },\n      { value: 'Customer Service', label: 'Customer Service' },\n      { value: 'Other', label: 'Other' }\n    ];\n  }\n\n  getPriorityOptions(): Array<{ value: string; label: string }> {\n    return [\n      { value: 'low', label: 'Low' },\n      { value: 'medium', label: 'Medium' },\n      { value: 'high', label: 'High' },\n      { value: 'urgent', label: 'Urgent' }\n    ];\n  }\n}\n\nexport const consumerAffairsService = new ConsumerAffairsService();\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAiDA,MAAM;IACI,UAAU,+BAA+B;IAEjD,MAAM,gBAAgB,IAAwC,EAA6C;QACzG,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C;gBACrD,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG;YAChE;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;YACnC,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAEzC,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAC3C;YAEA,8BAA8B;YAC9B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;oBAC9B,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU;gBAC5D,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,sDAAsD,SAAS,IAAI;YAE/E,uFAAuF;YACvF,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBAC5E,IAAI;oBACF,MAAM,cAAc,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY;oBACnD,MAAM,qIAAA,CAAA,kBAAe,CAAC,uBAAuB,CAC3C,KAAK,WAAW,EAChB,8BACA,aACA,wBACA;oBAEF,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,UAAU;oBACjB,QAAQ,IAAI,CAAC,oDAAoD;gBACjE,6DAA6D;gBAC/D;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;IAEA,MAAM,cAAc,SAAyC,CAAC,CAAC,EAAiD;QAC9G,IAAI;YACF,QAAQ,GAAG,CAAC,wDAAwD;YAEpE,MAAM,SAAS,IAAI;YACnB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;oBACzD,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACnC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;YAE3E,QAAQ,GAAG,CAAC,uDAAuD;gBACjE,OAAO,SAAS,IAAI,CAAC,KAAK;gBAC1B,OAAO,SAAS,IAAI,CAAC,IAAI,EAAE,UAAU;YACvC;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,MAAM;QACR;IACF;IAEA,MAAM,aAAa,WAAmB,EAA6C;QACjF,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C;YAEvD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,aAAa;YAErE,QAAQ,GAAG,CAAC,sDAAsD,SAAS,IAAI;YAC/E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;IAEA,MAAM,gBACJ,WAAmB,EACnB,UAAuD,EACZ;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C,aAAa;YAEpE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,aAAa,EAAE;YAEvE,QAAQ,GAAG,CAAC,sDAAsD,SAAS,IAAI;YAC/E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;IAEA,MAAM,sBACJ,WAAmB,EACnB,MAA0C,EACC;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,kDAAkD,aAAa;YAE3E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,EAAE;gBAAE;YAAO;YAEzF,QAAQ,GAAG,CAAC,6DAA6D,SAAS,IAAI;YACtF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uDAAuD;YACrE,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,WAAmB,EAAiB;QACxD,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C;YAEvD,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,aAAa;YAEvD,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,eAAe,MAAc,EAAU;QACrC,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,iBAAiB,QAAgB,EAAU;QACzC,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,mBAA4D;QAC1D,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;IAEA,qBAA8D;QAC5D,OAAO;YACL;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAmB,OAAO;YAAkB;YACrD;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAoB,OAAO;YAAmB;YACvD;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;IAEA,qBAA8D;QAC5D,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;AACF;AAEO,MAAM,yBAAyB,IAAI", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/index.ts"], "sourcesContent": ["// Consumer Affairs Services\nexport * from './consumerAffairsService';\nexport { consumerAffairsService } from './consumerAffairsService';\n"], "names": [], "mappings": "AAAA,4BAA4B;;AAC5B", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/consumer-affairs/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { consumerAffairsService, ConsumerAffairsComplaint } from '@/services/consumer-affairs';\nimport Loader from '@/components/Loader';\n\nconst ConsumerAffairsPage: React.FC = () => {\n  const { isAuthenticated } = useAuth();\n  const [complaints, setComplaints] = useState<ConsumerAffairsComplaint[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [filter, setFilter] = useState({\n    status: '',\n    category: '',\n    priority: '',\n    search: ''\n  });\n\n  // Fetch complaints\n  useEffect(() => {\n    const fetchComplaints = async () => {\n      if (!isAuthenticated) return;\n\n      try {\n        setLoading(true);\n        console.log('🔄 Fetching all consumer affairs complaints for staff...');\n        \n        // For staff, fetch all complaints (not filtered by user)\n        const response = await consumerAffairsService.getComplaints({\n          limit: 100,\n          ...filter\n        });\n\n        console.log('✅ Consumer Affairs complaints fetched:', response);\n        \n        if (response.success && Array.isArray(response.data)) {\n          setComplaints(response.data);\n        } else {\n          setComplaints([]);\n        }\n      } catch (err: unknown) {\n        console.error('❌ Error fetching complaints:', err);\n        if (err instanceof Error) {\n          setError(`Failed to load complaints: ${err.message}`);\n        } else {\n          setError('Failed to load complaints: Unknown error');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchComplaints();\n  }, [isAuthenticated, filter]);\n\n  const handleStatusUpdate = async (\n    complaintId: string,\n    newStatus: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed'\n  ) => {\n    try {\n      // TODO: Implement status update API call\n      console.log('Updating complaint status:', complaintId, newStatus);\n\n      // Update local state\n      setComplaints(prev =>\n        prev.map(complaint =>\n          complaint.complaint_id === complaintId\n            ? { ...complaint, status: newStatus }\n            : complaint\n        )\n      );\n    } catch (error) {\n      console.error('Error updating status:', error);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status?.toLowerCase()) {\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority?.toLowerCase()) {\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"p-6 min-h-full bg-gray-50 dark:bg-gray-900\">\n        <Loader message=\"Loading consumer affairs complaints...\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 min-h-full bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n          Consumer Affairs Management\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n          Manage and respond to customer complaints and service issues\n        </p>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-file-list-line text-2xl text-blue-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Complaints</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{complaints.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-time-line text-2xl text-yellow-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Submitted</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {complaints.filter(c => c.status === 'submitted').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-progress-line text-2xl text-blue-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Under Review</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {complaints.filter(c => c.status === 'under_review').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <i className=\"ri-check-line text-2xl text-green-600\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Resolved</p>\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\n                {complaints.filter(c => c.status === 'resolved').length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Search\n            </label>\n            <input\n              type=\"text\"\n              placeholder=\"Search complaints...\"\n              value={filter.search}\n              onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Status\n            </label>\n            <select\n              value={filter.status}\n              onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n              aria-label=\"Filter by status\"\n              title=\"Filter complaints by status\"\n            >\n              <option value=\"\">All Statuses</option>\n              <option value=\"submitted\">Submitted</option>\n              <option value=\"under_review\">Under Review</option>\n              <option value=\"investigating\">Investigating</option>\n              <option value=\"resolved\">Resolved</option>\n              <option value=\"closed\">Closed</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Category\n            </label>\n            <select\n              value={filter.category}\n              onChange={(e) => setFilter(prev => ({ ...prev, category: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n              aria-label=\"Filter by category\"\n              title=\"Filter complaints by category\"\n            >\n              <option value=\"\">All Categories</option>\n              <option value=\"Billing & Charges\">Billing & Charges</option>\n              <option value=\"Service Quality\">Service Quality</option>\n              <option value=\"Network Issues\">Network Issues</option>\n              <option value=\"Customer Service\">Customer Service</option>\n              <option value=\"Other\">Other</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Priority\n            </label>\n            <select\n              value={filter.priority}\n              onChange={(e) => setFilter(prev => ({ ...prev, priority: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\n              aria-label=\"Filter by priority\"\n              title=\"Filter complaints by priority\"\n            >\n              <option value=\"\">All Priorities</option>\n              <option value=\"low\">Low</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"high\">High</option>\n              <option value=\"urgent\">Urgent</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6\">\n          <div className=\"flex\">\n            <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\n            <p className=\"text-red-700 dark:text-red-200\">{error}</p>\n          </div>\n        </div>\n      )}\n\n      {/* Complaints Table */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n            Consumer Affairs Complaints ({complaints.length})\n          </h3>\n        </div>\n\n        {complaints.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <i className=\"ri-file-search-line text-4xl text-gray-400 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No complaints found</h3>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              No consumer affairs complaints have been submitted yet.\n            </p>\n          </div>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n              <thead className=\"bg-gray-50 dark:bg-gray-900\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Complaint\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Category\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Priority\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Submitted\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                {complaints.map((complaint) => (\n                  <tr key={complaint.complaint_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                          {complaint.title}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\">\n                          {complaint.description}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                        {complaint.category}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(complaint.status)}`}>\n                        {complaint.status?.replace('_', ' ').toUpperCase()}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(complaint.priority)}`}>\n                        {complaint.priority?.toUpperCase() || 'MEDIUM'}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                      {new Date(complaint.created_at).toLocaleDateString()}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <button\n                        type=\"button\"\n                        className=\"text-primary hover:text-red-700 mr-3\"\n                        disabled\n                        title=\"View Details modal not implemented\"\n                      >\n                        View Details\n                      </button>\n                      <select\n                        value={complaint.status}\n                        onChange={(e) =>\n                          handleStatusUpdate(\n                            complaint.complaint_id,\n                            e.target.value as 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed'\n                          )\n                        }\n                        className=\"text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 dark:bg-gray-700 dark:text-gray-100\"\n                        aria-label=\"Update complaint status\"\n                        title=\"Update complaint status\"\n                      >\n                        <option value=\"submitted\">Submitted</option>\n                        <option value=\"under_review\">Under Review</option>\n                        <option value=\"investigating\">Investigating</option>\n                        <option value=\"resolved\">Resolved</option>\n                        <option value=\"closed\">Closed</option>\n                      </select>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ConsumerAffairsPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,sBAAgC;;IACpC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IAEA,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;iEAAkB;oBACtB,IAAI,CAAC,iBAAiB;oBAEtB,IAAI;wBACF,WAAW;wBACX,QAAQ,GAAG,CAAC;wBAEZ,yDAAyD;wBACzD,MAAM,WAAW,MAAM,mKAAA,CAAA,yBAAsB,CAAC,aAAa,CAAC;4BAC1D,OAAO;4BACP,GAAG,MAAM;wBACX;wBAEA,QAAQ,GAAG,CAAC,0CAA0C;wBAEtD,IAAI,SAAS,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;4BACpD,cAAc,SAAS,IAAI;wBAC7B,OAAO;4BACL,cAAc,EAAE;wBAClB;oBACF,EAAE,OAAO,KAAc;wBACrB,QAAQ,KAAK,CAAC,gCAAgC;wBAC9C,IAAI,eAAe,OAAO;4BACxB,SAAS,CAAC,2BAA2B,EAAE,IAAI,OAAO,EAAE;wBACtD,OAAO;4BACL,SAAS;wBACX;oBACF,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;wCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,qBAAqB,OACzB,aACA;QAEA,IAAI;YACF,yCAAyC;YACzC,QAAQ,GAAG,CAAC,8BAA8B,aAAa;YAEvD,qBAAqB;YACrB,cAAc,CAAA,OACZ,KAAK,GAAG,CAAC,CAAA,YACP,UAAU,YAAY,KAAK,cACvB;wBAAE,GAAG,SAAS;wBAAE,QAAQ;oBAAU,IAClC;QAGV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;IAGtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,6LAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;0BAMvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDAA2D,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK/F,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMhE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMnE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,OAAO,MAAM;oCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAU;;;;;;;;;;;;sCAId,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO,OAAO,MAAM;oCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6LAAC;4CAAO,OAAM;sDAAe;;;;;;sDAC7B,6LAAC;4CAAO,OAAM;sDAAgB;;;;;;sDAC9B,6LAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAI3B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO,OAAO,QAAQ;oCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACzE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAoB;;;;;;sDAClC,6LAAC;4CAAO,OAAM;sDAAkB;;;;;;sDAChC,6LAAC;4CAAO,OAAM;sDAAiB;;;;;;sDAC/B,6LAAC;4CAAO,OAAM;sDAAmB;;;;;;sDACjC,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;sCAI1B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO,OAAO,QAAQ;oCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACzE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO9B,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;0BAMrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAuD;gCACrC,WAAW,MAAM;gCAAC;;;;;;;;;;;;oBAInD,WAAW,MAAM,KAAK,kBACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;6CAKlD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,6LAAC;oCAAM,WAAU;8CACd,WAAW,GAAG,CAAC,CAAC,0BACf,6LAAC;4CAAgC,WAAU;;8DACzC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EACZ,UAAU,KAAK;;;;;;0EAElB,6LAAC;gEAAI,WAAU;0EACZ,UAAU,WAAW;;;;;;;;;;;;;;;;;8DAI5B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAU;kEACb,UAAU,QAAQ;;;;;;;;;;;8DAGvB,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,UAAU,MAAM,GAAG;kEAC5G,UAAU,MAAM,EAAE,QAAQ,KAAK,KAAK;;;;;;;;;;;8DAGzC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,UAAU,QAAQ,GAAG;kEAChH,UAAU,QAAQ,EAAE,iBAAiB;;;;;;;;;;;8DAG1C,6LAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,UAAU,UAAU,EAAE,kBAAkB;;;;;;8DAEpD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,QAAQ;4DACR,OAAM;sEACP;;;;;;sEAGD,6LAAC;4DACC,OAAO,UAAU,MAAM;4DACvB,UAAU,CAAC,IACT,mBACE,UAAU,YAAY,EACtB,EAAE,MAAM,CAAC,KAAK;4DAGlB,WAAU;4DACV,cAAW;4DACX,OAAM;;8EAEN,6LAAC;oEAAO,OAAM;8EAAY;;;;;;8EAC1B,6LAAC;oEAAO,OAAM;8EAAe;;;;;;8EAC7B,6LAAC;oEAAO,OAAM;8EAAgB;;;;;;8EAC9B,6LAAC;oEAAO,OAAM;8EAAW;;;;;;8EACzB,6LAAC;oEAAO,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;2CAtDpB,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEjD;GA5WM;;QACwB,kIAAA,CAAA,UAAO;;;KAD/B;uCA8WS", "debugId": null}}]}