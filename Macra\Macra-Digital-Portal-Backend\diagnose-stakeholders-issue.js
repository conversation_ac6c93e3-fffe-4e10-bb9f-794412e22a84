const mysql = require('mysql2/promise');
require('dotenv').config();

async function diagnoseStakeholdersIssue() {
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'macra_db'
    });

    try {
        console.log('🔍 Diagnosing stakeholders table issues...\n');
        
        // 1. Check table structure
        console.log('1. Checking stakeholders table structure:');
        const [tableStructure] = await connection.execute(`DESCRIBE stakeholders`);
        tableStructure.forEach(col => {
            console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? `[${col.Key}]` : ''} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
        });

        // 2. Check contacts table structure 
        console.log('\n2. Checking contacts table structure:');
        const [contactsStructure] = await connection.execute(`DESCRIBE contacts`);
        contactsStructure.forEach(col => {
            console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? `[${col.Key}]` : ''} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
        });

        // 3. Check users table structure
        console.log('\n3. Checking users table structure:');
        const [usersStructure] = await connection.execute(`DESCRIBE users`);
        usersStructure.forEach(col => {
            console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? `[${col.Key}]` : ''} ${col.Default ? `DEFAULT ${col.Default}` : ''}`);
        });

        // 4. Check existing foreign key constraints
        console.log('\n4. Checking existing foreign key constraints on stakeholders:');
        const [constraints] = await connection.execute(`
            SELECT 
                kcu.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME,
                rc.DELETE_RULE,
                rc.UPDATE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
                ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
                AND kcu.CONSTRAINT_SCHEMA = rc.CONSTRAINT_SCHEMA
            WHERE kcu.TABLE_SCHEMA = ? 
            AND kcu.TABLE_NAME = 'stakeholders'
            AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        `, [process.env.DB_NAME || 'macra_db']);
        
        if (constraints.length === 0) {
            console.log('  No foreign key constraints found');
        } else {
            constraints.forEach(constraint => {
                console.log(`  ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME} (DELETE: ${constraint.DELETE_RULE})`);
            });
        }

        // 5. Check data type compatibility
        console.log('\n5. Checking data type compatibility:');
        
        // Get stakeholders contact_id column info
        const stakeholdersContactId = tableStructure.find(col => col.Field === 'contact_id');
        const contactsContactId = contactsStructure.find(col => col.Field === 'contact_id');
        
        console.log(`  stakeholders.contact_id: ${stakeholdersContactId.Type} (${stakeholdersContactId.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
        console.log(`  contacts.contact_id: ${contactsContactId.Type} (${contactsContactId.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
        console.log(`  Types match: ${stakeholdersContactId.Type === contactsContactId.Type ? '✅' : '❌'}`);

        // Check user_id compatibility
        const stakeholdersCreatedBy = tableStructure.find(col => col.Field === 'created_by');
        const usersUserId = usersStructure.find(col => col.Field === 'user_id');
        
        console.log(`  stakeholders.created_by: ${stakeholdersCreatedBy.Type} (${stakeholdersCreatedBy.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
        console.log(`  users.user_id: ${usersUserId.Type} (${usersUserId.Null === 'YES' ? 'NULL' : 'NOT NULL'})`);
        console.log(`  Types match: ${stakeholdersCreatedBy.Type === usersUserId.Type ? '✅' : '❌'}`);

        // 6. Check for problematic data
        console.log('\n6. Checking for problematic data:');
        
        const [stakeholdersCount] = await connection.execute(`SELECT COUNT(*) as count FROM stakeholders`);
        console.log(`  Total stakeholders: ${stakeholdersCount[0].count}`);
        
        if (stakeholdersCount[0].count > 0) {
            // Check for invalid contact_id references
            const [invalidContactIds] = await connection.execute(`
                SELECT COUNT(*) as count 
                FROM stakeholders s
                WHERE s.contact_id IS NOT NULL 
                AND s.contact_id NOT IN (SELECT c.contact_id FROM contacts c WHERE c.contact_id IS NOT NULL)
            `);
            console.log(`  Invalid contact_id references: ${invalidContactIds[0].count}`);

            // Check for invalid created_by references
            const [invalidCreatedBy] = await connection.execute(`
                SELECT COUNT(*) as count 
                FROM stakeholders s
                WHERE s.created_by IS NOT NULL 
                AND s.created_by NOT IN (SELECT u.user_id FROM users u WHERE u.user_id IS NOT NULL)
            `);
            console.log(`  Invalid created_by references: ${invalidCreatedBy[0].count}`);

            // Check for invalid updated_by references
            const [invalidUpdatedBy] = await connection.execute(`
                SELECT COUNT(*) as count 
                FROM stakeholders s
                WHERE s.updated_by IS NOT NULL 
                AND s.updated_by NOT IN (SELECT u.user_id FROM users u WHERE u.user_id IS NOT NULL)
            `);
            console.log(`  Invalid updated_by references: ${invalidUpdatedBy[0].count}`);
        }

        // 7. Check if constraints already exist
        console.log('\n7. Attempting to identify the exact issue...');
        
        // Try to create the constraint manually to see the exact error
        try {
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT test_contact_fk 
                FOREIGN KEY (contact_id) REFERENCES contacts(contact_id) 
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('  ✅ Foreign key constraint can be created successfully');
            
            // Clean up test constraint
            await connection.execute(`ALTER TABLE stakeholders DROP FOREIGN KEY test_contact_fk`);
        } catch (error) {
            console.log(`  ❌ Cannot create foreign key constraint: ${error.message}`);
            console.log(`  Error code: ${error.code}`);
        }

        console.log('\n🎯 Diagnosis complete!');
        
    } catch (error) {
        console.error('❌ Error during diagnosis:', error);
        throw error;
    } finally {
        await connection.end();
    }
}

diagnoseStakeholdersIssue();