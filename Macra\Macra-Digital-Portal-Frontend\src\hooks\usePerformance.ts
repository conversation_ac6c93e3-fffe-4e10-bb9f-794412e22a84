'use client';

import { useEffect, useCallback } from 'react';

interface PerformanceMetrics {
  loadComplete: number;
  domContentLoaded: number;
  firstPaint?: number;
  firstContentfulPaint?: number;
}

export const usePerformance = (pageName?: string) => {
  const measurePageLoad = useCallback(() => {
    if (typeof window === 'undefined') return;

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');

    const metrics: PerformanceMetrics = {
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.loadEventStart,
    };

    // Get paint metrics if available
    paint.forEach((entry) => {
      if (entry.name === 'first-paint') {
        metrics.firstPaint = entry.startTime;
      } else if (entry.name === 'first-contentful-paint') {
        metrics.firstContentfulPaint = entry.startTime;
      }
    });

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development' && pageName) {
      console.group(`🚀 Performance Metrics - ${pageName}`);
      console.log('DOM Content Loaded:', `${metrics.domContentLoaded.toFixed(2)}ms`);
      console.log('Load Complete:', `${metrics.loadComplete.toFixed(2)}ms`);
      if (metrics.firstPaint) {
        console.log('First Paint:', `${metrics.firstPaint.toFixed(2)}ms`);
      }
      if (metrics.firstContentfulPaint) {
        console.log('First Contentful Paint:', `${metrics.firstContentfulPaint.toFixed(2)}ms`);
      }
      console.groupEnd();
    }

    return metrics;
  }, [pageName]);

  const measureResourceLoad = useCallback((resourceName: string) => {
    if (typeof window === 'undefined') return;

    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    const resource = resources.find(r => r.name.includes(resourceName));

    if (resource && process.env.NODE_ENV === 'development') {
      console.log(`📦 Resource Load - ${resourceName}:`, `${resource.duration.toFixed(2)}ms`);
    }

    return resource;
  }, []);

  const measureApiCall = useCallback((apiName: string) => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🌐 API Call - ${apiName}:`, `${duration.toFixed(2)}ms`);
      }
      
      return duration;
    };
  }, []);

  useEffect(() => {
    // Measure page load performance after component mounts
    const timer = setTimeout(() => {
      measurePageLoad();
    }, 100);

    return () => clearTimeout(timer);
  }, [measurePageLoad]);

  return {
    measurePageLoad,
    measureResourceLoad,
    measureApiCall
  };
};

export default usePerformance;