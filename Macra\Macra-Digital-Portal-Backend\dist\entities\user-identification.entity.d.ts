import { User } from './user.entity';
import { IdentificationType } from './identification-type.entity';
export declare class UserIdentification {
    identification_type_id: string;
    user_id: string;
    identification_value: string;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date;
    created_by?: string;
    updated_by?: string;
    identification_type: IdentificationType;
    user: User;
    creator?: User;
    updater?: User;
}
