import { apiClient } from '../lib/apiClient';

export interface ApplicationFormData {
  form_data_id?: string;
  application_id: string;
  section_name: string;
  section_data: Record<string, any>;
  completed: boolean;
  created_at?: string;
  updated_at?: string;
}

export const applicationFormDataService = {
  // Save form section data
  async saveFormSection(
    applicationId: string,
    sectionName: string,
    sectionData: Record<string, any>
  ): Promise<ApplicationFormData> {
    try {
      console.log(`Saving form section ${sectionName} for application ${applicationId}:`, sectionData);
      
      const response = await apiClient.post('/application-form-data', {
        application_id: applicationId,
        section_name: sectionName,
        section_data: sectionData,
        completed: true
      });
      
      console.log(`Form section ${sectionName} saved successfully:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error saving form section ${sectionName}:`, error);
      throw error;
    }
  },

  // Update existing form section data
  async updateFormSection(
    applicationId: string,
    sectionName: string,
    sectionData: Record<string, any>
  ): Promise<ApplicationFormData> {
    try {
      console.log(`Updating form section ${sectionName} for application ${applicationId}:`, sectionData);
      
      const response = await apiClient.put(`/application-form-data/${applicationId}/${sectionName}`, {
        section_data: sectionData,
        completed: true
      });
      
      console.log(`Form section ${sectionName} updated successfully:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error updating form section ${sectionName}:`, error);
      throw error;
    }
  },

  // Get form section data
  async getFormSection(applicationId: string, sectionName: string): Promise<ApplicationFormData | null> {
    try {
      const response = await apiClient.get(`/application-form-data/${applicationId}/${sectionName}`);
      return response.data;
    } catch (error) {
      if ((error as any)?.response?.status === 404) {
        return null; // Section doesn't exist yet
      }
      console.error(`Error fetching form section ${sectionName}:`, error);
      throw error;
    }
  },

  // Get all form data for an application
  async getApplicationFormData(applicationId: string): Promise<Record<string, any>> {
    try {
      const response = await apiClient.get(`/application-form-data/${applicationId}`);
      
      // Convert array of sections to object
      const formData: Record<string, any> = {};
      if (Array.isArray(response.data)) {
        response.data.forEach((section: ApplicationFormData) => {
          formData[section.section_name] = section.section_data;
        });
      }
      
      return formData;
    } catch (error) {
      console.error('Error fetching application form data:', error);
      return {}; // Return empty object if no data found
    }
  },

  // Delete form section
  async deleteFormSection(applicationId: string, sectionName: string): Promise<{ message: string }> {
    try {
      const response = await apiClient.delete(`/application-form-data/${applicationId}/${sectionName}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting form section ${sectionName}:`, error);
      throw error;
    }
  },

  // Save or update form section (upsert)
  async saveOrUpdateFormSection(
    applicationId: string,
    sectionName: string,
    sectionData: Record<string, any>
  ): Promise<ApplicationFormData> {
    try {
      // Try to get existing section first
      const existingSection = await this.getFormSection(applicationId, sectionName);
      
      if (existingSection) {
        // Update existing section
        return await this.updateFormSection(applicationId, sectionName, sectionData);
      } else {
        // Create new section
        return await this.saveFormSection(applicationId, sectionName, sectionData);
      }
    } catch (error) {
      console.error(`Error saving/updating form section ${sectionName}:`, error);
      throw error;
    }
  }
};
