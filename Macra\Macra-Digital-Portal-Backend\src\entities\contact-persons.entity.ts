import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEmail, IsOptional, IsUUID, IsBoolean, Length, Matches } from 'class-validator';
import { User } from './user.entity';
import { Applicants } from './applicant.entity';

@Entity('contact_persons')
export class ContactPersons {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  contact_id: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  applicant_id: string;

  @Column({ type: 'varchar', length: 100 })
  @IsString()
  @Length(1, 100)
  first_name: string;

  @Column({ type: 'varchar', length: 100 })
  @IsString()
  @Length(1, 100)
  last_name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  middle_name?: string;

  @Column({ type: 'varchar', length: 50 })
  @IsString()
  @Length(5, 50)
  designation: string;

  @Column({ type: 'varchar', length: 255 })
  @IsEmail()
  @Length(1, 255)
  email: string;

  @Column({ type: 'varchar', length: 20 })
  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid phone number format' })
  phone: string;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  is_primary: boolean;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applicants)
  @JoinColumn({ name: 'applicant_id' })
  applicant: Applicants;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.contact_id) {
      this.contact_id = uuidv4();
    }
  }
}
