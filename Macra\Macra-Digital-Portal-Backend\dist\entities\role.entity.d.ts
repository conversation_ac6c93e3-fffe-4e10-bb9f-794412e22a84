import { User } from './user.entity';
import { Permission } from './permission.entity';
export declare enum RoleName {
    CUSTOMER = "customer",
    ADMINISTRATOR = "administrator",
    EVALUATOR = "evaluator",
    LEGAL = "legal",
    ACCOUNTANT = "accountant",
    SALES = "sales",
    OTHER = "other",
    POSTAL = "postal",
    TELECOMMUNICATIONS = "telecommunications",
    STANDARDS = "standards",
    CLF = "clf"
}
export declare class Role {
    role_id: string;
    name: string;
    description?: string;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date;
    created_by?: string;
    updated_by?: string;
    creator?: User;
    updater?: User;
    users: User[];
    permissions: Permission[];
    generateId(): void;
}
