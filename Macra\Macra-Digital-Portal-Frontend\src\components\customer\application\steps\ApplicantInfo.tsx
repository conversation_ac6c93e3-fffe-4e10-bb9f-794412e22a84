'use client';

import React, { useState, useEffect, useCallback } from 'react';
import TextInput from '@/components/common/TextInput';
import Select from '@/components/common/Select';
import { validateSection } from '@/utils/formValidation';

interface ApplicantInfoProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  errors: Record<string, string>;
  licenseTypeId?: string;
  licenseCategoryId?: string;
  applicationId?: string;
  isLoading?: boolean;
}

const ApplicantInfo: React.FC<ApplicantInfoProps> = ({
  formData,
  onChange,
  onSave,
  errors,
  licenseTypeId,
  licenseCategoryId,
  applicationId,
  isLoading = false
}) => {
  const [localData, setLocalData] = useState({
    applicant_type: '',
    first_name: '',
    last_name: '',
    middle_name: '',
    email: '',
    phone: '',
    national_id: '',
    date_of_birth: '',
    nationality: 'Malawian',
    gender: '',
    postal_address: '',
    physical_address: '',
    city: '',
    district: '',
    postal_code: '',
    ...formData
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Sync formData to localData only when formData changes and is different
  useEffect(() => {
    if (formData && Object.keys(formData).length > 0) {
      setLocalData((prev: any) => {
        const newData = { ...prev, ...formData };
        // Only update if there are actual changes to prevent infinite loops
        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);
        return hasChanges ? newData : prev;
      });
    }
  }, [formData]);

  // Handle local changes
  const handleLocalChange = useCallback((field: string, value: any) => {
    setLocalData((prev: any) => ({ ...prev, [field]: value }));

    // Call onChange with the field and value
    if (onChange) {
      onChange(field, value);
    }

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }
  }, [onChange, validationErrors]);

  // Validate form data
  const validateForm = () => {
    const validation = validateSection(localData, 'applicantInfo');
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  // Handle save
  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      // This will create the application if it doesn't exist
      const savedApplicationId = await onSave(localData);
      console.log('Applicant info saved, application created:', savedApplicationId);
    } catch (error) {
      console.error('Error saving applicant info:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Auto-save on blur
  const handleBlur = (field: string) => {
    if (localData[field] && !validationErrors[field]) {
      // Auto-save individual field changes
      onChange(field, localData[field]);
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Applicant Information
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Please provide your personal information. This will create your application record.
        </p>
        {!applicationId && (
          <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <i className="ri-information-line mr-1"></i>
              Your application will be created when you save this step.
            </p>
          </div>
        )}
        {applicationId && (
          <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <p className="text-sm text-green-700 dark:text-green-300">
              <i className="ri-check-line mr-1"></i>
              Application created: {applicationId.slice(0, 8)}...
            </p>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Applicant Type */}
        <div className="md:col-span-2">
          <Select
            label="Applicant Type"
            value={localData.applicant_type || ''}
            onChange={(value) => handleLocalChange('applicant_type', value)}
            options={[
              { value: 'individual', label: 'Individual' },
              { value: 'company', label: 'Company' },
              { value: 'organization', label: 'Organization' }
            ]}
            required
            error={validationErrors.applicant_type || errors.applicant_type}
          />
        </div>

        {/* Personal Information */}
        <TextInput
          label="First Name"
          value={localData.first_name || ''}
          onChange={(e) => handleLocalChange('first_name', e.target.value)}
          onBlur={() => handleBlur('first_name')}
          required
          error={validationErrors.first_name || errors.first_name}
        />

        <TextInput
          label="Last Name"
          value={localData.last_name || ''}
          onChange={(e) => handleLocalChange('last_name', e.target.value)}
          onBlur={() => handleBlur('last_name')}
          required
          error={validationErrors.last_name || errors.last_name}
        />

        <TextInput
          label="Middle Name"
          value={localData.middle_name || ''}
          onChange={(e) => handleLocalChange('middle_name', e.target.value)}
          onBlur={() => handleBlur('middle_name')}
          error={validationErrors.middle_name || errors.middle_name}
        />

        <Select
          label="Gender"
          value={localData.gender || ''}
          onChange={(value) => handleLocalChange('gender', value)}
          options={[
            { value: 'male', label: 'Male' },
            { value: 'female', label: 'Female' },
            { value: 'other', label: 'Other' }
          ]}
          required
          error={validationErrors.gender || errors.gender}
        />

        {/* Contact Information */}
        <TextInput
          label="Email Address"
          type="email"
          value={localData.email || ''}
          onChange={(e) => handleLocalChange('email', e.target.value)}
          onBlur={() => handleBlur('email')}
          required
          error={validationErrors.email || errors.email}
        />

        <TextInput
          label="Phone Number"
          value={localData.phone || ''}
          onChange={(e) => handleLocalChange('phone', e.target.value)}
          onBlur={() => handleBlur('phone')}
          placeholder="+265 1 234 567"
          required
          error={validationErrors.phone || errors.phone}
        />

        {/* Identification */}
        <TextInput
          label="National ID"
          value={localData.national_id || ''}
          onChange={(e) => handleLocalChange('national_id', e.target.value)}
          onBlur={() => handleBlur('national_id')}
          placeholder="**********"
          required
          error={validationErrors.national_id || errors.national_id}
        />

        <TextInput
          label="Date of Birth"
          type="date"
          value={localData.date_of_birth || ''}
          onChange={(e) => handleLocalChange('date_of_birth', e.target.value)}
          onBlur={() => handleBlur('date_of_birth')}
          required
          error={validationErrors.date_of_birth || errors.date_of_birth}
        />

        <Select
          label="Nationality"
          value={localData.nationality || 'Malawian'}
          onChange={(value) => handleLocalChange('nationality', value)}
          options={[
            { value: 'Malawian', label: 'Malawian' },
            { value: 'Other', label: 'Other' }
          ]}
          required
          error={validationErrors.nationality || errors.nationality}
        />
      </div>

      {/* Address Information */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Address Information
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <TextInput
              label="Postal Address"
              value={localData.postal_address || ''}
              onChange={(e) => handleLocalChange('postal_address', e.target.value)}
              onBlur={() => handleBlur('postal_address')}
              placeholder="P.O. Box 123"
              required
              error={validationErrors.postal_address || errors.postal_address}
            />
          </div>

          <div className="md:col-span-2">
            <TextInput
              label="Physical Address"
              value={localData.physical_address || ''}
              onChange={(e) => handleLocalChange('physical_address', e.target.value)}
              onBlur={() => handleBlur('physical_address')}
              placeholder="Street address"
              required
              error={validationErrors.physical_address || errors.physical_address}
            />
          </div>

          <TextInput
            label="City"
            value={localData.city || ''}
            onChange={(e) => handleLocalChange('city', e.target.value)}
            onBlur={() => handleBlur('city')}
            required
            error={validationErrors.city || errors.city}
          />

          <Select
            label="District"
            value={localData.district || ''}
            onChange={(value) => handleLocalChange('district', value)}
            options={[
              { value: 'Blantyre', label: 'Blantyre' },
              { value: 'Lilongwe', label: 'Lilongwe' },
              { value: 'Mzuzu', label: 'Mzuzu' },
              { value: 'Zomba', label: 'Zomba' },
              { value: 'Other', label: 'Other' }
            ]}
            required
            error={validationErrors.district || errors.district}
          />

          <TextInput
            label="Postal Code"
            value={localData.postal_code || ''}
            onChange={(e) => handleLocalChange('postal_code', e.target.value)}
            onBlur={() => handleBlur('postal_code')}
            error={validationErrors.postal_code || errors.postal_code}
          />
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving || isLoading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              {applicationId ? 'Saving...' : 'Creating Application...'}
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              {applicationId ? 'Save Changes' : 'Create Application'}
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ApplicantInfo;
