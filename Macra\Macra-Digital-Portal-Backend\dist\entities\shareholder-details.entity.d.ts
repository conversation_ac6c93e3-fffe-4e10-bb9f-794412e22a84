import { User } from './user.entity';
import { Stakeholders } from './stakeholders.entity';
export declare class ShareholderDetails {
    shareholder_id: string;
    stakeholder_id: string;
    shareholding_percent: number;
    description?: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    stakeholder: Stakeholders;
    creator: User;
    updater?: User;
    generateId(): void;
}
