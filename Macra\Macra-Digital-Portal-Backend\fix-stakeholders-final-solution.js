const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixStakeholdersFinalSolution() {
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'macra_db'
    });

    try {
        console.log('🔧 Final fix for stakeholders table...\n');
        
        // 1. Drop existing foreign key constraints if they exist
        console.log('1. Cleaning up existing constraints...');
        
        const constraintsToDelete = [
            'FK_75516ad5098e0aada3ffe364bf2',
            'FK_5525cda345b76b7633dba45bc3d',
            'FK_5518ae16ebb22019f47827a3127',
            'FK_fd8bbabbc325fbacdcb8c2001ec'
        ];

        for (const constraint of constraintsToDelete) {
            try {
                await connection.execute(`ALTER TABLE stakeholders DROP FOREIGN KEY ${constraint}`);
                console.log(`  ✅ Dropped constraint: ${constraint}`);
            } catch (error) {
                if (error.code === 'ER_CANT_DROP_FIELD_OR_KEY') {
                    console.log(`  ⚠️ Constraint ${constraint} does not exist`);
                } else {
                    console.log(`  ⚠️ Error dropping ${constraint}: ${error.message}`);
                }
            }
        }

        // 2. Modify columns to ensure proper nullability and types
        console.log('\n2. Modifying column definitions...');
        
        console.log('  Making contact_id nullable...');
        await connection.execute(`
            ALTER TABLE stakeholders 
            MODIFY COLUMN contact_id VARCHAR(36) NULL
        `);

        console.log('  Making created_by nullable...');
        await connection.execute(`
            ALTER TABLE stakeholders 
            MODIFY COLUMN created_by VARCHAR(36) NULL
        `);

        console.log('  Making updated_by nullable...');
        await connection.execute(`
            ALTER TABLE stakeholders 
            MODIFY COLUMN updated_by VARCHAR(36) NULL
        `);

        console.log('  Making cv_document_id nullable...');
        await connection.execute(`
            ALTER TABLE stakeholders 
            MODIFY COLUMN cv_document_id VARCHAR(36) NULL
        `);

        // 3. Re-create foreign key constraints
        console.log('\n3. Creating foreign key constraints...');
        
        try {
            console.log('  Adding contact_id foreign key...');
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT FK_stakeholders_contact_id 
                FOREIGN KEY (contact_id) REFERENCES contacts(contact_id) 
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('  ✅ contact_id foreign key added');
        } catch (error) {
            console.log(`  ❌ Error adding contact_id FK: ${error.message}`);
        }

        try {
            console.log('  Adding created_by foreign key...');
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT FK_stakeholders_created_by 
                FOREIGN KEY (created_by) REFERENCES users(user_id) 
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('  ✅ created_by foreign key added');
        } catch (error) {
            console.log(`  ❌ Error adding created_by FK: ${error.message}`);
        }

        try {
            console.log('  Adding updated_by foreign key...');
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT FK_stakeholders_updated_by 
                FOREIGN KEY (updated_by) REFERENCES users(user_id) 
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('  ✅ updated_by foreign key added');
        } catch (error) {
            console.log(`  ❌ Error adding updated_by FK: ${error.message}`);
        }

        try {
            console.log('  Adding applicant_id foreign key...');
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT FK_stakeholders_applicant_id 
                FOREIGN KEY (applicant_id) REFERENCES applicants(applicant_id) 
                ON DELETE CASCADE ON UPDATE NO ACTION
            `);
            console.log('  ✅ applicant_id foreign key added');
        } catch (error) {
            console.log(`  ❌ Error adding applicant_id FK: ${error.message}`);
        }

        // 4. Verify the final state
        console.log('\n4. Verifying final state...');
        
        const [finalConstraints] = await connection.execute(`
            SELECT 
                kcu.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME,
                rc.DELETE_RULE,
                rc.UPDATE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
                ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
            WHERE kcu.TABLE_SCHEMA = ? 
            AND kcu.TABLE_NAME = 'stakeholders'
            AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        `, [process.env.DB_NAME || 'macra_db']);
        
        console.log('Final foreign key constraints:');
        finalConstraints.forEach(constraint => {
            console.log(`  ✅ ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME} (DELETE: ${constraint.DELETE_RULE})`);
        });

        console.log('\n🎉 Stakeholders table successfully fixed!');
        
    } catch (error) {
        console.error('❌ Error during final fix:', error);
        throw error;
    } finally {
        await connection.end();
    }
}

fixStakeholdersFinalSolution();