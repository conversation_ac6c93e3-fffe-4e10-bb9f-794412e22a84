'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useApplications, useApplicants, CreateApplicantData, CreateApplicationData, ShareholderType, DirectorType, ManagementTeamType, useAddresses } from '@/hooks/useApplications';
import { usePostalCourierLicenses } from '@/hooks/useLicenseData';
import { CountryDropdown, TextInput, TextArea } from '@/components/forms';
import { CreateAddressData }  from "@/lib/customer-api";

interface FormData {
  // Step 1: Applicant Information
  applicantName: string;
  // Postal Address
  postalPoBox: string;
  postalCity: string;
  postalCountry: string;
  // Physical Address
  physicalStreet: string;
  physicalCity: string;
  physicalCountry: string;
  telephone: string;
  fax: string;
  email: string;
  
  // Step 2: License Type
  licenseType: string;
  
  // Step 3: Company Profile
  shareholders: Array<{
    name: string;
    nationality: string;
    address: string;
    shareholding: string;
  }>;
  directors: Array<{
    name: string;
    nationality: string;
    address: string;
  }>;
  foreignOwnership: string;
  businessRegistrationNo: string;
  tpin: string;
  website: string;
  dateOfIncorporation: string;
  placeOfIncorporation: string;
  
  // Step 4: Management Team
  ceoName: string;
  ceoAddress: string;
  ceoNationality: string;
  ceoQualifications: string;
  ceoExperience: string;
  managementTeam: Array<{
    name: string;
    address: string;
    nationality: string;
    qualifications: string;
  }>;
  
  // Step 5: Professional Services
  externalAuditors: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
  };
  lawyers: {
    name: string;
    address: string;
    phone: string;
    email: string;
    website: string;
  };
  
  // Step 6: Business Information
  otherCourierShareholding: string;
  insuranceCover: string;
  
  // Step 7: Service Scope
  serviceNature: string;
  premises: string;
  transportNature: string;
  customerAssistance: string;
  
  // Step 8: Business Plan
  projectProposal: File | null;
  businessPlan: File | null;
  
  // Step 9: Legal History
  censured: boolean;
  censuredDetails: string;
  disciplined: boolean;
  disciplinedDetails: string;
  penalized: boolean;
  penalizedDetails: string;
  suspended: boolean;
  suspendedDetails: string;
  prosecuted: boolean;
  prosecutedDetails: string;
  convicted: boolean;
  convictedDetails: string;
  investigated: boolean;
  investigatedDetails: string;
  litigation: boolean;
  litigationDetails: string;
  judgmentDebt: boolean;
  judgmentDetails: string;
  insolvent: boolean;
  insolventDetails: string;
  liquidator: boolean;
  liquidatorDetails: string;
  compromise: boolean;
  compromiseDetails: string;
  voluntaryWinding: boolean;
  insolvencyDetails: string;
}

const initialAddressData: CreateAddressData = {
  address_type: 'postal',
  address_origin: 'applicant',
  address_line_1: '',
  address_line_2: '',
  postal_code: '',
  country: '',
  city: ''
};

// Union type for array fields
type ArrayFieldType = ShareholderType[] | DirectorType[] | ManagementTeamType[];

const CourierApplicationForm = () => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Backend integration hooks
  const { createApplication, updateApplicationProgress, submitApplication, applicationService } = useApplications();
  const { createAddress, editAddress, addressService, debouncedSearchPostcodes,postcodeSuggestions } = useAddresses();
  const { createApplicant } = useApplicants();
  const { categories, loading: licenseLoading, error: licenseError } = usePostalCourierLicenses();

  // Get license category from URL params
  const licenseCategoryId = searchParams.get('category') || '';
  const selectedCategory = categories.find(cat => cat.license_category_id === licenseCategoryId);

  // Get continue parameters for resuming applications
  const continueApplicationId = searchParams.get('continue');
  const continueStep = searchParams.get('step');
  const isContinuing = !!continueApplicationId;

  // Redirect to new standardized system if category is found
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    if (selectedCategory && selectedCategory.license_type) {
      // Redirect to the new standardized application system
      router.push(`/customer/applications/${selectedCategory.license_type.license_type_id}/${licenseCategoryId}`);
      return;
    }
  }, [authLoading, isAuthenticated, selectedCategory, licenseCategoryId, router]);

  // Determine if this is a postal application
  const isPostalApplication = selectedCategory?.name.toLowerCase().includes('postal') || false;

  const [currentStep, setCurrentStep] = useState(() => {
    // If continuing an application, start at the specified step
    if (isContinuing && continueStep) {
      const step = parseInt(continueStep, 10);
      return step >= 1 && step <= 10 ? step : 1;
    }
    return 1;
  });
  const [applicationId, setApplicationId] = useState<string | null>(continueApplicationId || null);
  const [applicantId, setApplicantId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [region, setRegion] = useState('');
  const [addressData, setAddressData] = useState<CreateAddressData>(initialAddressData);
  const [formData, setFormData] = useState<FormData>({
    applicantName: '',
    // Postal Address
    postalPoBox: '',
    postalCity: '',
    postalCountry: '',
    // Physical Address
    physicalStreet: '',
    physicalCity: '',
    physicalCountry: '',
    telephone: '',
    fax: '',
    email: '',
    licenseType: selectedCategory?.license_category_id || '',
    shareholders: [{ name: '', nationality: '', address: '', shareholding: '' }],
    directors: [{ name: '', nationality: '', address: '' }],
    foreignOwnership: '',
    businessRegistrationNo: '',
    tpin: '',
    website: '',
    dateOfIncorporation: '',
    placeOfIncorporation: '',
    ceoName: '',
    ceoAddress: '',
    ceoNationality: '',
    ceoQualifications: '',
    ceoExperience: '',
    managementTeam: [{ name: '', address: '', nationality: '', qualifications: '' }],
    externalAuditors: {
      name: '',
      address: '',
      phone: '',
      email: '',
      website: ''
    },
    lawyers: {
      name: '',
      address: '',
      phone: '',
      email: '',
      website: ''
    },
    otherCourierShareholding: '',
    insuranceCover: '',
    serviceNature: '',
    premises: '',
    transportNature: '',
    customerAssistance: '',
    projectProposal: null,
    businessPlan: null,
    censured: false,
    censuredDetails: '',
    disciplined: false,
    disciplinedDetails: '',
    penalized: false,
    penalizedDetails: '',
    suspended: false,
    suspendedDetails: '',
    prosecuted: false,
    prosecutedDetails: '',
    convicted: false,
    convictedDetails: '',
    investigated: false,
    investigatedDetails: '',
    litigation: false,
    litigationDetails: '',
    judgmentDebt: false,
    judgmentDetails: '',
    insolvent: false,
    insolventDetails: '',
    liquidator: false,
    liquidatorDetails: '',
    compromise: false,
    compromiseDetails: '',
    voluntaryWinding: false,
    insolvencyDetails: ''
  });

  const totalSteps = 10;
  console.log('Address Data', addressData);
  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Set license type for postal applications
  useEffect(() => {
    if (selectedCategory && !formData.licenseType) {
      setFormData(prev => ({
        ...prev,
        licenseType: selectedCategory.license_category_id
      }));
    }
  }, [selectedCategory, formData.licenseType]);

  // Load existing application data when continuing
  useEffect(() => {
    const loadExistingApplication = async () => {
      if (isContinuing && continueApplicationId) {
        try {
          console.log(`Loading existing application data for ID: ${continueApplicationId}`);

          // TODO: Add API call to load existing application data
          // const existingApplication = await applicationService.getApplication(continueApplicationId);
          // if (existingApplication) {
          //   // Populate form data with existing application data
          //   setFormData(existingApplication.formData);
          //   setApplicantId(existingApplication.applicant_id);
          // }

          console.log(`Continuing application at step ${currentStep}`);
        } catch (error) {
          console.error('Error loading existing application:', error);
          // If loading fails, start from step 1
          setCurrentStep(1);
        }
      }
    };

    loadExistingApplication();
  }, [isContinuing, continueApplicationId, currentStep]);

  // License type mapping
  const licenseTypeNames: { [key: string]: string } = {
    'international-commercial-a': 'International Commercial Courier Service License Category A',
    'international-commercial-b': 'International Commercial Courier Service License Category B',
    'domestic-commercial': 'Domestic Commercial Courier Service License',
    'domestic-inter-city': 'Domestic Inter-City Commercial Courier Service License',
    'domestic-intra-city': 'Domestic Intra-city Commercial Courier Service License',
    'district-commercial': 'District Commercial Courier Service License',
    'postal-operator-license': 'Postal Operator License'
  };

  // Phone number validation and formatting
  const formatPhoneNumber = (phone: string): string => {
    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '');

    // Ensure it starts with + if it doesn't already and has digits
    if (cleaned && !cleaned.startsWith('+') && cleaned.length > 0) {
      return '+265' + cleaned;
    }

    return cleaned;
  };

  const validatePhoneNumber = (phone: string): boolean => {
    // Phone should be 10-20 characters and match the pattern
    const phoneRegex = /^[+]?[\d\s\-()]+$/;
    return phone.length >= 10 && phone.length <= 20 && phoneRegex.test(phone);
  };

  const validateUUID = (uuid: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  };

  // Handle form input changes
  const handleInputChange = (field: string, value: string | boolean | File | null) => {
    // Special handling for phone numbers
    if ((field === 'telephone' || field === 'fax') && typeof value === 'string') {
      value = formatPhoneNumber(value);
    }

    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle array field changes
  const handleArrayFieldChange = (arrayName: string, index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: (prev[arrayName as keyof FormData] as ArrayFieldType).map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  // Handle professional services field changes
  const handleProfessionalServiceChange = (serviceName: 'externalAuditors' | 'lawyers', field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [serviceName]: {
        ...prev[serviceName],
        [field]: value
      }
    }));
  };

  // Add array item
  const addArrayItem = (arrayName: string, template: ShareholderType | DirectorType | ManagementTeamType) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: [...(prev[arrayName as keyof FormData] as ArrayFieldType), template]
    }));
  };

  // Remove array item
  const removeArrayItem = (arrayName: string, index: number) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: (prev[arrayName as keyof FormData] as ArrayFieldType).filter((_, i) => i !== index)
    }));
  };

  // Handle file upload
  const handleFileUpload = (field: string, file: File | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: file
    }));
  };

  // Navigate to next step
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  // Navigate to previous step
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedCategory) {
      alert('Please select a valid license category');
      return;
    }

    setIsSubmitting(true);

    try {
      // Step 1: Create or update applicant
      let currentApplicantId = applicantId;

      if (!currentApplicantId) {
        // Validate required fields before sending
        if (!formData.applicantName || !formData.businessRegistrationNo || !formData.tpin ||
            !formData.email || !formData.telephone || !formData.dateOfIncorporation ||
            !formData.placeOfIncorporation) {
          alert('Please fill in all required fields before submitting.');
          return;
        }

        // Validate phone number format
        if (!validatePhoneNumber(formData.telephone)) {
          alert('Please enter a valid phone number (10-20 characters, format: +265123456789).');
          return;
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
          alert('Please enter a valid email address.');
          return;
        }

        // First, check if applicant already exists
        try {
          const existingApplicants = await applicationService.getApplicants();
          const existingApplicant = existingApplicants.data.find(
            (app: any) =>
              app.email === formData.email ||
              app.business_registration_number === formData.businessRegistrationNo ||
              app.tpin === formData.tpin
          );

          if (existingApplicant) {
            currentApplicantId = existingApplicant.applicant_id;
            setApplicantId(currentApplicantId);
            console.log('Found existing applicant:', existingApplicant);
          }
        } catch (fetchError) {
          console.warn('Could not check for existing applicants:', fetchError);
          // Continue with creation attempt
        }

        // Only create applicant if we didn't find an existing one
        if (!currentApplicantId) {
          const applicantData: CreateApplicantData = {
            name: formData.applicantName,
            business_registration_number: formData.businessRegistrationNo,
            tpin: formData.tpin,
            website: formData.website || 'https://www.company.com', // Backend requires website
            email: formData.email,
            phone: formData.telephone,
            fax: formData.fax || undefined, // Send undefined if empty
            level_of_insurance_cover: formData.insuranceCover || undefined,
            date_incorporation: new Date(formData.dateOfIncorporation).toISOString(), // Convert to ISO string
            place_incorporation: formData.placeOfIncorporation,
          };

          console.log('Creating new applicant with data:', applicantData);

          try {
            const newApplicant = await createApplicant(applicantData);
            currentApplicantId = newApplicant.applicant_id;
            setApplicantId(currentApplicantId);
            console.log('Created new applicant:', newApplicant);
          } catch (applicantError: any) {
            console.error('Applicant creation error:', applicantError);

            // Handle 409 conflict (applicant already exists) - fallback
            if (applicantError.response?.status === 409) {
              alert('An applicant with this email, business registration number, or TPIN already exists. Please use different details or contact support.');
              return;
            } else {
              throw applicantError; // Re-throw other errors
            }
          }
        }
      }

      // Step 2: Create or update application
      let currentApplicationId = applicationId;

      if (!currentApplicationId) {
        const applicationNumber = applicationService.generateApplicationNumber(
          selectedCategory.name.toLowerCase().includes('postal') ? 'PST' : 'COU'
        );

        // Ensure step and progress are within valid ranges
        const validCurrentStep = Math.min(Math.max(currentStep, 1), 6); // Between 1 and 6
        const validProgressPercentage = Math.min(Math.round((validCurrentStep / 6) * 100), 100); // Max 100%

        console.log('Current step:', currentStep, 'Valid step:', validCurrentStep, 'Progress:', validProgressPercentage);

        const applicationData: CreateApplicationData = {
          application_number: applicationNumber,
          applicant_id: currentApplicantId,
          license_category_id: selectedCategory.license_category_id,
          status: 'submitted',
          current_step: validCurrentStep,
          progress_percentage: validProgressPercentage,
        };

        // Validate UUIDs before sending
        if (!validateUUID(currentApplicantId)) {
          alert('Invalid applicant ID format. Please try again.');
          return;
        }

        if (!validateUUID(selectedCategory.license_category_id)) {
          alert('Invalid license category ID format. Please try again.');
          return;
        }

        console.log('Sending application data:', applicationData);
        console.log('Selected category:', selectedCategory);

        const newApplication = await createApplication(applicationData);
        currentApplicationId = newApplication.application_id;
        setApplicationId(currentApplicationId);
      } else {
        // Update progress with valid ranges
        const validCurrentStep = Math.min(Math.max(currentStep, 1), 6);
        const validProgressPercentage = Math.min(Math.round((validCurrentStep / 6) * 100), 100);

        await updateApplicationProgress(
          currentApplicationId,
          validCurrentStep,
          validProgressPercentage
        );
      }

      // Step 3: If this is the final step, submit the application
      if (currentStep === 6) {
        await submitApplication(currentApplicationId);
        alert('Application submitted successfully! You will receive a confirmation email shortly.');
        router.push('/customer/my-licenses');
      } else {
        // Move to next step
        setCurrentStep(currentStep + 1);
      }

    } catch (error: any) {
      console.error('Form submission error:', error);
      console.error('Error response:', error.response?.data);

      let errorMessage = 'Failed to submit application';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(`${errorMessage}. Please check the console for more details.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Breadcrumb items
  const breadcrumbs = [
    { label: 'Dashboard', href: '/customer' },
    { label: 'Applications', href: '/customer/applications' },
    { label: isPostalApplication ? 'Postal Services' : 'Courier Services', href: '/customer/applications/courier' },
    { label: 'New Application' }
  ];

  // Show loading state
  if (authLoading || licenseLoading) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading application form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Show error state
  if (licenseError) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <i className="ri-error-warning-line text-4xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Failed to load license information
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{licenseError}</p>
            <button
              type="button"
              onClick={() => window.location.reload()}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Show error if no category selected
  if (!selectedCategory) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-orange-600 dark:text-orange-400 mb-4">
              <i className="ri-alert-line text-4xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No License Category Selected
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Please select a license category to continue with your application.
            </p>
            <button
              type="button"
              onClick={() => router.push('/customer/applications/courier')}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Select License Category
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout breadcrumbs={breadcrumbs}>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {selectedCategory?.name || 'License Application'}
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            {selectedCategory?.description || 'Complete your license application'}
          </p>
          {selectedCategory && (
            <div className="mt-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-center">
                <i className="ri-information-line text-blue-600 dark:text-blue-400 mr-2"></i>
                <div>
                  <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                    License Category: {selectedCategory.name}
                  </p>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Fee: {selectedCategory.fee} | Authorization: {selectedCategory.authorizes || 'As specified in license terms'}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Continue Application Indicator */}
          {isContinuing && (
            <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex items-center">
                <i className="ri-play-circle-line text-green-600 dark:text-green-400 text-lg mr-3"></i>
                <div>
                  <h3 className="text-sm font-medium text-green-900 dark:text-green-100">
                    Continuing Application
                  </h3>
                  <p className="text-green-700 dark:text-green-300 text-sm mt-1">
                    You are continuing your application from Step {currentStep}. Your previous progress has been saved.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {Array.from({ length: totalSteps }, (_, i) => i + 1).map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center ${
                  step === currentStep
                    ? 'bg-primary'
                    : step < currentStep
                    ? 'bg-green-500'
                    : 'bg-gray-200 dark:bg-gray-700'
                }`}>
                  {step < currentStep && (
                    <i className="ri-check-line text-white text-xs md:text-sm"></i>
                  )}
                </div>
                {step < totalSteps && (
                  <div className={`flex-1 h-0.5 mx-1 md:mx-2 ${
                    step < currentStep ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'
                  }`}></div>
                )}
              </div>
            ))}
          </div>
          <div className="hidden md:flex justify-between text-xs text-gray-500 mt-2">
            <span>Applicant Info</span>
            <span>Company Profile</span>
            <span>Management</span>
            <span>Professional Services</span>
            <span>Business Info</span>
            <span>Service Scope</span>
            <span>Business Plan</span>
            <span>Legal History</span>
            <span>Review & Submit</span>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <form onSubmit={handleSubmit} className="p-6">
            {/* Step 1: Applicant Information */}
            {currentStep === 1 && (
              <div className="space-y-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Applicant Information</h2>

                {/* Basic Information */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    <i className="ri-user-line mr-2"></i>
                    Basic Information
                  </h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="lg:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name of Applicant *</label>
                      <TextArea
                        value={formData.applicantName}
                        onChange={(e) => handleInputChange('applicantName', e.target.value)}
                        rows={2}
                        placeholder="Enter full name of applicant organization"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Telephone *</label>
                      <TextInput
                        type="tel"
                        value={formData.telephone}
                        onChange={(e) => handleInputChange('telephone', e.target.value)}
                        placeholder="+265 123 456 789"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address *</label>
                      <TextInput
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Fax</label>
                      <TextInput
                        type="tel"
                        value={formData.fax}
                        onChange={(e) => handleInputChange('fax', e.target.value)}
                        placeholder="+265 123 456 789"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Business Registration Number *</label>
                      <TextInput
                        type="text"
                        value={formData.businessRegistrationNo}
                        onChange={(e) => handleInputChange('businessRegistrationNo', e.target.value)}
                        placeholder="BRS/12345/2024"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">TPIN *</label>
                      <TextInput
                        type="text"
                        value={formData.tpin}
                        onChange={(e) => handleInputChange('tpin', e.target.value)}
                        placeholder="1234567890"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Website</label>
                      <TextInput
                        type="url"
                        value={formData.website}
                        onChange={(e) => handleInputChange('website', e.target.value)}
                        placeholder="https://www.company.com"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date of Incorporation *</label>
                      <TextInput
                        type="date"
                        value={formData.dateOfIncorporation}
                        onChange={(e) => handleInputChange('dateOfIncorporation', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Place of Incorporation *</label>
                      <TextInput
                        type="text"
                        value={formData.placeOfIncorporation}
                        onChange={(e) => handleInputChange('placeOfIncorporation', e.target.value)}
                        placeholder="Blantyre, Malawi"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Address Information */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Postal Address */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                      <i className="ri-mail-line mr-2"></i>
                      Postal Address
                    </h3>
                    <div className="space-y-4">
                      {/* Country Dropdown (Always Visible) */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Country *</label>
                        <CountryDropdown
                          value={addressData.country}
                          onChange={(value) => {
                            setAddressData(prev => ({ ...prev, country: value }));
                            if (value === 'Malawi') {
                              setRegion('');
                              setAddressData(prev => ({ ...prev, city: '', postal_code: '', address_line_1: '', address_line_2: '' }));
                            }
                          }}
                          className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                          required
                        />
                      </div>
                      {addressData.country === 'Malawi' ? (
                        <>
                          {/* Region Dropdown */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Region *</label>
                            <select
                              className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                              value={region || ''}
                              onChange={(e) => {
                                const selectedRegion = e.target.value;
                                setRegion(selectedRegion);
                                setAddressData(prev => ({
                                  ...prev,
                                  country: 'Malawi',
                                  city: '',
                                  postal_code: '',
                                  address_line_1: '',
                                  address_line_2: ''
                                }));
                                debouncedSearchPostcodes({ region: selectedRegion });
                              }}
                              required
                            >
                              <option value="">Select a region</option>
                              {['Northern', 'Central', 'Southern'].map(region => (
                                <option key={region} value={region}>{region}</option>
                              ))}
                            </select>
                          </div>

                          {/* District Dropdown */}
                          {region && (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">District *</label>
                              <select
                                className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                                value={addressData.city || ''}
                                onChange={(e) => {
                                  const selectedDistrict = e.target.value;
                                  setAddressData(prev => ({
                                    ...prev,
                                    city: selectedDistrict,
                                    postal_code: '',
                                    address_line_1: '',
                                    address_line_2: ''
                                  }));
                                  debouncedSearchPostcodes({ region, district: selectedDistrict });
                                }}
                                required
                              >
                                <option value="">Select a district</option>
                                {[...new Set(postcodeSuggestions
                                  .filter(p => p.region === region)
                                  .map(p => p.district))].map(d => (
                                  <option key={d} value={d}>{d}</option>
                                ))}
                              </select>
                            </div>
                          )}

                          {/* Location Dropdown */}
                          {addressData.city && (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location *</label>
                              <select
                                className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                                value={addressData.postal_code || ''}
                                onChange={(e) => {
                                  const selectedLocation = e.target.value;
                                  const match = postcodeSuggestions.find(p =>
                                    p.region === region &&
                                    p.district === addressData.city &&
                                    p.postal_code === selectedLocation
                                  );
                                  if (match) {
                                    setAddressData(prev => ({ ...prev, postal_code: match.postal_code }));
                                  }
                                }}
                                required
                                disabled={!!addressData.postal_code}
                              >
                                <option value="">Select a location</option>
                                {postcodeSuggestions
                                  .filter(p =>
                                    p.region === region &&
                                    p.district === addressData.city)
                                  .map(loc => (
                                    <option key={loc.postal_code_id} value={loc.postal_code}>{loc.location}</option>
                                  ))}
                              </select>
                            </div>
                          )}

                          {/* Postal Code Display */}
                          {addressData.postal_code && (
                            <div>
                              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Postal Code *</label>
                              <TextInput
                                value={addressData.postal_code}
                                readOnly
                                disabled
                                className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                              />
                            </div>
                          )}

                          {/* Address Line 1 & 2 */}
                          {addressData.postal_code && (
                            <>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Line 1 *</label>
                                <TextInput
                                  placeholder="e.g., P.O. Box or P/Bag"
                                  value={addressData.address_line_1}
                                  onChange={(e) => setAddressData(prev => ({ ...prev, address_line_1: e.target.value }))}
                                  required
                                  className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Line 2</label>
                                <TextInput
                                  placeholder="Optional details like Building Number, Block, etc."
                                  value={addressData.address_line_2}
                                  onChange={(e) => setAddressData(prev => ({ ...prev, address_line_2: e.target.value }))}
                                  className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                                />
                              </div>
                            </>
                          )}
                        </>
                      ) : (
                        <>
                          {/* Manual entry for non-Malawi countries */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Postal Code *</label>
                            <TextInput
                              value={addressData.postal_code}
                              onChange={(e) => setAddressData(prev => ({ ...prev, postal_code: e.target.value }))}
                              required
                              className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">City *</label>
                            <TextInput
                              value={addressData.city}
                              onChange={(e) => setAddressData(prev => ({ ...prev, city: e.target.value }))}
                              required
                              className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Line 1 *</label>
                            <TextInput
                              value={addressData.address_line_1}
                              onChange={(e) => setAddressData(prev => ({ ...prev, address_line_1: e.target.value }))}
                              required
                              className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Line 2</label>
                            <TextInput
                              value={addressData.address_line_2}
                              onChange={(e) => setAddressData(prev => ({ ...prev, address_line_2: e.target.value }))}
                              className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                            />
                          </div>
                        </>
                      )}
                    </div>

                  </div>

                  {/* Physical Address */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                      <i className="ri-building-line mr-2"></i>
                      Physical Address
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Street Address *</label>
                        <TextInput
                          type="text"
                          value={formData.physicalStreet}
                          onChange={(e) => handleInputChange('physicalStreet', e.target.value)}
                          placeholder="123 Main Street"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">City *</label>
                        <TextInput
                          type="text"
                          value={formData.physicalCity}
                          onChange={(e) => handleInputChange('physicalCity', e.target.value)}
                          placeholder="Enter city"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Country *</label>
                        <CountryDropdown
                          value={formData.physicalCountry}
                          onChange={(value) => handleInputChange('physicalCountry', value)}
                          required
                          className="text-sm"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: License Type */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  {isPostalApplication ? 'Postal Service Applied For' : 'Courier Service Applied For'}
                </h2>

                <div className="space-y-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {isPostalApplication
                      ? 'Confirm the postal service license you are applying for:'
                      : 'Select the type of courier service license you are applying for:'
                    }
                  </p>

                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center">
                      <i className="ri-check-line text-green-600 dark:text-green-400 mr-3"></i>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {selectedCategory?.name}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {selectedCategory?.description}
                        </p>
                        <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                          Fee: {selectedCategory?.fee}
                        </p>
                      </div>
                    </div>
                    <input
                      type="hidden"
                      name="licenseType"
                      value={selectedCategory?.license_category_id || ''}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Company Profile */}
            {currentStep === 3 && (
              <div className="space-y-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Company Profile</h2>

                {/* Business Registration */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    <i className="ri-building-2-line mr-2"></i>
                    Business Registration
                  </h3>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Business Registration Certificate No. *
                    </label>
                    <TextInput
                      type="text"
                      value={formData.businessRegistrationNo}
                      onChange={(e) => handleInputChange('businessRegistrationNo', e.target.value)}
                      placeholder="Enter business registration certificate number"
                      required
                    />
                  </div>
                </div>

                  {/* Shareholders */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        <i className="ri-group-line mr-2"></i>
                        Shareholders
                      </h3>
                      <button
                        type="button"
                        onClick={() => addArrayItem('shareholders', { name: '', nationality: '', address: '', shareholding: '' })}
                        className="px-3 py-1 bg-primary text-white rounded-md text-sm hover:bg-red-700 transition-colors"
                      >
                        <i className="ri-add-line mr-1"></i>
                        Add Shareholder
                      </button>
                    </div>

                    <div className="space-y-4">
                      {formData.shareholders.map((shareholder, index) => (
                        <div key={index} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                          <div className="flex justify-between items-center mb-4">
                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              <i className="ri-user-line mr-1"></i>
                              Shareholder {index + 1}
                            </h4>
                            {formData.shareholders.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removeArrayItem('shareholders', index)}
                                className="text-red-600 hover:text-red-800 text-sm p-1 rounded"
                                title="Remove shareholder"
                                aria-label="Remove shareholder"
                              >
                                <i className="ri-delete-bin-line"></i>
                              </button>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Name *</label>
                              <TextInput
                                variant="small"
                                value={shareholder.name}
                                onChange={(e) => handleArrayFieldChange('shareholders', index, 'name', e.target.value)}
                                type="text"
                                placeholder="Full name"
                                required
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Nationality *</label>
                              <CountryDropdown
                                value={shareholder.nationality}
                                onChange={(value) => handleArrayFieldChange('shareholders', index, 'nationality', value)}
                                required
                                className="text-sm"
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Shareholding (%) *</label>
                              <TextInput
                                variant="small"
                                value={shareholder.shareholding}
                                onChange={(e) => handleArrayFieldChange('shareholders', index, 'shareholding', e.target.value)}
                                type="text"
                                placeholder="e.g., 25%"
                                required
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Address *</label>
                              <TextArea
                                variant="small"
                                value={shareholder.address}
                                onChange={(e) => handleArrayFieldChange('shareholders', index, 'address', e.target.value)}
                                rows={2}
                                placeholder="Complete address"
                                required
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Directors */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        <i className="ri-user-star-line mr-2"></i>
                        Directors
                      </h3>
                      <button
                        type="button"
                        onClick={() => addArrayItem('directors', { name: '', nationality: '', address: '' })}
                        className="px-3 py-1 bg-primary text-white rounded-md text-sm hover:bg-red-700 transition-colors"
                      >
                        <i className="ri-add-line mr-1"></i>
                        Add Director
                      </button>
                    </div>

                    <div className="space-y-4">
                      {formData.directors.map((director, index) => (
                        <div key={index} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                          <div className="flex justify-between items-center mb-4">
                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              <i className="ri-user-star-line mr-1"></i>
                              Director {index + 1}
                            </h4>
                            {formData.directors.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removeArrayItem('directors', index)}
                                className="text-red-600 hover:text-red-800 text-sm p-1 rounded"
                                title="Remove director"
                                aria-label="Remove director"
                              >
                                <i className="ri-delete-bin-line"></i>
                              </button>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Name *</label>
                              <TextInput
                                variant="small"
                                value={director.name}
                                onChange={(e) => handleArrayFieldChange('directors', index, 'name', e.target.value)}
                                type="text"
                                placeholder="Full name"
                                required
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Nationality *</label>
                              <CountryDropdown
                                value={director.nationality}
                                onChange={(value) => handleArrayFieldChange('directors', index, 'nationality', value)}
                                required
                                className="text-sm"
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Address *</label>
                              <TextArea
                                variant="small"
                                value={director.address}
                                onChange={(e) => handleArrayFieldChange('directors', index, 'address', e.target.value)}
                                rows={2}
                                placeholder="Complete address"
                                required
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Foreign Ownership */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                      <i className="ri-global-line mr-2"></i>
                      Foreign Ownership
                    </h3>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Level of Foreign Ownership in the Company *
                      </label>
                      <TextArea
                        value={formData.foreignOwnership}
                        onChange={(e) => handleInputChange('foreignOwnership', e.target.value)}
                        rows={3}
                        placeholder="Describe the level of foreign ownership, including percentage and details of foreign investors"
                        required
                      />
                    </div>
                  </div>
              </div>
            )}

            {/* Step 4: Management Team */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Management Team</h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Chief Executive Officer</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name *</label>
                        <TextInput
                          type="text"
                          value={formData.ceoName}
                          onChange={(e) => handleInputChange('ceoName', e.target.value)}
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Nationality *</label>
                        <CountryDropdown
                          value={formData.ceoNationality}
                          onChange={(value) => handleInputChange('ceoNationality', value)}
                          required
                        />
                      </div>
                      <div className="md:col-span-2">
                        <TextArea
                          label="Address"
                          value={formData.ceoAddress}
                          onChange={(e) => handleInputChange('ceoAddress', e.target.value)}
                          rows={2}
                          placeholder="Enter complete address"
                          required
                        />
                      </div>
                      <TextArea
                        label="Qualifications"
                        value={formData.ceoQualifications}
                        onChange={(e) => handleInputChange('ceoQualifications', e.target.value)}
                        rows={3}
                        placeholder="Enter qualifications and certifications"
                        required
                      />
                      <TextArea
                        label="Experience"
                        value={formData.ceoExperience}
                        onChange={(e) => handleInputChange('ceoExperience', e.target.value)}
                        rows={3}
                        placeholder="Enter relevant work experience"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Management Team Members</h3>
                      <button
                        type="button"
                        onClick={() => addArrayItem('managementTeam', { name: '', address: '', nationality: '', qualifications: '' })}
                        className="px-3 py-1 bg-primary text-white rounded-md text-sm hover:bg-red-700 transition-colors"
                      >
                        <i className="ri-add-line mr-1"></i>
                        Add Member
                      </button>
                    </div>

                    {formData.managementTeam.map((member, index) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-md p-4 mb-4">
                        <div className="flex justify-between items-center mb-3">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Team Member {index + 1}</h4>
                          {formData.managementTeam.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeArrayItem('managementTeam', index)}
                              className="text-red-600 hover:text-red-800 text-sm"
                              title="Remove team member"
                              aria-label="Remove team member"
                            >
                              <i className="ri-delete-bin-line"></i>
                            </button>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <TextInput
                            type="text"
                            label="Name"
                            variant="small"
                            value={member.name}
                            onChange={(e) => handleArrayFieldChange('managementTeam', index, 'name', e.target.value)}
                            required
                          />
                          <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Nationality *</label>
                            <CountryDropdown
                              value={member.nationality}
                              onChange={(value) => handleArrayFieldChange('managementTeam', index, 'nationality', value)}
                              required
                              className="text-sm"
                            />
                          </div>
                          <div className="md:col-span-2">
                            <TextArea
                              label="Address"
                              variant="small"
                              value={member.address}
                              onChange={(e) => handleArrayFieldChange('managementTeam', index, 'address', e.target.value)}
                              rows={2}
                              placeholder="Enter complete address"
                              required
                            />
                          </div>
                          <div className="md:col-span-2">
                            <TextArea
                              label="Qualifications"
                              variant="small"
                              value={member.qualifications}
                              onChange={(e) => handleArrayFieldChange('managementTeam', index, 'qualifications', e.target.value)}
                              rows={2}
                              placeholder="Enter qualifications and certifications"
                              required
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Step 5: Professional Services */}
            {currentStep === 5 && (
              <div className="space-y-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Professional Services</h2>

                {/* External Auditors */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    <i className="ri-file-chart-line mr-2"></i>
                    External Auditors in Malawi *
                  </h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Firm/Auditor Name *
                      </label>
                      <TextInput
                        type="text"
                        value={formData.externalAuditors.name}
                        onChange={(e) => handleProfessionalServiceChange('externalAuditors', 'name', e.target.value)}
                        placeholder="Enter auditing firm name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Physical Address *
                      </label>
                      <TextArea
                        value={formData.externalAuditors.address}
                        onChange={(e) => handleProfessionalServiceChange('externalAuditors', 'address', e.target.value)}
                        rows={3}
                        placeholder="Complete physical address"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Phone Number *
                      </label>
                      <TextInput
                        type="tel"
                        value={formData.externalAuditors.phone}
                        onChange={(e) => handleProfessionalServiceChange('externalAuditors', 'phone', e.target.value)}
                        placeholder="+265 123 456 789"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address *
                      </label>
                      <TextInput
                        type="email"
                        value={formData.externalAuditors.email}
                        onChange={(e) => handleProfessionalServiceChange('externalAuditors', 'email', e.target.value)}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Website
                      </label>
                      <TextInput
                        type="url"
                        value={formData.externalAuditors.website}
                        onChange={(e) => handleProfessionalServiceChange('externalAuditors', 'website', e.target.value)}
                        placeholder="https://www.auditfirm.com"
                      />
                    </div>
                  </div>
                </div>

                {/* Legal Counsel */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    <i className="ri-scales-3-line mr-2"></i>
                    Legal Counsel in Malawi *
                  </h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Law Firm/Lawyer Name *
                      </label>
                      <TextInput
                        type="text"
                        value={formData.lawyers.name}
                        onChange={(e) => handleProfessionalServiceChange('lawyers', 'name', e.target.value)}
                        placeholder="Enter law firm or lawyer name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Physical Address *
                      </label>
                      <TextArea
                        value={formData.lawyers.address}
                        onChange={(e) => handleProfessionalServiceChange('lawyers', 'address', e.target.value)}
                        rows={3}
                        placeholder="Complete physical address"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Phone Number *
                      </label>
                      <TextInput
                        type="tel"
                        value={formData.lawyers.phone}
                        onChange={(e) => handleProfessionalServiceChange('lawyers', 'phone', e.target.value)}
                        placeholder="+265 123 456 789"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address *
                      </label>
                      <TextInput
                        type="email"
                        value={formData.lawyers.email}
                        onChange={(e) => handleProfessionalServiceChange('lawyers', 'email', e.target.value)}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Website
                      </label>
                      <TextInput
                        type="url"
                        value={formData.lawyers.website}
                        onChange={(e) => handleProfessionalServiceChange('lawyers', 'website', e.target.value)}
                        placeholder="https://www.lawfirm.com"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 6: Business Information */}
            {currentStep === 6 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Business Information</h2>

                <div className="space-y-6">
                  <TextArea
                    label="Applicant's Shareholding (10% or more) in Another Courier Licensee"
                    value={formData.otherCourierShareholding}
                    onChange={(e) => handleInputChange('otherCourierShareholding', e.target.value)}
                    rows={3}
                    placeholder="Indicate any shareholding of 10% or more in another courier licensee, or 'None' if not applicable"
                    required
                  />

                  <TextArea
                    label="Level of Insurance Cover for Customer Services"
                    value={formData.insuranceCover}
                    onChange={(e) => handleInputChange('insuranceCover', e.target.value)}
                    rows={3}
                    placeholder="Describe the insurance coverage for customer services including coverage amounts and types"
                    required
                  />
                </div>
              </div>
            )}

            {/* Step 7: Service Scope */}
            {currentStep === 7 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Scope of Service</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <TextArea
                    label="Nature of Service"
                    value={formData.serviceNature}
                    onChange={(e) => handleInputChange('serviceNature', e.target.value)}
                    rows={4}
                    placeholder="Describe the nature of courier services to be provided"
                    required
                  />

                  <TextArea
                    label="Premises"
                    value={formData.premises}
                    onChange={(e) => handleInputChange('premises', e.target.value)}
                    rows={4}
                    placeholder="Describe business premises and facilities"
                    required
                  />

                  <TextArea
                    label="Nature of Transport to be Used"
                    value={formData.transportNature}
                    onChange={(e) => handleInputChange('transportNature', e.target.value)}
                    rows={4}
                    placeholder="Describe vehicles and transport methods to be used"
                    required
                  />

                  <TextArea
                    label="Customer Assistance"
                    value={formData.customerAssistance}
                    onChange={(e) => handleInputChange('customerAssistance', e.target.value)}
                    rows={4}
                    placeholder="Describe customer service and assistance procedures"
                    required
                  />
                </div>
              </div>
            )}

            {/* Step 8: Business Plan */}
            {currentStep === 8 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Business Plan & Documentation</h2>

                <div className="space-y-6">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-start">
                      <i className="ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"></i>
                      <div>
                        <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">Required Documentation</h3>
                        <p className="text-blue-700 dark:text-blue-300 text-sm mb-3">
                          Please provide the following documents as part of your application:
                        </p>
                        <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                          <li>• Project proposal </li>
                          <li>• Business Plan including market analysis, financial resources, tariff proposals, cash flow projections for 3 years, and experience in similar services</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Project Proposal *
                      </label>
                      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                        <i className="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Upload Project Proposal</p>
                        <p className="text-xs text-gray-500 dark:text-gray-500 mb-3">PDF up to 10MB</p>
                        <input
                          type="file"
                          accept=".pdf"
                          onChange={(e) => handleFileUpload('projectProposal', e.target.files?.[0] || null)}
                          className="hidden"
                          id="projectProposal"
                        />
                        <label
                          htmlFor="projectProposal"
                          className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer"
                        >
                          <i className="ri-folder-upload-line mr-2"></i>
                          Choose File
                        </label>
                        {formData.projectProposal && (
                          <p className="text-sm text-green-600 dark:text-green-400 mt-2">
                            <i className="ri-check-line mr-1"></i>
                            {formData.projectProposal.name}
                          </p>
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Business Plan *
                      </label>
                      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                        <i className="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Upload Business Plan</p>
                        <p className="text-xs text-gray-500 dark:text-gray-500 mb-3">PDF up to 10MB</p>
                        <input
                          type="file"
                          accept=".pdf"
                          onChange={(e) => handleFileUpload('businessPlan', e.target.files?.[0] || null)}
                          className="hidden"
                          id="businessPlan"
                        />
                        <label
                          htmlFor="businessPlan"
                          className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer"
                        >
                          <i className="ri-folder-upload-line mr-2"></i>
                          Choose File
                        </label>
                        {formData.businessPlan && (
                          <p className="text-sm text-green-600 dark:text-green-400 mt-2">
                            <i className="ri-check-line mr-1"></i>
                            {formData.businessPlan.name}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 9: Legal History */}
            {currentStep === 9 && (
              <div className="space-y-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Legal History & Declarations</h2>

                {/* Regulatory Actions */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    <i className="ri-shield-check-line mr-2"></i>
                    Regulatory Actions & Disciplinary History
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                    Has the applicant or any of its associates ever been:
                  </p>

                  <div className="space-y-6">
                    {[
                      { key: 'censured', label: 'Censured by any regulatory authority or professional body' },
                      { key: 'disciplined', label: 'Disciplined by any regulatory authority or professional body' },
                      { key: 'penalized', label: 'Penalized by any regulatory authority or professional body' },
                      { key: 'suspended', label: 'Suspended from any professional practice or business activity' },
                      { key: 'prosecuted', label: 'Prosecuted for any criminal offense' },
                      { key: 'convicted', label: 'Convicted or warned as to conduct by any court or authority' },
                      { key: 'investigated', label: 'Investigated or subjected to proceedings by any governmental agency, regulatory authority or professional association' }
                    ].map((item) => (
                      <div key={item.key} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                        <div className="flex items-start space-x-4 mb-3">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                              <input
                                type="radio"
                                id={`${item.key}-yes`}
                                name={item.key}
                                value="yes"
                                checked={formData[item.key as keyof FormData] === true}
                                onChange={() => handleInputChange(item.key, true)}
                                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                              />
                              <label htmlFor={`${item.key}-yes`} className="text-sm font-medium text-gray-700 dark:text-gray-300">Yes</label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <input
                                type="radio"
                                id={`${item.key}-no`}
                                name={item.key}
                                value="no"
                                checked={formData[item.key as keyof FormData] === false}
                                onChange={() => handleInputChange(item.key, false)}
                                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                              />
                              <label htmlFor={`${item.key}-no`} className="text-sm font-medium text-gray-700 dark:text-gray-300">No</label>
                            </div>
                          </div>
                          <span className="text-sm text-gray-600 dark:text-gray-400 flex-1">{item.label}</span>
                        </div>

                        {formData[item.key as keyof FormData] === true && (
                          <div className="mt-3">
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                              Please provide detailed explanation:
                            </label>
                            <TextArea
                              value={formData[`${item.key}Details` as keyof FormData] as string || ''}
                              onChange={(e) => handleInputChange(`${item.key}Details`, e.target.value)}
                              rows={3}
                              placeholder={`Provide detailed explanation regarding ${item.label.toLowerCase()}`}
                              required
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Legal & Financial Questions */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    <i className="ri-gavel-line mr-2"></i>
                    Legal & Financial Questions
                  </h3>

                  <div className="space-y-6">
                    {/* Litigation Question */}
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <div className="flex items-start space-x-4 mb-3">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="litigation-yes"
                              name="litigation"
                              value="yes"
                              checked={formData.litigation === true}
                              onChange={() => handleInputChange('litigation', true)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="litigation-yes" className="text-sm font-medium text-gray-700 dark:text-gray-300">Yes</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="litigation-no"
                              name="litigation"
                              value="no"
                              checked={formData.litigation === false}
                              onChange={() => handleInputChange('litigation', false)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="litigation-no" className="text-sm font-medium text-gray-700 dark:text-gray-300">No</label>
                          </div>
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400 flex-1">
                          Has the applicant or any of its associates ever been or is now the subject of any litigation which may have a material effect on its resources?
                        </span>
                      </div>

                      {formData.litigation && (
                        <div className="mt-3">
                          <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                            Please provide detailed explanation:
                          </label>
                          <TextArea
                            value={formData.litigationDetails}
                            onChange={(e) => handleInputChange('litigationDetails', e.target.value)}
                            rows={3}
                            placeholder="Provide details of litigation including case details, current status, and potential financial impact"
                            required
                          />
                        </div>
                      )}
                    </div>

                    {/* Judgment Debt Question */}
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <div className="flex items-start space-x-4 mb-3">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="judgmentDebt-yes"
                              name="judgmentDebt"
                              value="yes"
                              checked={formData.judgmentDebt === true}
                              onChange={() => handleInputChange('judgmentDebt', true)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="judgmentDebt-yes" className="text-sm font-medium text-gray-700 dark:text-gray-300">Yes</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="judgmentDebt-no"
                              name="judgmentDebt"
                              value="no"
                              checked={formData.judgmentDebt === false}
                              onChange={() => handleInputChange('judgmentDebt', false)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="judgmentDebt-no" className="text-sm font-medium text-gray-700 dark:text-gray-300">No</label>
                          </div>
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400 flex-1">
                          Has the applicant or any of its associates failed to satisfy within one year any judgment debt?
                        </span>
                      </div>

                      {formData.judgmentDebt && (
                        <div className="mt-3">
                          <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                            Please provide detailed explanation:
                          </label>
                          <TextArea
                            value={formData.judgmentDetails}
                            onChange={(e) => handleInputChange('judgmentDetails', e.target.value)}
                            rows={3}
                            placeholder="Provide details of judgment debt including amount, court details, and current status"
                            required
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Insolvency & Financial Difficulties */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    <i className="ri-money-dollar-circle-line mr-2"></i>
                    Insolvency & Financial Difficulties
                  </h3>

                  <div className="space-y-6">
                    {/* Insolvency Question */}
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <div className="flex items-start space-x-4 mb-3">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="insolvent-yes"
                              name="insolvent"
                              value="yes"
                              checked={formData.insolvent === true}
                              onChange={() => handleInputChange('insolvent', true)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="insolvent-yes" className="text-sm font-medium text-gray-700 dark:text-gray-300">Yes</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="insolvent-no"
                              name="insolvent"
                              value="no"
                              checked={formData.insolvent === false}
                              onChange={() => handleInputChange('insolvent', false)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="insolvent-no" className="text-sm font-medium text-gray-700 dark:text-gray-300">No</label>
                          </div>
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400 flex-1">
                          Is the applicant insolvent or unable to pay its debts as they fall due?
                        </span>
                      </div>

                      {formData.insolvent && (
                        <div className="mt-3">
                          <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                            Please provide detailed explanation:
                          </label>
                          <TextArea
                            value={formData.insolventDetails}
                            onChange={(e) => handleInputChange('insolventDetails', e.target.value)}
                            rows={3}
                            placeholder="Provide details of insolvency including circumstances, timeline, and current financial status"
                            required
                          />
                        </div>
                      )}
                    </div>

                    {/* Liquidator Question */}
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <div className="flex items-start space-x-4 mb-3">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="liquidator-yes"
                              name="liquidator"
                              value="yes"
                              checked={formData.liquidator === true}
                              onChange={() => handleInputChange('liquidator', true)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="liquidator-yes" className="text-sm font-medium text-gray-700 dark:text-gray-300">Yes</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="liquidator-no"
                              name="liquidator"
                              value="no"
                              checked={formData.liquidator === false}
                              onChange={() => handleInputChange('liquidator', false)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="liquidator-no" className="text-sm font-medium text-gray-700 dark:text-gray-300">No</label>
                          </div>
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400 flex-1">
                          Is a liquidator appointed to the applicant or any of its associates?
                        </span>
                      </div>

                      {formData.liquidator && (
                        <div className="mt-3">
                          <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                            Please provide detailed explanation:
                          </label>
                          <TextArea
                            value={formData.liquidatorDetails}
                            onChange={(e) => handleInputChange('liquidatorDetails', e.target.value)}
                            rows={3}
                            placeholder="Provide details of liquidator appointment including name, date of appointment, and current status"
                            required
                          />
                        </div>
                      )}
                    </div>

                    {/* Compromise Question */}
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <div className="flex items-start space-x-4 mb-3">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="compromise-yes"
                              name="compromise"
                              value="yes"
                              checked={formData.compromise === true}
                              onChange={() => handleInputChange('compromise', true)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="compromise-yes" className="text-sm font-medium text-gray-700 dark:text-gray-300">Yes</label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="compromise-no"
                              name="compromise"
                              value="no"
                              checked={formData.compromise === false}
                              onChange={() => handleInputChange('compromise', false)}
                              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600"
                            />
                            <label htmlFor="compromise-no" className="text-sm font-medium text-gray-700 dark:text-gray-300">No</label>
                          </div>
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400 flex-1">
                          Is there a compromise with creditors or any arrangement with creditors?
                        </span>
                      </div>

                      {formData.compromise && (
                        <div className="mt-3">
                          <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                            Please provide detailed explanation:
                          </label>
                          <TextArea
                            value={formData.compromiseDetails}
                            onChange={(e) => handleInputChange('compromiseDetails', e.target.value)}
                            rows={3}
                            placeholder="Provide details of compromise or arrangement with creditors including terms and current status"
                            required
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}


            {/* Step 10: Review & Submit */}
            {currentStep === 10 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Review & Submit Application</h2>

                <div className="space-y-6">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 border border-yellow-200 dark:border-yellow-800">
                    <div className="flex items-start">
                      <i className="ri-information-line text-yellow-600 dark:text-yellow-400 text-lg mr-3 mt-0.5"></i>
                      <div>
                        <h3 className="text-sm font-medium text-yellow-900 dark:text-yellow-100 mb-2">Application Fees</h3>
                        <div className="text-yellow-700 dark:text-yellow-300 text-sm space-y-1">
                          <p>• Application Fee: US$100.00</p>
                          <p>• Annual License Fee: Varies by license type (see license selection page)</p>
                          <p>• Annual Levy: 1.5% of operating revenue (audited accounts)</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                    <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-3">Application Summary</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-blue-900 dark:text-blue-100">Applicant:</span>
                        <span className="text-blue-700 dark:text-blue-300 ml-2">{formData.applicantName || 'Not provided'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-900 dark:text-blue-100">License Type:</span>
                        <span className="text-blue-700 dark:text-blue-300 ml-2">
                          {selectedCategory?.name || 'Not selected'}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-900 dark:text-blue-100">Email:</span>
                        <span className="text-blue-700 dark:text-blue-300 ml-2">{formData.email || 'Not provided'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-900 dark:text-blue-100">Business Registration:</span>
                        <span className="text-blue-700 dark:text-blue-300 ml-2">{formData.businessRegistrationNo || 'Not provided'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-900 dark:text-blue-100">Postal Address:</span>
                        <span className="text-blue-700 dark:text-blue-300 ml-2">
                          {formData.postalPoBox && formData.postalCity && formData.postalCountry
                            ? `${formData.postalPoBox}, ${formData.postalCity}, ${formData.postalCountry}`
                            : 'Not provided'
                          }
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-900 dark:text-blue-100">Physical Address:</span>
                        <span className="text-blue-700 dark:text-blue-300 ml-2">
                          {formData.physicalStreet && formData.physicalCity && formData.physicalCountry
                            ? `${formData.physicalStreet}, ${formData.physicalCity}, ${formData.physicalCountry}`
                            : 'Not provided'
                          }
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Declaration</h3>
                    <div className="prose prose-sm text-gray-600 dark:text-gray-400 mb-6">
                      <p>
                        We, the undersigned Chief Executive Officer and members of the Board of Directors of the applicant,
                        do hereby certify that:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>
                          All information given in response to and in support of the questions of this application is true
                          and correct to the best of our knowledge and belief;
                        </li>
                        <li>
                          This application is made in good faith with the purpose and intent that the affairs and business
                          of the applicant will at all times be honestly conducted in accordance with good and sound business
                          principles and in full compliance with all applicable laws and lawful directives from the Authority.
                        </li>
                      </ul>
                      <p>
                        We further certify that to the best of our knowledge and belief there are no other facts or information
                        relevant to this application which may arise while it is being considered by the Authority. We hereby
                        authorize the Authority and any of its authorized staff to make an inquiry or obtain any information
                        from any source for the purpose of determining the correctness of all representations made in connection
                        with this application or of assessing its merits.
                      </p>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-start">
                        <input
                          type="checkbox"
                          id="declaration"
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded mt-1"
                          required
                        />
                        <label htmlFor="declaration" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                          I acknowledge that I have read, understood, and agree to the above declaration. I certify that all
                          information provided in this application is true and accurate to the best of my knowledge.
                        </label>
                      </div>

                      <div className="flex items-start">
                        <input
                          type="checkbox"
                          id="terms"
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded mt-1"
                          required
                        />
                        <label htmlFor="terms" className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                          I agree to comply with all applicable laws, regulations, and directives issued by MACRA regarding
                          courier services operations in Malawi.
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
                    <div className="flex items-start">
                      <i className="ri-check-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5"></i>
                      <div>
                        <h3 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">Ready to Submit</h3>
                        <p className="text-green-700 dark:text-green-300 text-sm">
                          Your application is ready for submission. Once submitted, you will receive a confirmation email
                          with your application reference number and next steps.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 1}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-md text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i className="ri-arrow-left-line mr-1"></i>
                Previous
              </button>
              
              {currentStep === totalSteps ? (
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-primary text-white hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md text-sm font-medium transition-colors"
                >
                  {isSubmitting ? (
                    <>
                      <i className="ri-loader-4-line mr-1 animate-spin"></i>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <i className="ri-send-plane-line mr-1"></i>
                      Submit Application
                    </>
                  )}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={nextStep}
                  className="px-4 py-2 bg-primary text-white hover:bg-red-700 rounded-md text-sm font-medium transition-colors"
                >
                  Next
                  <i className="ri-arrow-right-line ml-1"></i>
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default CourierApplicationForm;
