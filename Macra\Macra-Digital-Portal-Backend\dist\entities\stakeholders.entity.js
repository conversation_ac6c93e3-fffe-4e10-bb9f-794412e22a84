"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Stakeholders = exports.StakeholderPosition = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const applicant_entity_1 = require("./applicant.entity");
const contacts_entity_1 = require("./contacts.entity");
var StakeholderPosition;
(function (StakeholderPosition) {
    StakeholderPosition["CEO"] = "CEO";
    StakeholderPosition["SHAREHOLDER"] = "Shareholder";
    StakeholderPosition["AUDITOR"] = "Auditor";
    StakeholderPosition["LAWYER"] = "Lawyer";
})(StakeholderPosition || (exports.StakeholderPosition = StakeholderPosition = {}));
let Stakeholders = class Stakeholders {
    stakeholder_id;
    applicant_id;
    first_name;
    last_name;
    middle_name;
    contact_id;
    nationality;
    position;
    profile;
    cv_document_id;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    applicant;
    contact;
    creator;
    updater;
    generateId() {
        if (!this.stakeholder_id) {
            this.stakeholder_id = (0, uuid_1.v4)();
        }
    }
};
exports.Stakeholders = Stakeholders;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], Stakeholders.prototype, "stakeholder_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Stakeholders.prototype, "applicant_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], Stakeholders.prototype, "first_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], Stakeholders.prototype, "last_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], Stakeholders.prototype, "middle_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Stakeholders.prototype, "contact_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50 }),
    __metadata("design:type", String)
], Stakeholders.prototype, "nationality", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: StakeholderPosition,
    }),
    __metadata("design:type", String)
], Stakeholders.prototype, "position", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 300 }),
    __metadata("design:type", String)
], Stakeholders.prototype, "profile", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Stakeholders.prototype, "cv_document_id", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Stakeholders.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Stakeholders.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Stakeholders.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Stakeholders.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], Stakeholders.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => applicant_entity_1.Applicants),
    (0, typeorm_1.JoinColumn)({ name: 'applicant_id' }),
    __metadata("design:type", applicant_entity_1.Applicants)
], Stakeholders.prototype, "applicant", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => contacts_entity_1.Contacts),
    (0, typeorm_1.JoinColumn)({ name: 'contact_id' }),
    __metadata("design:type", contacts_entity_1.Contacts)
], Stakeholders.prototype, "contact", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], Stakeholders.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], Stakeholders.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Stakeholders.prototype, "generateId", null);
exports.Stakeholders = Stakeholders = __decorate([
    (0, typeorm_1.Entity)('stakeholders')
], Stakeholders);
//# sourceMappingURL=stakeholders.entity.js.map