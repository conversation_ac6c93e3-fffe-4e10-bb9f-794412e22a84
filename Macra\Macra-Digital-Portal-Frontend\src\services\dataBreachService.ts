import { apiClient } from '@/lib/apiClient';
import { documentService } from './documentService';

export interface DataBreachReport {
  report_id: string;
  report_number: string;
  reporter_id: string;
  title: string;
  description: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  incident_date: string;
  organization_involved: string;
  affected_data_types?: string;
  contact_attempts?: string;
  assigned_to?: string;
  resolution?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  reporter?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  attachments?: {
    attachment_id: string;
    file_name: string;
    file_type: string;
    file_size: number;
    uploaded_at: string;
  }[];
  status_history?: {
    history_id: string;
    status: string;
    comment?: string;
    created_at: string;
    creator: {
      user_id: string;
      first_name: string;
      last_name: string;
    };
  }[];
}

export interface CreateDataBreachReportData {
  title: string;
  description: string;
  category: string;
  severity: string;
  incident_date: string;
  organization_involved: string;
  affected_data_types?: string;
  contact_attempts?: string;
  priority?: string;
  attachments?: File[];
}

export interface UpdateDataBreachReportData {
  title?: string;
  description?: string;
  category?: string;
  severity?: string;
  status?: string;
  priority?: string;
  incident_date?: string;
  organization_involved?: string;
  affected_data_types?: string;
  contact_attempts?: string;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
}

export interface DataBreachReportFilter {
  category?: string;
  severity?: string;
  status?: string;
  priority?: string;
  reporter_id?: string;
  assigned_to?: string;
  from_date?: string;
  to_date?: string;
  incident_from_date?: string;
  incident_to_date?: string;
  search?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

export interface DataBreachReportResponse {
  success: boolean;
  message: string;
  data?: DataBreachReport;
  total?: number;
  page?: number;
  limit?: number;
}

export interface DataBreachReportListResponse {
  success: boolean;
  message: string;
  data: DataBreachReport[];
  total: number;
  page: number;
  limit: number;
}

class DataBreachService {
  private readonly baseUrl = '/data-breach-reports';

  async createReport(data: CreateDataBreachReportData): Promise<DataBreachReportResponse> {
    try {
      console.log('🔄 Creating data breach report:', { ...data, attachments: data.attachments?.length || 0 });

      const formData = new FormData();
      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('category', data.category);
      formData.append('severity', data.severity);
      formData.append('incident_date', data.incident_date);
      formData.append('organization_involved', data.organization_involved);
      
      if (data.affected_data_types) {
        formData.append('affected_data_types', data.affected_data_types);
      }
      
      if (data.contact_attempts) {
        formData.append('contact_attempts', data.contact_attempts);
      }
      
      if (data.priority) {
        formData.append('priority', data.priority);
      }

      // Add attachments if any
      if (data.attachments && data.attachments.length > 0) {
        data.attachments.forEach((file, index) => {
          formData.append('attachments', file);
        });
      }

      const response = await apiClient.post(this.baseUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Data breach report created successfully:', response.data);

      // Upload documents to polymorphic document table if report was created successfully
      if (response.data.success && data.attachments && data.attachments.length > 0) {
        try {
          const reportId = response.data.data.report_id;
          await documentService.uploadMultipleDocuments(
            data.attachments,
            'data_breach_report',
            reportId,
            'BREACH_EVIDENCE',
            false
          );
          console.log('✅ Data breach attachments uploaded to document table');
        } catch (docError) {
          console.warn('⚠️ Failed to upload documents to document table:', docError);
          // Don't fail the report creation if document upload fails
        }
      }

      return response.data;
    } catch (error) {
      console.error('❌ Error creating data breach report:', error);
      throw error;
    }
  }

  async getReports(filter: DataBreachReportFilter = {}): Promise<DataBreachReportListResponse> {
    try {
      console.log('🔄 Fetching data breach reports with filter:', filter);

      const params = new URLSearchParams();
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`${this.baseUrl}?${params.toString()}`);

      console.log('✅ Data breach reports fetched successfully:', {
        total: response.data.total,
        count: response.data.data?.length || 0
      });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching data breach reports:', error);
      throw error;
    }
  }

  async getReport(reportId: string): Promise<DataBreachReportResponse> {
    try {
      console.log('🔄 Fetching data breach report:', reportId);

      const response = await apiClient.get(`${this.baseUrl}/${reportId}`);

      console.log('✅ Data breach report fetched successfully:', response.data.data?.report_number);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching data breach report:', error);
      throw error;
    }
  }

  async updateReport(
    reportId: string,
    data: UpdateDataBreachReportData
  ): Promise<DataBreachReportResponse> {
    try {
      console.log('🔄 Updating data breach report:', reportId, data);

      const response = await apiClient.put(`${this.baseUrl}/${reportId}`, data);

      console.log('✅ Data breach report updated successfully:', response.data.data?.report_number);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating data breach report:', error);
      throw error;
    }
  }

  async deleteReport(reportId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 Deleting data breach report:', reportId);

      const response = await apiClient.delete(`${this.baseUrl}/${reportId}`);

      console.log('✅ Data breach report deleted successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error deleting data breach report:', error);
      throw error;
    }
  }

  async updateReportStatus(
    reportId: string,
    status: string,
    comment?: string
  ): Promise<DataBreachReportResponse> {
    try {
      console.log('🔄 Updating data breach report status:', reportId, status);

      const response = await apiClient.put(`${this.baseUrl}/${reportId}/status`, {
        status,
        comment
      });

      console.log('✅ Data breach report status updated successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error updating data breach report status:', error);
      throw error;
    }
  }

  async assignReport(reportId: string, assignedTo: string): Promise<DataBreachReportResponse> {
    try {
      console.log('🔄 Assigning data breach report:', reportId, assignedTo);

      const response = await apiClient.put(`${this.baseUrl}/${reportId}/assign`, {
        assigned_to: assignedTo
      });

      console.log('✅ Data breach report assigned successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error assigning data breach report:', error);
      throw error;
    }
  }

  async addAttachments(reportId: string, files: File[]): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 Adding attachments to data breach report:', reportId, files.length);

      const formData = new FormData();
      files.forEach((file) => {
        formData.append('files', file);
      });

      const response = await apiClient.post(`${this.baseUrl}/${reportId}/attachments`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Attachments added successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error adding attachments:', error);
      throw error;
    }
  }

  async deleteAttachment(reportId: string, attachmentId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 Deleting attachment:', reportId, attachmentId);

      const response = await apiClient.delete(`${this.baseUrl}/${reportId}/attachments/${attachmentId}`);

      console.log('✅ Attachment deleted successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error deleting attachment:', error);
      throw error;
    }
  }

  async getStatsSummary(): Promise<{
    success: boolean;
    message: string;
    data: {
      total: number;
      by_status: Record<string, number>;
      by_category: Record<string, number>;
      by_severity: Record<string, number>;
      by_priority: Record<string, number>;
    };
  }> {
    try {
      console.log('🔄 Fetching data breach statistics summary');

      const response = await apiClient.get(`${this.baseUrl}/stats/summary`);

      console.log('✅ Statistics summary fetched successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching statistics summary:', error);
      throw error;
    }
  }

  async getUrgentAlerts(): Promise<DataBreachReportListResponse> {
    try {
      console.log('🔄 Fetching urgent data breach alerts');

      const response = await apiClient.get(`${this.baseUrl}/urgent/alerts`);

      console.log('✅ Urgent alerts fetched successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching urgent alerts:', error);
      throw error;
    }
  }

  // Helper method to get breach categories
  getBreachCategories(): string[] {
    return [
      'Unauthorized Data Access',
      'Data Misuse or Sharing',
      'Privacy Violations',
      'Identity Theft',
      'Phishing Attempts',
      'Data Loss or Theft',
      'Consent Violations',
      'Other'
    ];
  }

  // Helper method to get severity levels
  getSeverityLevels(): { value: string; label: string }[] {
    return [
      { value: 'low', label: 'Low - Minor privacy concern' },
      { value: 'medium', label: 'Medium - Moderate data exposure' },
      { value: 'high', label: 'High - Significant data breach' },
      { value: 'critical', label: 'Critical - Severe security incident' }
    ];
  }

  // Helper method to get report statuses
  getReportStatuses(): string[] {
    return [
      'submitted',
      'under_review',
      'investigating',
      'resolved',
      'closed'
    ];
  }

  // Helper method to get priority levels
  getPriorityLevels(): string[] {
    return [
      'low',
      'medium',
      'high',
      'urgent'
    ];
  }
}

export const dataBreachService = new DataBreachService();
