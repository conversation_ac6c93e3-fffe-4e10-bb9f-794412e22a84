import { Test, TestingModule } from '@nestjs/testing';
import { AddressService } from './address.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Address } from '../entities/address.entity';
import { Repository, DataSource } from 'typeorm';
import { ConflictException, NotFoundException } from '@nestjs/common';

// Minimal realistic mock User matching User entity shape
const mockUser = {
  user_id: 'user-1',
  email: '<EMAIL>',
  password: 'hashedpassword',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  phone: '1234567890',
  status: 'active',
  two_factor_enabled: false,
  roles: [],
  created_at: new Date(),
  updated_at: new Date(),
  identifications: [],
  employee_records: [],
  generateId: () => 'user-1',
};

// Mock Address entity matching your entity shape and DTO
const mockAddress: Address = {
  address_id: 'addr-1',
  address_type: 'physical',
  address_origin: 'user',
  address_line_1: '123 Test St',
  address_line_2: undefined,
  address_line_3: undefined,
  postal_code: '123456',
  country: 'CountryX',
  city: 'CityX',
  created_at: new Date(),
  created_by: 'user-1',
  updated_at: new Date(),
  updated_by: undefined,
  deleted_at: undefined,
  creator: mockUser,
  updater: undefined,
  generateId() {
    return 'addr-1';
  },
};

describe('AddressService', () => {
  let service: AddressService;
  let repo: Repository<Address>;
  let dataSource: DataSource;

  const addressRepoMock = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    softDelete: jest.fn(),
    restore: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      andWhere: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      withDeleted: jest.fn().mockReturnThis(),
      getOne: jest.fn(),
      getMany: jest.fn(),
      setLock: jest.fn().mockReturnThis(),
    })),
  };

  const dataSourceMock = {
    transaction: jest.fn().mockImplementation((cb) =>
      cb({ getRepository: () => addressRepoMock }),
    ),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AddressService,
        {
          provide: getRepositoryToken(Address),
          useValue: addressRepoMock,
        },
        {
          provide: DataSource,
          useValue: dataSourceMock,
        },
      ],
    }).compile();

    service = module.get<AddressService>(AddressService);
    repo = module.get<Repository<Address>>(getRepositoryToken(Address));
    dataSource = module.get<DataSource>(DataSource);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createAddress', () => {
    it('creates and returns a new address if it does not exist', async () => {
      addressRepoMock.createQueryBuilder().getOne.mockResolvedValue(null);
      addressRepoMock.create.mockReturnValue(mockAddress);
      addressRepoMock.save.mockResolvedValue(mockAddress);

      const dto = {
        address_line_1: mockAddress.address_line_1,
        address_type: mockAddress.address_type,
        address_origin: mockAddress.address_origin,
        city: mockAddress.city,
        country: mockAddress.country,
        postal_code: mockAddress.postal_code,
      };

      const result = await service.createAddress(dto, mockAddress.created_by);

      expect(addressRepoMock.create).toHaveBeenCalledWith({
        ...dto,
        created_by: mockAddress.created_by,
      });
      expect(addressRepoMock.save).toHaveBeenCalledWith(mockAddress);
      expect(result).toEqual(mockAddress);
    });

    it('throws ConflictException when address already exists', async () => {
      addressRepoMock.createQueryBuilder().getOne.mockResolvedValue(mockAddress);

      await expect(
        service.createAddress(
          {
            address_line_1: mockAddress.address_line_1,
            address_type: mockAddress.address_type,
            address_origin: mockAddress.address_origin,
            city: mockAddress.city,
            country: mockAddress.country,
            postal_code: mockAddress.postal_code,
          },
          mockAddress.created_by,
        ),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('editAddress', () => {
    it('updates and returns the address', async () => {
      const updatedAddress = { ...mockAddress, city: 'NewCity', updated_by: 'user-2' };
      addressRepoMock.findOne.mockResolvedValue(mockAddress);
      addressRepoMock.save.mockResolvedValue(updatedAddress);

      const dto = {
        address_id: mockAddress.address_id,
        city: 'NewCity',
      };

      const result = await service.editAddress(dto, 'user-2');

      expect(addressRepoMock.save).toHaveBeenCalledWith({
        ...mockAddress,
        ...dto,
        updated_by: 'user-2',
      });
      expect(result.city).toBe('NewCity');
      expect(result.updated_by).toBe('user-2');
    });

    it('throws NotFoundException if address not found', async () => {
      addressRepoMock.findOne.mockResolvedValue(null);

      await expect(
        service.editAddress({ address_id: 'invalid-id', city: 'X' }, 'user-x'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('findAll', () => {
    it('returns addresses filtered by criteria', async () => {
      addressRepoMock.createQueryBuilder().getMany.mockResolvedValue([mockAddress]);

      const filter = { origin: 'user', type: 'physical' };
      const result = await service.findAll(filter);

      expect(addressRepoMock.createQueryBuilder).toHaveBeenCalled();
      expect(result).toEqual([mockAddress]);
    });
  });

  describe('findOneById', () => {
    it('returns address by id', async () => {
      addressRepoMock.findOne.mockResolvedValue(mockAddress);

      const result = await service.findOneById(mockAddress.address_id);

      expect(addressRepoMock.findOne).toHaveBeenCalledWith({ where: { address_id: mockAddress.address_id } });
      expect(result).toEqual(mockAddress);
    });

    it('throws NotFoundException if address not found', async () => {
      addressRepoMock.findOne.mockResolvedValue(null);

      await expect(service.findOneById('invalid')).rejects.toThrow(NotFoundException);
    });
  });

  describe('softDelete', () => {
    it('soft deletes an address', async () => {
      addressRepoMock.findOne.mockResolvedValue(mockAddress);
      addressRepoMock.softDelete.mockResolvedValue({ affected: 1 });

      await service.softDelete(mockAddress.address_id, 'user-x');

      expect(addressRepoMock.softDelete).toHaveBeenCalledWith(mockAddress.address_id);
    });

    it('throws NotFoundException if address missing', async () => {
      addressRepoMock.findOne.mockResolvedValue(null);

      await expect(service.softDelete('bad-id', 'user')).rejects.toThrow(NotFoundException);
    });
  });

  describe('restore', () => {
    it('restores soft deleted address', async () => {
      addressRepoMock.createQueryBuilder().getOne.mockResolvedValue(mockAddress);
      addressRepoMock.restore.mockResolvedValue({ affected: 1 });

      await service.restore(mockAddress.address_id);

      expect(addressRepoMock.restore).toHaveBeenCalledWith(mockAddress.address_id);
    });

    it('throws NotFoundException if deleted address missing', async () => {
      addressRepoMock.createQueryBuilder().getOne.mockResolvedValue(null);

      await expect(service.restore('bad-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('hardDelete', () => {
    it('hard deletes an address', async () => {
      addressRepoMock.findOne.mockResolvedValue(mockAddress);
      addressRepoMock.remove.mockResolvedValue(mockAddress);

      await service.hardDelete(mockAddress.address_id);

      expect(addressRepoMock.remove).toHaveBeenCalledWith(mockAddress);
    });

    it('throws NotFoundException if address not found', async () => {
      addressRepoMock.findOne.mockResolvedValue(null);

      await expect(service.hardDelete('missing')).rejects.toThrow(NotFoundException);
    });
  });
});
