'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { validateSection } from '@/utils/formValidation';

interface LegalHistoryProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  errors: Record<string, string>;
  applicationId?: string;
  isLoading?: boolean;
}

const LegalHistory: React.FC<LegalHistoryProps> = ({
  formData,
  onChange,
  onSave,
  errors,
  applicationId,
  isLoading = false
}) => {
  const [localData, setLocalData] = useState({
    criminal_history: false,
    criminal_details: '',
    bankruptcy_history: false,
    bankruptcy_details: '',
    regulatory_actions: false,
    regulatory_details: '',
    litigation_history: false,
    litigation_details: '',
    compliance_record: '',
    previous_licenses: '',
    declaration_accepted: false,
    ...formData
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Sync formData to localData only when formData changes and is different
  useEffect(() => {
    if (formData && Object.keys(formData).length > 0) {
      setLocalData((prev: any) => {
        const newData = { ...prev, ...formData };
        // Only update if there are actual changes to prevent infinite loops
        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);
        return hasChanges ? newData : prev;
      });
    }
  }, [formData]);

  const handleLocalChange = useCallback((field: string, value: any) => {
    setLocalData((prev: any) => ({ ...prev, [field]: value }));

    // Call onChange with the field and value
    if (onChange) {
      onChange(field, value);
    }

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }
  }, [onChange, validationErrors]);

  const validateForm = () => {
    const validation = validateSection(localData, 'legalHistory');
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      await onSave(localData);
      console.log('Legal history saved');
    } catch (error) {
      console.error('Error saving legal history:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Legal History & Compliance
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Please provide information about your legal and compliance history.
        </p>
      </div>

      {/* Criminal History */}
      <div className="space-y-4">
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={localData.criminal_history || false}
              onChange={(e) => handleLocalChange('criminal_history', e.target.checked)}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              I have a criminal history
            </span>
          </label>
        </div>

        {localData.criminal_history && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Criminal History Details *
            </label>
            <textarea
              value={localData.criminal_details || ''}
              onChange={(e) => handleLocalChange('criminal_details', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Please provide details of your criminal history..."
            />
            {(validationErrors.criminal_details || errors.criminal_details) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.criminal_details || errors.criminal_details}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Bankruptcy History */}
      <div className="space-y-4">
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={localData.bankruptcy_history || false}
              onChange={(e) => handleLocalChange('bankruptcy_history', e.target.checked)}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              I have a bankruptcy history
            </span>
          </label>
        </div>

        {localData.bankruptcy_history && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Bankruptcy History Details *
            </label>
            <textarea
              value={localData.bankruptcy_details || ''}
              onChange={(e) => handleLocalChange('bankruptcy_details', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Please provide details of your bankruptcy history..."
            />
            {(validationErrors.bankruptcy_details || errors.bankruptcy_details) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.bankruptcy_details || errors.bankruptcy_details}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Regulatory Actions */}
      <div className="space-y-4">
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={localData.regulatory_actions || false}
              onChange={(e) => handleLocalChange('regulatory_actions', e.target.checked)}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              I have been subject to regulatory actions
            </span>
          </label>
        </div>

        {localData.regulatory_actions && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Regulatory Actions Details *
            </label>
            <textarea
              value={localData.regulatory_details || ''}
              onChange={(e) => handleLocalChange('regulatory_details', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Please provide details of regulatory actions..."
            />
            {(validationErrors.regulatory_details || errors.regulatory_details) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.regulatory_details || errors.regulatory_details}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Litigation History */}
      <div className="space-y-4">
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={localData.litigation_history || false}
              onChange={(e) => handleLocalChange('litigation_history', e.target.checked)}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              I have been involved in litigation
            </span>
          </label>
        </div>

        {localData.litigation_history && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Litigation History Details *
            </label>
            <textarea
              value={localData.litigation_details || ''}
              onChange={(e) => handleLocalChange('litigation_details', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Please provide details of litigation history..."
            />
            {(validationErrors.litigation_details || errors.litigation_details) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.litigation_details || errors.litigation_details}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Compliance Record */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Compliance Record *
        </label>
        <textarea
          value={localData.compliance_record || ''}
          onChange={(e) => handleLocalChange('compliance_record', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="Describe your compliance record and any relevant certifications..."
        />
        {(validationErrors.compliance_record || errors.compliance_record) && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {validationErrors.compliance_record || errors.compliance_record}
          </p>
        )}
      </div>

      {/* Previous Licenses */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Previous Licenses
        </label>
        <textarea
          value={localData.previous_licenses || ''}
          onChange={(e) => handleLocalChange('previous_licenses', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
          placeholder="List any previous licenses held or applied for..."
        />
        {(validationErrors.previous_licenses || errors.previous_licenses) && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {validationErrors.previous_licenses || errors.previous_licenses}
          </p>
        )}
      </div>

      {/* Declaration */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <label className="flex items-start">
            <input
              type="checkbox"
              checked={localData.declaration_accepted || false}
              onChange={(e) => handleLocalChange('declaration_accepted', e.target.checked)}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"
            />
            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
              I declare that all information provided is true and accurate to the best of my knowledge. 
              I understand that providing false information may result in the rejection of my application 
              or revocation of any license granted. *
            </span>
          </label>
          {(validationErrors.declaration_accepted || errors.declaration_accepted) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.declaration_accepted || errors.declaration_accepted}
            </p>
          )}
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving || isLoading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Legal History
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default LegalHistory;
