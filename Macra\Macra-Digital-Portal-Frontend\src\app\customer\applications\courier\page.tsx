'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import AddLocationModal from '@/components/customer/AddLocationModal';
import { usePostalCourierLicenses } from '@/hooks/useLicenseData';

interface LicenseOption {
  id: string;
  name: string;
  description: string;
  icon: string;
  iconBg: string;
  iconColor: string;
  processingTime: string;
  applicationFee: number;
  annualFee: number;
  currency: string;
  requirements: string[];
  benefits: string[];
  validityPeriod: string;
  renewalRequired: boolean;
  category: 'international' | 'domestic' | 'district' | 'postal';
  serviceType: 'courier' | 'postal';
}

const PostalCourierApplicationPage = () => {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const { licenseTypes, categories, loading: licenseLoading, error } = usePostalCourierLicenses();

  const [showDetails, setShowDetails] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<'courier' | 'postal' | 'mnas'>('courier');
  const [showAddLocationModal, setShowAddLocationModal] = useState(false);

  // MNAS regions (static for now)
  const mnasRegions = ['Northern', 'Central', 'Southern'];

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated,router]);

  // Transform backend license categories to display format
  const transformLicenseCategory = (category: any, licenseType: any): LicenseOption => {
    const nameLower = category.name.toLowerCase();

    // Determine icon and styling based on category name
    let icon = 'ri-file-text-line';
    let iconBg = 'bg-gray-100';
    let iconColor = 'text-gray-600';
    let serviceType: 'courier' | 'postal' = 'courier';
    let categoryType: 'international' | 'domestic' | 'district' | 'postal' = 'domestic';

    if (nameLower.includes('postal') || nameLower.includes('mail')) {
      icon = 'ri-mail-line';
      iconBg = 'bg-blue-100';
      iconColor = 'text-blue-600';
      serviceType = 'postal';
      categoryType = 'postal';
    } else if (nameLower.includes('international')) {
      icon = 'ri-global-line';
      iconBg = 'bg-purple-100';
      iconColor = 'text-purple-600';
      categoryType = 'international';
    } else if (nameLower.includes('domestic')) {
      icon = 'ri-truck-line';
      iconBg = 'bg-green-100';
      iconColor = 'text-green-600';
      categoryType = 'domestic';
    } else if (nameLower.includes('district')) {
      icon = 'ri-map-pin-line';
      iconBg = 'bg-orange-100';
      iconColor = 'text-orange-600';
      categoryType = 'district';
    }

    return {
      id: category.license_category_id,
      name: category.name,
      description: category.description || 'No description available',
      icon,
      iconBg,
      iconColor,
      processingTime: 'Contact MACRA',
      applicationFee: parseFloat(category.fee) || 0,
      annualFee: 0,
      currency: 'MWK',
      requirements: [
        'Valid business registration certificate',
        'Tax clearance certificate',
        'Proof of financial capacity',
        'Insurance coverage documentation',
        'Business plan and operational procedures'
      ],
      benefits: [
        category.authorizes || 'Authorization for specified services',
        'Regulatory compliance and protection',
        'Market credibility and recognition',
        'Access to regulated business opportunities'
      ],
      validityPeriod: licenseType.validity ? `${licenseType.validity} months` : 'Contact MACRA',
      renewalRequired: true,
      category: categoryType,
      serviceType,
    };
  };

  // Transform backend data to license options
  const allLicenseOptions: LicenseOption[] = categories.map(category => {
    const licenseType = licenseTypes.find(lt => lt.license_type_id === category.license_type_id);
    return transformLicenseCategory(category, licenseType);
  });

  // Fallback static data if no backend data is available
  const fallbackLicenseOptions: LicenseOption[] = [
    // Postal License Option
    {
      id: 'postal-operator-license',
      name: 'Postal Operator License',
      description: 'Comprehensive authorization for postal services including mail collection, processing, transportation, and delivery throughout Malawi.',
      icon: 'ri-mail-line',
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600',
      processingTime: '30-60 days',
      applicationFee: 100000,
      annualFee: 500000,
      currency: 'MWK',
      validityPeriod: '5 years',
      renewalRequired: true,
      category: 'postal',
      serviceType: 'postal',
      requirements: [
        'Valid business registration certificate',
        'Tax clearance certificate',
        'Proof of financial capacity (minimum MWK 50,000,000)',
        'Comprehensive insurance coverage for postal items and operations',
        'Postal network infrastructure and equipment plan',
        'Mail processing facility documentation and specifications',
        'Staff training and qualification certificates',
        'Quality assurance and service standards documentation',
        'Customer complaint handling procedures',
        'Postal addressing system compliance',
        'Security measures for mail handling and storage',
        'Environmental impact assessment',
        'Business plan for postal operations',
        'Proof of technical capability for postal services',
        'Compliance with international postal standards'
      ],
      benefits: [
        'Authorization for comprehensive postal services nationwide',
        'Access to national and international postal networks',
        'Ability to handle domestic and international mail',
        'Access to government and corporate contracts',
        'Consumer protection through regulated standards',
        'Brand credibility and market recognition',
        'Participation in postal sector initiatives',
        'Revenue sharing opportunities with postal authority',
        'Access to postal infrastructure and technology',
        'Eligibility for postal development programs',
        'Participation in postal policy development'
      ]
    },


    // Courier License Options
    {
      id: 'international-commercial-a',
      name: 'International Commercial Courier Services License (Category A)',
      description: 'Comprehensive international courier services license including domestic services. For operators with turnover above US$704,000.',
      icon: 'ri-global-line',
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-600',
      processingTime: '30-45 days',
      applicationFee: 100,
      annualFee: 15000,
      currency: 'USD',
      validityPeriod: '1 year',
      renewalRequired: true,
      category: 'international',
      serviceType: 'courier',
      requirements: [
        'Valid business registration certificate',
        'Tax clearance certificate',
        'Proof of financial resources (minimum US$704,000 turnover)',
        'Insurance coverage for customer services',
        'Fleet registration and vehicle documentation',
        'Staff security clearance certificates',
        'International partnership agreements',
        'Customs clearance procedures documentation',
        'Quality assurance and service standards',
        'Customer complaint handling procedures'
      ],
      benefits: [
        'Authorization for international courier services',
        'Includes domestic service authorization',
        'Access to international courier networks',
        'Eligibility for government contracts',
        'Brand credibility and market recognition',
        'Consumer protection compliance'
      ]
    },
    {
      id: 'international-commercial-b',
      name: 'International Commercial Courier Services License (Category B)',
      description: 'International courier services license for smaller operators with turnover below US$704,000. Includes domestic services.',
      icon: 'ri-global-line',
      iconBg: 'bg-indigo-100',
      iconColor: 'text-indigo-600',
      processingTime: '30-45 days',
      applicationFee: 100,
      annualFee: 8000,
      currency: 'USD',
      validityPeriod: '1 year',
      renewalRequired: true,
      category: 'international',
      serviceType: 'courier',
      requirements: [
        'Valid business registration certificate',
        'Tax clearance certificate',
        'Proof of turnover below US$704,000',
        'Insurance coverage for customer services',
        'Fleet registration and vehicle documentation',
        'Staff security clearance certificates',
        'International partnership agreements',
        'Customs clearance procedures documentation',
        'Quality assurance and service standards',
        'Customer complaint handling procedures'
      ],
      benefits: [
        'Authorization for international courier services',
        'Includes domestic service authorization',
        'Lower annual fees for smaller operators',
        'Access to international courier networks',
        'Eligibility for government contracts',
        'Consumer protection compliance'
      ]
    },
    {
      id: 'domestic-commercial',
      name: 'Domestic Commercial Courier Service License',
      description: 'Authorization for domestic courier services throughout Malawi including express delivery and parcel services.',
      icon: 'ri-truck-line',
      iconBg: 'bg-orange-100',
      iconColor: 'text-orange-600',
      processingTime: '21-30 days',
      applicationFee: 100,
      annualFee: 5000,
      currency: 'USD',
      validityPeriod: '1 year',
      renewalRequired: true,
      category: 'domestic',
      serviceType: 'courier',
      requirements: [
        'Valid business registration certificate',
        'Tax clearance certificate',
        'Proof of business premises ownership or lease',
        'Insurance coverage for customer services',
        'Fleet registration and vehicle documentation',
        'Driver licenses and employment contracts',
        'Staff security clearance certificates',
        'Operational procedures and service standards',
        'Customer service and complaint handling procedures',
        'Financial statements and bank guarantees'
      ],
      benefits: [
        'Authorization for domestic courier services nationwide',
        'Access to government and corporate contracts',
        'Consumer protection through regulated standards',
        'Brand credibility and market recognition',
        'Participation in postal sector initiatives',
        'Protection of business interests'
      ]
    },
    {
      id: 'domestic-inter-city',
      name: 'Domestic Inter-City Commercial Courier License',
      description: 'Authorization for courier services between major cities (Blantyre, Zomba, Lilongwe, Mzuzu). Includes freight forwarding.',
      icon: 'ri-roadster-line',
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600',
      processingTime: '14-21 days',
      applicationFee: 100,
      annualFee: 2000,
      currency: 'USD',
      validityPeriod: '1 year',
      renewalRequired: true,
      category: 'domestic',
      serviceType: 'courier',
      requirements: [
        'Valid business registration certificate',
        'Tax clearance certificate',
        'Proof of business premises in target cities',
        'Insurance coverage for customer services',
        'Inter-city transport fleet documentation',
        'Route planning and scheduling documentation',
        'Warehouse and distribution center specifications',
        'Staff qualifications and training records',
        'Service level agreements and performance metrics',
        'Customer service procedures'
      ],
      benefits: [
        'Authorization for inter-city courier services',
        'Freight forwarding service authorization',
        'Access to major city networks',
        'Lower fees for focused operations',
        'Eligibility for corporate contracts',
        'Regulated service standards'
      ]
    },
    {
      id: 'domestic-intra-city',
      name: 'Domestic Intra-City Commercial Courier License',
      description: 'Authorization for courier services within one major city (Blantyre, Zomba, Lilongwe, or Mzuzu).',
      icon: 'ri-map-pin-line',
      iconBg: 'bg-green-100',
      iconColor: 'text-green-600',
      processingTime: '7-14 days',
      applicationFee: 100,
      annualFee: 1000,
      currency: 'USD',
      validityPeriod: '1 year',
      renewalRequired: true,
      category: 'domestic',
      serviceType: 'courier',
      requirements: [
        'Valid business registration certificate',
        'Tax clearance certificate',
        'Proof of business premises in target city',
        'Insurance coverage for customer services',
        'City transport fleet documentation',
        'Local delivery route planning',
        'Same-day delivery capabilities',
        'Staff qualifications and training records',
        'Real-time tracking system implementation',
        'Customer service procedures'
      ],
      benefits: [
        'Authorization for intra-city courier services',
        'Focus on single city operations',
        'Lowest annual fees',
        'Same-day delivery authorization',
        'Local market specialization',
        'Streamlined operations'
      ]
    },
    {
      id: 'district-commercial',
      name: 'District Commercial Courier License',
      description: 'Authorization for courier services within one district of Malawi. Ideal for local and regional operators.',
      icon: 'ri-community-line',
      iconBg: 'bg-teal-100',
      iconColor: 'text-teal-600',
      processingTime: '7-14 days',
      applicationFee: 100,
      annualFee: 500,
      currency: 'USD',
      validityPeriod: '1 year',
      renewalRequired: true,
      category: 'district',
      serviceType: 'courier',
      requirements: [
        'Valid business registration certificate',
        'Tax clearance certificate',
        'Proof of business premises in target district',
        'Insurance coverage for customer services',
        'District transport fleet documentation',
        'Local delivery network planning',
        'Community service capabilities',
        'Staff qualifications and training records',
        'Service standards and procedures',
        'Customer service procedures'
      ],
      benefits: [
        'Authorization for district courier services',
        'Lowest cost entry point',
        'Community-focused operations',
        'Local market specialization',
        'Simplified regulatory requirements',
        'Rural and remote area service authorization'
      ]
    }
  ];

  // Use backend data if available, otherwise use empty array
  const finalLicenseOptions = allLicenseOptions.length > 0 ? allLicenseOptions : [];

  // Filter licenses based on active tab and selected category
  const filteredLicenses = finalLicenseOptions.filter(license => {
    // First filter by service type (tab)
    if (license.serviceType !== activeTab) return false;

    // Then filter by category if not 'all'
    if (selectedCategory === 'all') return true;
    if (selectedCategory === 'postal') return license.category === 'postal';
    return license.category === selectedCategory;
  });

  // Handle starting application
  const handleStartApplication = (licenseId: string) => {
    // Find the postal/courier license type ID from backend data
    const postalCourierType = licenseTypes.find(lt =>
      lt.name.toLowerCase().includes('postal') ||
      lt.name.toLowerCase().includes('courier')
    );

    if (postalCourierType) {
      // Navigate to the standardized application flow
      router.push(`/customer/applications/${postalCourierType.license_type_id}/${licenseId}`);
    } else {
      // Fallback to old system if license type not found
      router.push(`/customer/applications/courier/new?category=${licenseId}`);
    }
  };

  // Toggle details view
  const toggleDetails = (licenseId: string) => {
    setShowDetails(showDetails === licenseId ? null : licenseId);
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Breadcrumb items
  const breadcrumbs = [
    { label: 'Dashboard', href: '/customer' },
    { label: 'Applications', href: '/customer/applications' },
    { label: 'Postal & Courier Services' }
  ];

  // Show loading state
  if ( licenseLoading) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading license information...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <i className="ri-error-warning-line text-4xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Failed to load license information
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <button
              type="button"
              onClick={() => window.location.reload()}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout breadcrumbs={breadcrumbs}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Postal & Courier Services Application</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">Choose between postal services or courier services license applications</p>
            </div>
          </div>
        </div>

        {/* Service Type Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              <button
                type="button"
                onClick={() => {
                  setActiveTab('courier');
                  setSelectedCategory('all');
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'courier'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <i className="ri-truck-line mr-2"></i>
                Courier Services
              </button>
              <button
                type="button"
                onClick={() => {
                  setActiveTab('postal');
                  setSelectedCategory('all');
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'postal'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <i className="ri-mail-line mr-2"></i>
                Postal Services
              </button>
              <button
                type="button"
                onClick={() => {
                  setActiveTab('mnas');
                  setSelectedCategory('all');
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'mnas'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <i className="ri-map-pin-line mr-2"></i>
                MNAS
              </button>
            </nav>
          </div>
        </div>

        {/* MNAS Content */}
        {activeTab === 'mnas' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="ri-map-pin-line text-2xl text-blue-600 dark:text-blue-400"></i>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Malawi National Addressing System (MNAS)
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Manage and register postal service locations across Malawi. Add new postal facilities,
                update existing locations, and maintain the national addressing database for efficient mail delivery.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Add Business Location */}
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/40 rounded-lg flex items-center justify-center mb-4">
                  <i className="ri-add-line text-xl text-blue-600 dark:text-blue-400"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Add Business Location</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Register your business location in the Malawi National Addressing System. Select your region and provide address details.
                </p>
                <button
                  type="button"
                  onClick={() => setShowAddLocationModal(true)}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                >
                  Add My Location
                </button>
              </div>

              {/* View My Locations */}
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/40 rounded-lg flex items-center justify-center mb-4">
                  <i className="ri-map-pin-line text-xl text-green-600 dark:text-green-400"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">My Locations</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  View and manage your registered business locations in the MNAS database.
                </p>
                <button type="button" className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                  View My Locations
                </button>
              </div>
            </div>

            {/* Available Regions */}
            <div className="mt-8">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Available Regions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">Northern</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Region</div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">Central</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Region</div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">Southern</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Region</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* License Options */}
        {activeTab !== 'mnas' && (
          <>
            {filteredLicenses.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 dark:text-gray-500 mb-4">
                  <i className="ri-file-list-line text-4xl"></i>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No license categories available
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {activeTab === 'courier'
                    ? 'No courier license categories found in the system.'
                    : 'No postal license categories found in the system.'
                  }
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                {filteredLicenses.map((license) => (
            <div key={license.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              {/* Header */}
              <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-start">
                  <div className={`w-8 h-8 ${license.iconBg} dark:bg-opacity-20 rounded-lg flex items-center justify-center mr-2 flex-shrink-0`}>
                    <i className={`${license.icon} text-sm ${license.iconColor} dark:text-opacity-80`}></i>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 leading-tight">{license.name}</h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">{license.description}</p>
                  </div>
                </div>
              </div>

              {/* Key Information */}
              <div className="p-3">
                <div className="grid grid-cols-2 gap-2 mb-3">
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Processing</div>
                    <div className="text-xs font-medium text-gray-900 dark:text-gray-100">{license.processingTime}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">App Fee</div>
                    <div className="text-xs font-medium text-gray-900 dark:text-gray-100">
                      {formatCurrency(license.applicationFee, license.currency)}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Annual Fee</div>
                    <div className="text-xs font-medium text-gray-900 dark:text-gray-100">
                      {formatCurrency(license.annualFee, license.currency)}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Validity</div>
                    <div className="text-xs font-medium text-gray-900 dark:text-gray-100">{license.validityPeriod}</div>
                  </div>
                </div>

                {/* Requirements Preview */}
                <div className="mb-3">
                  <h4 className="text-xs font-medium text-gray-900 dark:text-gray-100 mb-1">Key Requirements:</h4>
                  <div className="space-y-0.5">
                    {license.requirements.slice(0, 2).map((requirement, index) => (
                      <div key={index} className="flex items-start text-xs text-gray-600 dark:text-gray-400">
                        <i className="ri-check-line text-green-500 mr-1 text-xs mt-0.5 flex-shrink-0"></i>
                        <span className="truncate">{requirement}</span>
                      </div>
                    ))}
                    {license.requirements.length > 2 && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        +{license.requirements.length - 2} more requirements
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => toggleDetails(license.id)}
                    className="flex-1 px-2 py-1.5 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 rounded text-xs font-medium transition-colors"
                  >
                    <i className="ri-information-line mr-1"></i>
                    {showDetails === license.id ? 'Hide' : 'Details'}
                  </button>
                  <button
                    type="button"
                    onClick={() => handleStartApplication(license.id)}
                    className="flex-1 px-2 py-1.5 bg-primary text-white hover:bg-red-700 rounded text-xs font-medium transition-colors"
                  >
                    <i className="ri-file-add-line mr-1"></i>
                    Apply
                  </button>
                </div>
              </div>

              {/* Detailed Information */}
              {showDetails === license.id && (
                <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
                  <div className="p-4 space-y-4">
                    {/* All Requirements */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">All Requirements:</h4>
                      <div className="grid grid-cols-1 gap-1">
                        {license.requirements.map((requirement, index) => (
                          <div key={index} className="flex items-start text-xs text-gray-600 dark:text-gray-400">
                            <i className="ri-check-line text-green-500 mr-1 mt-0.5 flex-shrink-0 text-xs"></i>
                            {requirement}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Benefits */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Benefits:</h4>
                      <div className="grid grid-cols-1 gap-1">
                        {license.benefits.map((benefit, index) => (
                          <div key={index} className="flex items-start text-xs text-gray-600 dark:text-gray-400">
                            <i className="ri-star-line text-yellow-500 mr-1 mt-0.5 flex-shrink-0 text-xs"></i>
                            {benefit}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Additional Information */}
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3 border border-blue-200 dark:border-blue-800">
                      <div className="flex items-start">
                        <i className="ri-information-line text-blue-600 dark:text-blue-400 text-sm mr-2 mt-0.5"></i>
                        <div>
                          <h5 className="text-xs font-medium text-blue-900 dark:text-blue-100 mb-1">Additional Information</h5>
                          <p className="text-xs text-blue-700 dark:text-blue-300">
                            Annual levy of 1.5% of operating revenue applies to all licenses. 
                            {license.renewalRequired && ' License renewal is required annually.'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
                ))}
              </div>
            )}
          </>
        )}

        {/* Help Section */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/40 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
              <i className="ri-information-line text-blue-600 dark:text-blue-400 text-sm"></i>
            </div>
            <div>
              <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">Need Help Choosing?</h3>
              <p className="text-blue-700 dark:text-blue-300 text-xs mb-3">
                Not sure which postal or courier license type is right for you? Consider your target market, service area, and business model.
                Our support team can help you determine the best option based on your specific business needs.
              </p>
              <div className="flex flex-col sm:flex-row gap-2">
                <Link
                  href="/customer/help"
                  className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white hover:bg-blue-700 rounded-md text-xs font-medium transition-colors"
                >
                  <i className="ri-question-line mr-1"></i>
                  Visit Help Center
                </Link>
                <Link
                  href="/customer/resources"
                  className="inline-flex items-center px-3 py-1.5 border border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300 bg-white dark:bg-blue-900/20 hover:bg-blue-50 dark:hover:bg-blue-900/40 rounded-md text-xs font-medium transition-colors"
                >
                  <i className="ri-customer-service-2-line mr-1"></i>
                  Contact Support
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Location Modal */}
      <AddLocationModal
        isOpen={showAddLocationModal}
        onClose={() => setShowAddLocationModal(false)}
        onSuccess={() => {
          // TODO: Refresh locations list when implemented
          console.log('Location added successfully');
        }}
      />
    </CustomerLayout>
  );
};

export default PostalCourierApplicationPage;
