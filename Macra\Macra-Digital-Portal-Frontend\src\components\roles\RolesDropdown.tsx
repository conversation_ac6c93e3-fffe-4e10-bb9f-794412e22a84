import { useState, useRef, useEffect } from 'react';

interface Role {
  role_id: string | number;
  name: string;
}

interface RolesDropdownProps {
  roles: Role[];
  formData: {
    role_ids: (string | number)[];
    [key: string]: any;
  };
  handleRoleToggle: (roleId: string | number) => void;
}

function RolesDropdown({ roles, formData, handleRoleToggle }: RolesDropdownProps) {
  const [open, setOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const selectedRolesCount = formData.role_ids.length;

  return (
    <div className="w-full" ref={dropdownRef}>
      <label className="block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm">
        Roles
      </label>

      <div className="relative">
        <button
          type="button"
          onClick={() => setOpen(!open)}
          className={`
            px-3 py-2 border rounded-md shadow-sm transition-colors duration-200 text-left cursor-pointer
            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
            focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
            w-full
            ${open
              ? 'border-red-500 ring-2 ring-red-500'
              : 'border-gray-300 dark:border-gray-600'}
          `}
          aria-haspopup="listbox"
          aria-expanded={open}
          aria-labelledby="roles-label"
        >
          <span className="block truncate">
            {selectedRolesCount === 0
              ? 'Select roles...'
              : `${selectedRolesCount} role${selectedRolesCount > 1 ? 's' : ''} selected`}
          </span>
          <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg
              className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${open ? 'rotate-180' : ''}`}
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z"
                clipRule="evenodd"
              />
            </svg>
          </span>
        </button>

        {open && (
          <ul
            className="absolute z-[9999] mt-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto"
            role="listbox"
            aria-labelledby="roles-label"
          >
            {roles.length > 0 ? (
              roles.map((role) => {
                const isSelected = formData.role_ids.includes(role.role_id);
                return (
                  <li
                    key={role.role_id}
                    className="cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700"
                    onClick={() => handleRoleToggle(role.role_id)}
                    role="option"
                    aria-selected={isSelected}
                  >
                    <span
                      className={`block truncate ${
                        isSelected
                          ? 'font-semibold text-red-600 dark:text-red-300'
                          : 'font-normal text-gray-900 dark:text-gray-100'
                      }`}
                    >
                      {role.name}
                    </span>
                    {isSelected && (
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300">
                        <svg
                          className="h-5 w-5"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </span>
                    )}
                  </li>
                );
              })
            ) : (
              <li className="text-gray-500 dark:text-gray-400 px-4 py-2">Loading roles...</li>
            )}
          </ul>
        )}
      </div>
    </div>
  );
}

export default RolesDropdown;
