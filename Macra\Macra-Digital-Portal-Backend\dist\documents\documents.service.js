"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const documents_entity_1 = require("../entities/documents.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
let DocumentsService = class DocumentsService {
    documentsRepository;
    constructor(documentsRepository) {
        this.documentsRepository = documentsRepository;
    }
    paginateConfig = {
        sortableColumns: ['created_at', 'updated_at', 'file_name', 'document_type'],
        searchableColumns: ['file_name', 'document_type', 'entity_type'],
        defaultSortBy: [['created_at', 'DESC']],
        defaultLimit: 10,
        maxLimit: 100,
        relations: ['application', 'creator', 'updater'],
    };
    async create(createDocumentDto, createdBy) {
        const document = this.documentsRepository.create({
            ...createDocumentDto,
            is_required: createDocumentDto.is_required || false,
            created_by: createdBy,
        });
        return this.documentsRepository.save(document);
    }
    async findAll(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.documentsRepository, this.paginateConfig);
    }
    async findOne(id) {
        const document = await this.documentsRepository.findOne({
            where: { document_id: id },
            relations: ['application', 'creator', 'updater'],
        });
        if (!document) {
            throw new common_1.NotFoundException(`Document with ID ${id} not found`);
        }
        return document;
    }
    async findByApplication(applicationId) {
        return this.documentsRepository.find({
            where: { application_id: applicationId },
            relations: ['application', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async findByEntity(entityType, entityId) {
        return this.documentsRepository.find({
            where: { entity_type: entityType, entity_id: entityId },
            relations: ['application', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async findByDocumentType(documentType) {
        return this.documentsRepository.find({
            where: { document_type: documentType },
            relations: ['application', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async findRequiredDocuments() {
        return this.documentsRepository.find({
            where: { is_required: true },
            relations: ['application', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async update(id, updateDocumentDto, updatedBy) {
        const document = await this.findOne(id);
        Object.assign(document, updateDocumentDto, { updated_by: updatedBy });
        return this.documentsRepository.save(document);
    }
    async remove(id) {
        const document = await this.findOne(id);
        await this.documentsRepository.softDelete(document.document_id);
    }
    async getDocumentStats() {
        const stats = await this.documentsRepository
            .createQueryBuilder('document')
            .select('document.document_type', 'document_type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('document.document_type')
            .getRawMany();
        return stats.reduce((acc, stat) => {
            acc[stat.document_type] = parseInt(stat.count);
            return acc;
        }, {});
    }
    async getDocumentsByMimeType(mimeType) {
        return this.documentsRepository.find({
            where: { mime_type: mimeType },
            relations: ['application', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async getTotalFileSize() {
        const result = await this.documentsRepository
            .createQueryBuilder('document')
            .select('SUM(document.file_size)', 'total_size')
            .getRawOne();
        return parseInt(result.total_size) || 0;
    }
};
exports.DocumentsService = DocumentsService;
exports.DocumentsService = DocumentsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(documents_entity_1.Documents)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], DocumentsService);
//# sourceMappingURL=documents.service.js.map