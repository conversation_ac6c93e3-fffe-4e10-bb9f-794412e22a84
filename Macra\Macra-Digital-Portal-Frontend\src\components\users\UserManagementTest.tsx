'use client';

import { useState, useEffect } from 'react';
import { userService, User } from '../../services/userService';
import { roleService, Role } from '../../services/roleService';
import { permissionService } from '../../services/permissionService';
import { Permission } from '../../services/userService';

export default function UserManagementTest() {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    runTests();
  }, []);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, result]);
  };

  const runTests = async () => {
    setLoading(true);
    setError(null);
    setTestResults([]);

    try {
      addTestResult('🧪 Starting User Management Tests...');

      // Test 1: Load Users
      addTestResult('📋 Test 1: Loading users...');
      const usersResponse = await userService.getUsers({ page: 1, limit: 10 });
      setUsers(usersResponse.data || []);
      addTestResult(`✅ Loaded ${usersResponse.data?.length || 0} users (Total: ${usersResponse.meta?.totalItems || 0})`);

      // Test 2: Load Roles
      addTestResult('🛡️ Test 2: Loading roles...');
      const rolesData = await roleService.getRoles({ page: 1, limit: 100 });
      setRoles(rolesData.data || []);
      addTestResult(`✅ Loaded ${rolesData.data?.length || 0} roles`);

      // Test 3: Load Permissions
      addTestResult('🔑 Test 3: Loading permissions...');
      const permissionsData = await permissionService.getPermissions();
      setPermissions(permissionsData);
      addTestResult(`✅ Loaded ${permissionsData.length} permissions`);

      // Test 4: Check Multiple Roles
      addTestResult('👥 Test 4: Checking multiple roles support...');
      const usersWithMultipleRoles = (usersResponse.data || []).filter(user =>
        user.roles && user.roles.length > 1
      );
      addTestResult(`✅ Found ${usersWithMultipleRoles.length} users with multiple roles`);

      // Test 5: Check Role-Permission Relationships
      addTestResult('🔗 Test 5: Checking role-permission relationships...');
      const rolesWithPermissions = (rolesData.data || []).filter(role =>
        role.permissions && role.permissions.length > 0
      );
      addTestResult(`✅ Found ${rolesWithPermissions.length} roles with permissions`);

      // Test 6: Create Test User (if possible)
      addTestResult('👤 Test 6: Testing user creation...');
      try {
        const testUser = await userService.createUser({
          email: `test-${Date.now()}@example.com`,
          password: 'TestPassword123!',
          first_name: 'Test',
          last_name: 'User',
          phone: '+265999000999',
          status: 'active',
          role_ids: (rolesData.data || []).slice(0, 2).map(role => role.role_id), // Assign first 2 roles
        });
        addTestResult(`✅ Created test user with ${testUser.roles?.length || 0} roles`);
        
        // Clean up - delete the test user
        await userService.deleteUser(testUser.user_id);
        addTestResult(`🗑️ Cleaned up test user`);
      } catch (err: any) {
        addTestResult(`⚠️ User creation test failed: ${err.message}`);
      }

      addTestResult('🎉 All tests completed!');

    } catch (err: any) {
      setError(err.message || 'Test failed');
      addTestResult(`❌ Test failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const getUserRoleNames = (user: User) => {
    if (!user.roles || user.roles.length === 0) return 'No roles';
    return user.roles.map(role => role.name).join(', ');
  };

  const getRolePermissionCount = (role: Role) => {
    return role.permissions?.length || 0;
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">User Management System Test</h1>
        <p className="text-gray-600">Testing the multiple roles user management functionality</p>
      </div>

      {/* Test Results */}
      <div className="mb-8 bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
        <h2 className="text-white font-bold mb-2">Test Console:</h2>
        <div className="space-y-1 max-h-64 overflow-y-auto">
          {testResults.map((result, index) => (
            <div key={index}>{result}</div>
          ))}
          {loading && <div className="animate-pulse">Running tests...</div>}
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Users</h3>
          <p className="text-3xl font-bold text-blue-600">{users.length}</p>
          <p className="text-sm text-gray-500">Total users loaded</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Roles</h3>
          <p className="text-3xl font-bold text-green-600">{roles.length}</p>
          <p className="text-sm text-gray-500">Available roles</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Permissions</h3>
          <p className="text-3xl font-bold text-purple-600">{permissions.length}</p>
          <p className="text-sm text-gray-500">System permissions</p>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow mb-8">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Users with Multiple Roles</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Roles
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.slice(0, 5).map((user) => (
                <tr key={user.user_id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {user.first_name} {user.last_name}
                    </div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{getUserRoleNames(user)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Roles Table */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Roles and Permissions</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Permissions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {roles.map((role) => (
                <tr key={role.role_id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 capitalize">{role.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{role.description || 'No description'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{getRolePermissionCount(role)} permissions</div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Refresh Button */}
      <div className="mt-8 text-center">
        <button
          onClick={runTests}
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Running Tests...
            </>
          ) : (
            <>
              <i className="ri-refresh-line mr-2"></i>
              Run Tests Again
            </>
          )}
        </button>
      </div>
    </div>
  );
}
