'use client';

import React, { useEffect, useState } from 'react';
import { TextInput, CountryDropdown } from '@/components/forms';
import { ApplicantInfoData, ApplicationFormComponentProps } from './index';
import { useAddresses, initialAddressData, CreateAddressData } from '@/hooks/useAddressing';

interface ApplicantInfoProps extends ApplicationFormComponentProps {
  data: ApplicantInfoData;
  onChange: (data: ApplicantInfoData) => void;
}


const ApplicantInfo: React.FC<ApplicantInfoProps> = ({
  data,
  onChange,
  errors = {},
  disabled = false
}) => {
  const handleInputChange = (field: keyof ApplicantInfoData, value: string) => {
    onChange({
      ...data,
      [field]: value
    });
  };
  
  const { postcodeSuggestions, debouncedSearchPostcodes, createAddress } = useAddresses();
  const [region, setRegion] = useState('');
  const [addressData, setAddressData] = useState<CreateAddressData>(initialAddressData);
  const [canAddNewEntry, setCanAddNewEntry] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  // validate address completeness
  useEffect(() => {
    const isComplete =
      addressData.address_line_1 &&
      addressData.country &&
      addressData.city &&
      addressData.postal_code &&
      addressData.address_origin &&
      addressData.address_type;

    setCanAddNewEntry(Boolean(isComplete));
  }, [addressData]);

  // ✅ Define save handler properly
  const handleSavePostalAddress = async () => {
    try {
      setSubmitting(true);
      console.log('Saving Postal Address:', addressData);
      await createAddress(addressData); // You can enhance: pass origin/type here
      setSubmitting(false);
    } catch (err) {
      console.error('Failed to save postal address:', err);
      setSubmitting(false);
    }
  };

  const handleSavePhysicalAddress = async () => {
    try {
      setSubmitting(true);
      console.log('Saving Postal Address:', addressData);
      await createAddress(addressData); // You can enhance: pass origin/type here
      setSubmitting(false);
    } catch (err) {
      console.error('Failed to save postal address:', err);
      setSubmitting(false);
    }
  };

  // rest of your code, including JSX return, is already correct


  return (
    <div className="space-y-8">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Applicant Information
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Please provide your personal and contact information
        </p>
      </div>

      {/* Personal Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Personal Information
        </h3>
        
        <div className="grid grid-cols-1 gap-6">
          <TextInput
            label="Applicant Name"
            value={data.applicantName}
            onChange={(e) => handleInputChange('applicantName', e.target.value)}
            placeholder="Enter full name or company name"
            required
            disabled={disabled}
            error={errors.applicantName}
          />
        </div>
      </div>

      {/* Address Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Postal Address */}
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            <i className="ri-mail-line mr-2"></i>
            Postal Address
          </h3>
          <div className="space-y-4">
            {/* Country Dropdown (Always Visible) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Country *</label>
              <CountryDropdown
                value={addressData.country}
                onChange={(value) => {
                  setAddressData(prev => ({ ...prev, country: value }));
                  if (value === 'Malawi') {
                    setRegion('');
                    setAddressData(prev => ({ ...prev, city: '', postal_code: '', address_line_1: '', address_line_2: '' }));
                  }
                }}
                className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                required
              />
            </div>
            {addressData.country === 'Malawi' ? (
              <>
                {/* Region Dropdown */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Region *</label>
                  <select
                    className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                    value={region || ''}
                    onChange={(e) => {
                      const selectedRegion = e.target.value;
                      setRegion(selectedRegion);
                      setAddressData(prev => ({
                        ...prev,
                        country: 'Malawi',
                        city: '',
                        postal_code: '',
                        address_line_1: '',
                        address_line_2: ''
                      }));
                      debouncedSearchPostcodes({ region: selectedRegion });
                    }}
                    required
                  >
                    <option value="">Select a region</option>
                    {['Northern', 'Central', 'Southern'].map(region => (
                      <option key={region} value={region}>{region}</option>
                    ))}
                  </select>
                </div>

                {/* District Dropdown */}
                {region && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">District *</label>
                    <select
                      className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                      value={addressData.city || ''}
                      onChange={(e) => {
                        const selectedDistrict = e.target.value;
                        setAddressData(prev => ({
                          ...prev,
                          city: selectedDistrict,
                          postal_code: '',
                          address_line_1: '',
                          address_line_2: ''
                        }));
                        debouncedSearchPostcodes({ region, district: selectedDistrict });
                      }}
                      required
                    >
                      <option value="">Select a district</option>
                      {[...new Set(postcodeSuggestions
                        .filter(p => p.region === region)
                        .map(p => p.district))].map(d => (
                        <option key={d} value={d}>{d}</option>
                      ))}
                    </select>
                  </div>
                )}

                {/* Location Dropdown */}
                {addressData.city && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location *</label>
                    <select
                      className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                      value={addressData.postal_code || ''}
                      onChange={(e) => {
                        const selectedLocation = e.target.value;
                        const match = postcodeSuggestions.find(p =>
                          p.region === region &&
                          p.district === addressData.city &&
                          p.postal_code === selectedLocation
                        );
                        if (match) {
                          setAddressData(prev => ({ ...prev, postal_code: match.postal_code }));
                        }
                      }}
                      required
                      disabled={!!addressData.postal_code}
                    >
                      <option value="">Select a location</option>
                      {postcodeSuggestions
                        .filter(p =>
                          p.region === region &&
                          p.district === addressData.city)
                        .map(loc => (
                          <option key={loc.postal_code_id} value={loc.postal_code}>{loc.location}</option>
                        ))}
                    </select>
                  </div>
                )}

                {/* Postal Code Display */}
                {addressData.postal_code && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Postal Code *</label>
                    <TextInput
                      value={addressData.postal_code}
                      readOnly
                      disabled
                      className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                    />
                  </div>
                )}

                {/* Address Line 1 & 2 */}
                {addressData.postal_code && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Line 1 *</label>
                      <TextInput
                        placeholder="e.g., P.O. Box or P/Bag"
                        value={addressData.address_line_1}
                        onChange={(e) => setAddressData(prev => ({ ...prev, address_line_1: e.target.value }))}
                        required
                        className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Line 2</label>
                      <TextInput
                        placeholder="Optional details like Building Number, Block, etc."
                        value={addressData.address_line_2}
                        onChange={(e) => setAddressData(prev => ({ ...prev, address_line_2: e.target.value }))}
                        className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                      />
                    </div>
                  </>
                )}
              </>
            ) : (
              <>
                {/* Manual entry for non-Malawi countries */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Postal Code *</label>
                  <TextInput
                    value={addressData.postal_code}
                    onChange={(e) => setAddressData(prev => ({ ...prev, postal_code: e.target.value }))}
                    required
                    className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">City *</label>
                  <TextInput
                    value={addressData.city}
                    onChange={(e) => setAddressData(prev => ({ ...prev, city: e.target.value }))}
                    required
                    className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Line 1 *</label>
                  <TextInput
                    value={addressData.address_line_1}
                    onChange={(e) => setAddressData(prev => ({ ...prev, address_line_1: e.target.value }))}
                    required
                    className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address Line 2</label>
                  <TextInput
                    value={addressData.address_line_2}
                    onChange={(e) => setAddressData(prev => ({ ...prev, address_line_2: e.target.value }))}
                    className="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm"
                  />
                </div>
              </>
            )}
            <div className="pt-4">
              <button
                type="button"
                className="px-4 py-2 bg-primary text-white rounded disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={!canAddNewEntry || submitting}
                onClick={() => {
                  console.log('Saving Postal Address...', addressData);
                  handleSavePostalAddress();
                }}
              >
                {submitting ? 'Saving...' : 'Save Postal Address'}
              </button>
            </div>
          </div>

        </div>

      </div>
      {/* Contact Information */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Contact Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TextInput
            type="tel"
            label="Telephone"
            value={data.telephone}
            onChange={(e) => handleInputChange('telephone', e.target.value)}
            placeholder="+265 123 456 789"
            required
            disabled={disabled}
            error={errors.telephone}
            helperText="Include country code (e.g., +265)"
          />
          
          <TextInput
            type="tel"
            label="Fax"
            value={data.fax}
            onChange={(e) => handleInputChange('fax', e.target.value)}
            placeholder="+265 123 456 789"
            disabled={disabled}
            error={errors.fax}
            helperText="Include country code (optional)"
          />
        </div>
        
        <div className="grid grid-cols-1 gap-6">
          <TextInput
            type="email"
            label="Email Address"
            value={data.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="<EMAIL>"
            required
            disabled={disabled}
            error={errors.email}
            helperText="We'll use this email for all communications"
          />
        </div>
      </div>

      {/* Information Notice */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <div className="flex items-start">
          <i className="ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"></i>
          <div>
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
              Important Information
            </h4>
            <p className="text-blue-700 dark:text-blue-300 text-sm">
              Please ensure all information is accurate and up-to-date. This information will be used 
              for official correspondence and license documentation.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicantInfo;
function handleSubmit(arg0: () => void) {
  throw new Error('Function not implemented.');
}

