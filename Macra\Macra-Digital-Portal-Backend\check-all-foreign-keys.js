const mysql = require('mysql2/promise');

async function checkAllForeignKeys() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'macra_db'
  });

  try {
    console.log('🔍 Checking all foreign key constraints in the database...\n');

    // Get all foreign key constraints
    const [constraints] = await connection.execute(`
      SELECT 
        kcu.TABLE_NAME,
        kcu.COLUMN_NAME,
        kcu.CONSTRAINT_NAME,
        kcu.REFERENCED_TABLE_NAME,
        kcu.REFERENCED_COLUMN_NAME,
        rc.DELETE_RULE,
        rc.UPDATE_RULE
      FROM 
        INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
      INNER JOIN 
        INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
        ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
        AND kcu.CONSTRAINT_SCHEMA = rc.CONSTRAINT_SCHEMA
      WHERE 
        kcu.CONSTRAINT_SCHEMA = 'macra_db'
        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
      ORDER BY 
        kcu.TABLE_NAME, kcu.COLUMN_NAME
    `);

    console.log(`Found ${constraints.length} foreign key constraints:\n`);

    // Check each constraint for potential issues
    for (const constraint of constraints) {
      const { TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME, DELETE_RULE, UPDATE_RULE } = constraint;
      
      console.log(`📋 ${TABLE_NAME}.${COLUMN_NAME} -> ${REFERENCED_TABLE_NAME}.${REFERENCED_COLUMN_NAME}`);
      console.log(`   Constraint: ${CONSTRAINT_NAME}`);
      console.log(`   Rules: DELETE ${DELETE_RULE}, UPDATE ${UPDATE_RULE}`);

      // Check column types match
      const [sourceCol] = await connection.execute(`
        SELECT COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'macra_db' 
        AND TABLE_NAME = ? 
        AND COLUMN_NAME = ?
      `, [TABLE_NAME, COLUMN_NAME]);

      const [targetCol] = await connection.execute(`
        SELECT COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'macra_db' 
        AND TABLE_NAME = ? 
        AND COLUMN_NAME = ?
      `, [REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME]);

      if (sourceCol.length > 0 && targetCol.length > 0) {
        const sourceType = sourceCol[0].COLUMN_TYPE;
        const targetType = targetCol[0].COLUMN_TYPE;
        
        if (sourceType !== targetType) {
          console.log(`   ⚠️  TYPE MISMATCH: ${sourceType} != ${targetType}`);
        } else {
          console.log(`   ✅ Types match: ${sourceType}`);
        }
      } else {
        console.log(`   ❌ Column not found!`);
      }

      console.log('');
    }

    // Check for orphaned records
    console.log('\n🔍 Checking for orphaned records...\n');
    
    for (const constraint of constraints) {
      const { TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME } = constraint;
      
      try {
        const [orphans] = await connection.execute(`
          SELECT COUNT(*) as orphan_count
          FROM ${TABLE_NAME} t1
          WHERE t1.${COLUMN_NAME} IS NOT NULL
          AND t1.${COLUMN_NAME} NOT IN (
            SELECT ${REFERENCED_COLUMN_NAME} 
            FROM ${REFERENCED_TABLE_NAME} 
            WHERE ${REFERENCED_COLUMN_NAME} IS NOT NULL
          )
        `);

        const orphanCount = orphans[0].orphan_count;
        if (orphanCount > 0) {
          console.log(`⚠️  ${TABLE_NAME}.${COLUMN_NAME}: ${orphanCount} orphaned records`);
        } else {
          console.log(`✅ ${TABLE_NAME}.${COLUMN_NAME}: No orphaned records`);
        }
      } catch (error) {
        console.log(`❌ ${TABLE_NAME}.${COLUMN_NAME}: Error checking orphans - ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Error checking foreign keys:', error.message);
  } finally {
    await connection.end();
  }
}

checkAllForeignKeys();
