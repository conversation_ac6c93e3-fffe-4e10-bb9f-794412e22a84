-- Quick fix for consumer_affairs_complaints foreign key constraint error
-- Run these commands to fix the immediate issue

-- Step 1: Check the current problem
SELECT 
    COUNT(*) as problem_records,
    'consumer_affairs_complaints with invalid created_by' as issue
FROM consumer_affairs_complaints cac
LEFT JOIN users u ON cac.created_by = u.user_id
WHERE cac.created_by IS NOT NULL AND u.user_id IS NULL;

-- Step 2: Fix the data - set invalid created_by to NULL
UPDATE consumer_affairs_complaints 
SET created_by = NULL 
WHERE created_by IS NOT NULL 
AND created_by NOT IN (SELECT user_id FROM users);

-- Step 3: Also fix other potential issues in the same table
UPDATE consumer_affairs_complaints 
SET updated_by = NULL 
WHERE updated_by IS NOT NULL 
AND updated_by NOT IN (SELECT user_id FROM users);

UPDATE consumer_affairs_complaints 
SET assigned_to = NULL 
WHERE assigned_to IS NOT NULL 
AND assigned_to NOT IN (SELECT user_id FROM users);

-- Step 4: Check for complainant_id issues (this is required field, so delete if invalid)
SELECT 
    COUNT(*) as invalid_complainants,
    'consumer_affairs_complaints with invalid complainant_id - will be deleted' as warning
FROM consumer_affairs_complaints cac
LEFT JOIN users u ON cac.complainant_id = u.user_id
WHERE cac.complainant_id IS NOT NULL AND u.user_id IS NULL;

-- Delete complaints with invalid complainant_id (safer than keeping orphaned data)
DELETE FROM consumer_affairs_complaints 
WHERE complainant_id IS NOT NULL 
AND complainant_id NOT IN (SELECT user_id FROM users);

-- Step 5: Verify the fix
SELECT 
    COUNT(*) as remaining_problems,
    'Should be 0 if fixed' as status
FROM consumer_affairs_complaints cac
LEFT JOIN users u ON cac.created_by = u.user_id
WHERE cac.created_by IS NOT NULL AND u.user_id IS NULL;

SELECT 'Quick fix completed. Restart your TypeORM application now.' as message;