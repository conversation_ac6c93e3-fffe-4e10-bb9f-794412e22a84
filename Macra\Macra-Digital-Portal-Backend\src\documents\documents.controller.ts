import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  ParseEnumPipe,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { DocumentsService } from './documents.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateDocumentDto } from '../dto/document/create-document.dto';
import { UpdateDocumentDto } from '../dto/document/update-document.dto';
import { Documents, DocumentType } from '../entities/documents.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('documents')
@Controller('documents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new document' })
  @ApiResponse({
    status: 201,
    description: 'Document created successfully',
    type: Documents,
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Created new document',
  })
  async create(
    @Body() createDocumentDto: CreateDocumentDto,
    @Request() req: any,
  ): Promise<Documents> {
    return this.documentsService.create(createDocumentDto, req.user.userId);
  }

  @Post('upload')
  @ApiOperation({ summary: 'Upload a document file' })
  @ApiResponse({
    status: 201,
    description: 'Document uploaded successfully',
    type: Documents,
  })
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads/documents',
        filename: (req, file, callback) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
          const ext = extname(file.originalname);
          const filename = `${file.fieldname}-${uniqueSuffix}${ext}`;
          callback(null, filename);
        },
      }),
      fileFilter: (req, file, callback) => {
        // Allow common document types
        const allowedMimes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'image/jpeg',
          'image/png',
          'image/gif',
          'text/plain',
        ];

        if (allowedMimes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(new Error('Invalid file type'), false);
        }
      },
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
      },
    }),
  )
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Uploaded document file',
  })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadData: any,
    @Request() req: any,
  ): Promise<any> {
    if (!file) {
      throw new Error('No file uploaded');
    }

    const createDocumentDto: CreateDocumentDto = {
      document_type: uploadData.document_type || 'OTHER',
      file_name: uploadData.file_name || file.originalname,
      entity_type: uploadData.entity_type,
      entity_id: uploadData.entity_id,
      file_path: file.path,
      file_size: file.size,
      mime_type: file.mimetype,
      is_required: uploadData.is_required === 'true',
      application_id: uploadData.application_id || null,
    };

    const document = await this.documentsService.create(createDocumentDto, req.user.userId);

    return {
      success: true,
      message: 'Document uploaded successfully',
      data: document,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all documents with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents list',
  })
  async findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<Documents>> {
    const result = await this.documentsService.findAll(query);
    return PaginationTransformer.transform<Documents>(result);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get document statistics' })
  @ApiResponse({
    status: 200,
    description: 'Document statistics retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed document statistics',
  })
  async getStats(): Promise<any> {
    return this.documentsService.getDocumentStats();
  }

  @Get('total-file-size')
  @ApiOperation({ summary: 'Get total file size of all documents' })
  @ApiResponse({
    status: 200,
    description: 'Total file size retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed total file size',
  })
  async getTotalFileSize(): Promise<{ totalSize: number }> {
    const totalSize = await this.documentsService.getTotalFileSize();
    return { totalSize };
  }

  @Get('by-application/:applicationId')
  @ApiOperation({ summary: 'Get documents by application' })
  @ApiParam({ name: 'applicationId', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents by application',
  })
  async findByApplication(@Param('applicationId', ParseUUIDPipe) applicationId: string): Promise<Documents[]> {
    return this.documentsService.findByApplication(applicationId);
  }

  @Get('by-entity/:entityType/:entityId')
  @ApiOperation({ summary: 'Get documents by entity' })
  @ApiParam({ name: 'entityType', description: 'Entity type' })
  @ApiParam({ name: 'entityId', description: 'Entity UUID' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents by entity',
  })
  async findByEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseUUIDPipe) entityId: string,
  ): Promise<Documents[]> {
    return this.documentsService.findByEntity(entityType, entityId);
  }

  @Get('by-document-type/:documentType')
  @ApiOperation({ summary: 'Get documents by document type' })
  @ApiParam({ name: 'documentType', enum: DocumentType, description: 'Document type' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents by type',
  })
  async findByDocumentType(
    @Param('documentType', new ParseEnumPipe(DocumentType)) documentType: DocumentType,
  ): Promise<Documents[]> {
    return this.documentsService.findByDocumentType(documentType);
  }

  @Get('by-mime-type/:mimeType')
  @ApiOperation({ summary: 'Get documents by MIME type' })
  @ApiParam({ name: 'mimeType', description: 'MIME type' })
  @ApiResponse({
    status: 200,
    description: 'Documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed documents by MIME type',
  })
  async findByMimeType(@Param('mimeType') mimeType: string): Promise<Documents[]> {
    return this.documentsService.getDocumentsByMimeType(mimeType);
  }

  @Get('required')
  @ApiOperation({ summary: 'Get required documents' })
  @ApiResponse({
    status: 200,
    description: 'Required documents retrieved successfully',
    type: [Documents],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed required documents',
  })
  async findRequiredDocuments(): Promise<Documents[]> {
    return this.documentsService.findRequiredDocuments();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get document by ID' })
  @ApiParam({ name: 'id', description: 'Document UUID' })
  @ApiResponse({
    status: 200,
    description: 'Document retrieved successfully',
    type: Documents,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Viewed document details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Documents> {
    return this.documentsService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update document' })
  @ApiParam({ name: 'id', description: 'Document UUID' })
  @ApiResponse({
    status: 200,
    description: 'Document updated successfully',
    type: Documents,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Updated document',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDocumentDto: UpdateDocumentDto,
    @Request() req: any,
  ): Promise<Documents> {
    return this.documentsService.update(id, updateDocumentDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete document' })
  @ApiParam({ name: 'id', description: 'Document UUID' })
  @ApiResponse({
    status: 200,
    description: 'Document deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.DOCUMENT_MANAGEMENT,
    resourceType: 'Document',
    description: 'Deleted document',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.documentsService.remove(id);
    return { message: 'Document deleted successfully' };
  }
}
