'use client';

import React from 'react';
import { TextInput, CountryDropdown, TextArea } from '@/components/forms';
import { CompanyProfileData, ShareholderData, DirectorData, ApplicationFormComponentProps } from './index';

interface CompanyProfileProps extends ApplicationFormComponentProps {
  data: CompanyProfileData;
  onChange: (data: CompanyProfileData) => void;
}

const CompanyProfile: React.FC<CompanyProfileProps> = ({
  data,
  onChange,
  errors = {},
  disabled = false
}) => {
  const handleInputChange = (field: keyof CompanyProfileData, value: any) => {
    onChange({
      ...data,
      [field]: value
    });
  };

  const addShareholder = () => {
    const newShareholder: ShareholderData = {
      name: '',
      nationality: '',
      address: '',
      shareholding: ''
    };
    handleInputChange('shareholders', [...data.shareholders, newShareholder]);
  };

  const updateShareholder = (index: number, field: keyof ShareholderData, value: string) => {
    const updatedShareholders = data.shareholders.map((shareholder, i) =>
      i === index ? { ...shareholder, [field]: value } : shareholder
    );
    handleInputChange('shareholders', updatedShareholders);
  };

  const removeShareholder = (index: number) => {
    const updatedShareholders = data.shareholders.filter((_, i) => i !== index);
    handleInputChange('shareholders', updatedShareholders);
  };

  const addDirector = () => {
    const newDirector: DirectorData = {
      name: '',
      nationality: '',
      address: ''
    };
    handleInputChange('directors', [...data.directors, newDirector]);
  };

  const updateDirector = (index: number, field: keyof DirectorData, value: string) => {
    const updatedDirectors = data.directors.map((director, i) =>
      i === index ? { ...director, [field]: value } : director
    );
    handleInputChange('directors', updatedDirectors);
  };

  const removeDirector = (index: number) => {
    const updatedDirectors = data.directors.filter((_, i) => i !== index);
    handleInputChange('directors', updatedDirectors);
  };

  return (
    <div className="space-y-8">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Company Profile
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide details about your company structure and ownership
        </p>
      </div>

      {/* Company Registration Details */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Company Registration Details
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TextInput
            label="Business Registration Number"
            value={data.businessRegistrationNo}
            onChange={(e) => handleInputChange('businessRegistrationNo', e.target.value)}
            placeholder="Enter registration number"
            required
            disabled={disabled}
            error={errors.businessRegistrationNo}
          />
          
          <TextInput
            label="TPIN (Tax Payer Identification Number)"
            value={data.tpin}
            onChange={(e) => handleInputChange('tpin', e.target.value)}
            placeholder="Enter TPIN"
            required
            disabled={disabled}
            error={errors.tpin}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TextInput
            type="date"
            label="Date of Incorporation"
            value={data.dateOfIncorporation}
            onChange={(e) => handleInputChange('dateOfIncorporation', e.target.value)}
            required
            disabled={disabled}
            error={errors.dateOfIncorporation}
          />
          
          <TextInput
            label="Place of Incorporation"
            value={data.placeOfIncorporation}
            onChange={(e) => handleInputChange('placeOfIncorporation', e.target.value)}
            placeholder="Enter place of incorporation"
            required
            disabled={disabled}
            error={errors.placeOfIncorporation}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <TextInput
            label="Website"
            value={data.website}
            onChange={(e) => handleInputChange('website', e.target.value)}
            placeholder="https://www.example.com"
            disabled={disabled}
            error={errors.website}
          />
          
          <TextInput
            label="Foreign Ownership Percentage"
            value={data.foreignOwnership}
            onChange={(e) => handleInputChange('foreignOwnership', e.target.value)}
            placeholder="0-100%"
            disabled={disabled}
            error={errors.foreignOwnership}
            helperText="Enter percentage of foreign ownership"
          />
        </div>
      </div>

      {/* Shareholders */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Shareholders
          </h3>
          <button
            type="button"
            onClick={addShareholder}
            disabled={disabled}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
          >
            <i className="ri-add-line mr-1"></i>
            Add Shareholder
          </button>
        </div>
        
        {data.shareholders.map((shareholder, index) => (
          <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">
                Shareholder {index + 1}
              </h4>
              {data.shareholders.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeShareholder(index)}
                  disabled={disabled}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                  <i className="ri-delete-bin-line"></i>
                </button>
              )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <TextInput
                label="Name"
                value={shareholder.name}
                onChange={(e) => updateShareholder(index, 'name', e.target.value)}
                placeholder="Enter shareholder name"
                required
                disabled={disabled}
              />
              
              <CountryDropdown
                label="Nationality"
                value={shareholder.nationality}
                onChange={(value) => updateShareholder(index, 'nationality', value)}
                required
                disabled={disabled}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <TextArea
                label="Address"
                value={shareholder.address}
                onChange={(e) => updateShareholder(index, 'address', e.target.value)}
                placeholder="Enter address"
                rows={2}
                required
                disabled={disabled}
              />
              
              <TextInput
                label="Shareholding Percentage"
                value={shareholder.shareholding}
                onChange={(e) => updateShareholder(index, 'shareholding', e.target.value)}
                placeholder="0-100%"
                required
                disabled={disabled}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Directors */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Directors
          </h3>
          <button
            type="button"
            onClick={addDirector}
            disabled={disabled}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
          >
            <i className="ri-add-line mr-1"></i>
            Add Director
          </button>
        </div>
        
        {data.directors.map((director, index) => (
          <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">
                Director {index + 1}
              </h4>
              {data.directors.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeDirector(index)}
                  disabled={disabled}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                  <i className="ri-delete-bin-line"></i>
                </button>
              )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <TextInput
                label="Name"
                value={director.name}
                onChange={(e) => updateDirector(index, 'name', e.target.value)}
                placeholder="Enter director name"
                required
                disabled={disabled}
              />
              
              <CountryDropdown
                label="Nationality"
                value={director.nationality}
                onChange={(value) => updateDirector(index, 'nationality', value)}
                required
                disabled={disabled}
              />
            </div>
            
            <div className="grid grid-cols-1 gap-4 mt-4">
              <TextArea
                label="Address"
                value={director.address}
                onChange={(e) => updateDirector(index, 'address', e.target.value)}
                placeholder="Enter address"
                rows={2}
                required
                disabled={disabled}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CompanyProfile;
