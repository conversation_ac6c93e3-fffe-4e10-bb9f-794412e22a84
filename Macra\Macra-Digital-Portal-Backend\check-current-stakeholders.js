const mysql = require('mysql2/promise');

async function checkStakeholdersTable() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'macra_db'
    });

    try {
        console.log('🔍 Checking current stakeholders table structure...\n');

        // Check table structure
        const [columns] = await connection.execute(`
            SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'macra_db' AND TABLE_NAME = 'stakeholders'
            ORDER BY ORDINAL_POSITION
        `);

        console.log('📋 Current stakeholders table columns:');
        columns.forEach(col => {
            const length = col.CHARACTER_MAXIMUM_LENGTH ? `(${col.CHARACTER_MAXIMUM_LENGTH})` : '';
            const nullable = col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL';
            const defaultVal = col.COLUMN_DEFAULT ? ` DEFAULT ${col.COLUMN_DEFAULT}` : '';
            console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE}${length} ${nullable}${defaultVal}`);
        });

        // Check foreign key constraints
        const [constraints] = await connection.execute(`
            SELECT
                kcu.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME,
                rc.DELETE_RULE,
                rc.UPDATE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
            WHERE kcu.TABLE_SCHEMA = 'macra_db'
                AND kcu.TABLE_NAME = 'stakeholders'
                AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        `);

        console.log('\n🔗 Current foreign key constraints:');
        if (constraints.length === 0) {
            console.log('  No foreign key constraints found');
        } else {
            constraints.forEach(constraint => {
                console.log(`  ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME} (DELETE: ${constraint.DELETE_RULE})`);
            });
        }

        // Check referenced table column types
        console.log('\n📊 Referenced table column types:');
        
        const [contactsCol] = await connection.execute(`
            SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'macra_db' AND TABLE_NAME = 'contacts' AND COLUMN_NAME = 'contact_id'
        `);
        
        const [usersCol] = await connection.execute(`
            SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'macra_db' AND TABLE_NAME = 'users' AND COLUMN_NAME = 'user_id'
        `);

        if (contactsCol.length > 0) {
            const length = contactsCol[0].CHARACTER_MAXIMUM_LENGTH ? `(${contactsCol[0].CHARACTER_MAXIMUM_LENGTH})` : '';
            console.log(`  contacts.contact_id: ${contactsCol[0].DATA_TYPE}${length}`);
        }

        if (usersCol.length > 0) {
            const length = usersCol[0].CHARACTER_MAXIMUM_LENGTH ? `(${usersCol[0].CHARACTER_MAXIMUM_LENGTH})` : '';
            console.log(`  users.user_id: ${usersCol[0].DATA_TYPE}${length}`);
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await connection.end();
    }
}

checkStakeholdersTable();
