import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR, APP_FILTER, APP_GUARD } from '@nestjs/core';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { RolesModule } from './roles/roles.module';
import { PermissionsModule } from './permissions/permissions.module';
import { AuditTrailModule } from './audit-trail/audit-trail.module';
import { SeederModule } from './database/seeders/seeder.module';
import { LicenseTypesModule } from './license-types/license-types.module';
import { LicenseCategoriesModule } from './license-categories/license-categories.module';
import { IdentificationTypesModule } from './identification-types/identification-types.module';
import { LicenseCategoryDocumentsModule } from './license-category-documents/license-category-documents.module';
import { ApplicationsModule } from './applications/applications.module';
import { ApplicantsModule } from './applicants/applicants.module';
import { DocumentsModule } from './documents/documents.module';
import { ContactsModule } from './contacts/contacts.module';
import { ApplicationStatusTrackingModule } from './application-status-tracking/application-status-tracking.module';
import { ConsumerAffairsModule } from './consumer-affairs/consumer-affairs.module';
import { DataBreachModule } from './data-breach/data-breach.module';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';

import * as Joi from 'joi';
import { join } from "path";
import { PostalCode } from './entities/postal-code.entity';
import { AddressModule } from './address/address.module';
import { OrganizationModule } from './organization/organization.module';
import { DepartmentModule } from './department/department.module';
import { StakeholdersModule } from './stakeholders/stakeholders.module';

// TypeORM supported database types for NestJS v11
type SupportedDatabaseType = 'mysql' | 'mariadb' | 'postgres' | 'cockroachdb' | 'mongodb' | 'aurora-mysql';

function getDatabaseType(dbDriver?: string): SupportedDatabaseType {
  const supported: SupportedDatabaseType[] = ['mysql', 'mariadb', 'postgres', 'cockroachdb', 'mongodb', 'aurora-mysql'];
  if (dbDriver && supported.includes(dbDriver as SupportedDatabaseType)) {
    return dbDriver as SupportedDatabaseType;
  }

  console.warn(
    `Warning: Unsupported or missing DB_DRIVER "${dbDriver}". Defaulting to "mysql". Supported: ${supported.join(', ')}`
  );
  return 'mysql';
}

const isProd = process.env.NODE_ENV === 'production';
const isTest = process.env.NODE_ENV === 'test';
const templatesDir = isProd
  ? join(__dirname, 'templates') // dist/templates for prod
  : join(process.cwd(), 'src', 'templates'); // src/templates for dev

export const assetsDir = isProd
  ? join(__dirname, 'templates', 'assets') // dist/templates/assets
  : join(process.cwd(), 'src', 'templates', 'assets'); // src/templates/assets

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env${isTest ? '.test' : ''}`,
      validationSchema: Joi.object({
        DB_DRIVER: Joi.string().valid(
          'mysql',
          'mariadb',
          'postgres',
          'cockroachdb',
          'mongodb',
          'aurora-mysql',
        ),
        DB_HOST: Joi.string().required(),
        DB_PORT: Joi.number().default(3306),
        DB_USERNAME: Joi.string().required(),
        DB_PASSWORD: Joi.string().allow('').required(),
        DB_NAME: Joi.string().required(),
        EMAIL_USER: Joi.string().default(''),
        EMAIL_PWD: Joi.string().default(''),
        EMAIL_PORT: Joi.string().default(587),
        NODE_ENV: Joi.string()
          .valid('development', 'production', 'test')
          .default('development'),
      }),
    }),
    TypeOrmModule.forFeature([PostalCode]),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService): TypeOrmModuleOptions => {
        const dbType = getDatabaseType(config.get<string>('DB_DRIVER'));

        const baseConfig = {
          type: dbType,
          host: config.get<string>('DB_HOST'),
          port: config.get<number>('DB_PORT'),
          username: config.get<string>('DB_USERNAME'),
          password: config.get<string>('DB_PASSWORD'),
          database: config.get<string>('DB_NAME'),
          entities: [__dirname + '/**/*.entity{.ts,.js}'],
          synchronize: config.get<string>('NODE_ENV') !== 'production',
          logging: config.get<string>('NODE_ENV') === 'development',
          retryAttempts: 3,
          retryDelay: 3000,
          ssl: config.get<string>('DB_SSL', 'false') === 'true'
            ? { rejectUnauthorized: false }
            : false,
        };

        // Add database-specific options
        if (dbType === 'mysql' || dbType === 'mariadb') {
          return {
            ...baseConfig,
            charset: 'utf8mb4',
            timezone: '+00:00'
          } as TypeOrmModuleOptions;
        }

        // Add PostgreSQL specific options to handle UUID generation
        if (dbType === 'postgres') {
          return {
            ...baseConfig,
            // PostgreSQL specific configuration
            extra: {
              // Don't automatically install extensions
              installExtensions: false,
            },
            // Custom initialization to handle UUID functions
            applicationName: 'MACRA Digital Portal',
          } as TypeOrmModuleOptions;
        }

        return baseConfig as TypeOrmModuleOptions;
      },
    }),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => ({
        transport: {
          host: 'smtp.gmail.com',
          port: parseInt(config.get<string>('EMAIL_PORT') || '587', 10),
          secure: false,
          auth: {
            user: config.get<string>('EMAIL_USER'),
            pass: config.get<string>('EMAIL_PWD'),
          },
        },
        defaults: {
          from: `"MACRA Digital Portal" <${config.get<string>('EMAIL_USER')}>`,
        },
        template: {
          dir: templatesDir,
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
    }),
    ThrottlerModule.forRoot([
      {
        /*ttl: 60000, // 60 seconds in milliseconds
        limit: 5, // 5 requests per ttl*/
        ttl: 60000, // 60 seconds in milliseconds
        limit: 100, // Increased from 5 to 100 requests per minute
      },
    ]),
    AuthModule,
    UsersModule,
    RolesModule,
    PermissionsModule,
    AuditTrailModule,
    SeederModule,
    LicenseTypesModule,
    LicenseCategoriesModule,
    IdentificationTypesModule,
    LicenseCategoryDocumentsModule,
    ApplicationsModule,
    ApplicantsModule,
    DocumentsModule,
    ContactsModule,
    ApplicationStatusTrackingModule,
    AddressModule,
    ConsumerAffairsModule,
    DataBreachModule,
    OrganizationModule,
    DepartmentModule,
    StakeholdersModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule { }
