-- Comprehensive fix for database schema inconsistencies
-- Run this script to fix foreign key constraint issues

-- Step 1: Drop existing foreign key constraints that might be problematic
SET FOREIGN_KEY_CHECKS = 0;

-- Drop existing constraints if they exist
ALTER TABLE `audit_failures` DROP FOREIGN KEY IF EXISTS `FK_628f95ddb529026db9d8c641314`;
ALTER TABLE `audit_trail` DROP FOREIGN KEY IF EXISTS `FK_audit_trail_user`;
ALTER TABLE `notifications` DROP FOREIGN KEY IF EXISTS `FK_notifications_user`;

-- Step 2: Fix data inconsistencies

-- Clean up orphaned audit_failures records
UPDATE audit_failures 
SET user_id = NULL 
WHERE user_id IS NOT NULL 
AND user_id NOT IN (SELECT user_id FROM users);

-- Clean up orphaned audit_trail records
UPDATE audit_trail 
SET user_id = NULL 
WHERE user_id IS NOT NULL 
AND user_id NOT IN (SELECT user_id FROM users);

-- Clean up orphaned notifications records  
DELETE FROM notifications 
WHERE user_id IS NOT NULL 
AND user_id NOT IN (SELECT user_id FROM users);

-- Step 3: Fix column types to match the users table

-- Fix audit_failures user_id column type
ALTER TABLE `audit_failures` 
MODIFY COLUMN `user_id` varchar(36) NULL;

-- Fix audit_trail user_id column type  
ALTER TABLE `audit_trail` 
MODIFY COLUMN `user_id` varchar(36) NULL;

-- Fix notifications user_id column type
ALTER TABLE `notifications` 
MODIFY COLUMN `user_id` varchar(36) NOT NULL;

-- Fix user_identifications user_id column type (if needed)
ALTER TABLE `user_identifications` 
MODIFY COLUMN `user_id` varchar(36) NOT NULL;

-- Fix employees user_id column type (if needed)
ALTER TABLE `employees` 
MODIFY COLUMN `user_id` varchar(36) NOT NULL;

-- Step 4: Re-create foreign key constraints with proper references

-- Add foreign key for audit_failures
ALTER TABLE `audit_failures` 
ADD CONSTRAINT `FK_audit_failures_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Add foreign key for audit_trail
ALTER TABLE `audit_trail` 
ADD CONSTRAINT `FK_audit_trail_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Add foreign key for notifications
ALTER TABLE `notifications` 
ADD CONSTRAINT `FK_notifications_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add foreign key for user_identifications
ALTER TABLE `user_identifications` 
ADD CONSTRAINT `FK_user_identifications_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add foreign key for employees
ALTER TABLE `employees` 
ADD CONSTRAINT `FK_employees_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Step 5: Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Step 6: Verify the fixes
SELECT 'Verification Results' as status;

SELECT 
    COUNT(*) as orphaned_audit_failures,
    'audit_failures with invalid user_id' as description
FROM audit_failures af
LEFT JOIN users u ON af.user_id = u.user_id
WHERE af.user_id IS NOT NULL AND u.user_id IS NULL;

SELECT 
    COUNT(*) as orphaned_audit_trail,
    'audit_trail with invalid user_id' as description
FROM audit_trail at
LEFT JOIN users u ON at.user_id = u.user_id
WHERE at.user_id IS NOT NULL AND u.user_id IS NULL;

SELECT 
    COUNT(*) as orphaned_notifications,
    'notifications with invalid user_id' as description
FROM notifications n
LEFT JOIN users u ON n.user_id = u.user_id
WHERE n.user_id IS NOT NULL AND u.user_id IS NULL;

SELECT 'Schema fix completed successfully!' as message;