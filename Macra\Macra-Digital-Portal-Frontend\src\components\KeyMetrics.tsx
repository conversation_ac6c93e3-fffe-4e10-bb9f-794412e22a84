'use client';

import Link from 'next/link';

interface MetricCardProps {
  title: string;
  value: string;
  icon: string;
  iconBgColor?: string;
  subtext: string;
  subtextColor?: string;
  linkHref: string;
  linkText?: string;
}

const MetricCard = ({
  title,
  value,
  icon,
  iconBgColor = 'bg-gray-200',
  subtext,
  subtextColor = 'text-gray-500',
  linkHref,
  linkText = 'View More'
}: MetricCardProps) => {
  return (
    <div className="bg-gray-50 rounded-lg p-4 flex flex-col space-y-4">
      {/* Row 1: Icon and Info */}
      <div className="flex place-content-start items-center">
        {/* Column 1: Icon */}
        <div className={`flex-shrink-0 ${iconBgColor} rounded-md p-3`}>
          <div className="w-6 h-6 flex items-center justify-center text-primary">
            <i className={icon}></i>
          </div>
        </div>
        {/* Column 2: Info */}
        <div className="ml-4 flex flex-col">
          <h4 className="text-sm font-medium text-gray-500">{title}</h4>
          
          {/* Row: Value */}
          <div className="mt-1 flex items-baseline">
            <div className="text-2xl font-semibold text-gray-900">{value}</div>
          </div>
          
          {/* Row: Subtext */}
          <div className="mt-1">
            <span className={`text-xs ${subtextColor}`}>
              {subtext}
            </span>
          </div>
        </div>
      </div>

      {/* Row 2: Link */}
      <div>
        <Link 
          href={linkHref}
          className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']"
        >
          {linkText}
        </Link>
      </div>
    </div>
  );
};

interface KeyMetricsProps {
  className?: string;
}

const KeyMetrics = ({ className = '' }: KeyMetricsProps) => {
  const metrics = [
    {
      title: 'Licenses',
      value: '1,482',
      icon: 'ri-key-line',
      iconBgColor: 'bg-gray-200',
      subtext: '57 expiring soon',
      subtextColor: 'text-yellow-600',
      linkHref: '/dashboard/licenses',
      linkText: 'View More'
    },
    {
      title: 'Users',
      value: '3,649',
      icon: 'ri-user-line',
      iconBgColor: 'bg-gray-200',
      subtext: '247 new this month',
      subtextColor: 'text-green-600',
      linkHref: '/dashboard/users',
      linkText: 'View More'
    },
    {
      title: 'Revenue (MWK)',
      value: '115.4M',
      icon: 'ri-money-dollar-circle-line',
      iconBgColor: 'bg-gray-200',
      subtext: '1M more this month',
      subtextColor: 'text-green-600',
      linkHref: '/dashboard/financial',
      linkText: 'View More'
    },
    {
      title: 'Compliance (%)',
      value: '92.1',
      icon: 'ri-shield-check-line',
      iconBgColor: 'bg-red-100',
      subtext: '8 new issues',
      subtextColor: 'text-red-600',
      linkHref: '/dashboard/spectrum',
      linkText: 'View More'
    }
  ];

  return (
    <div className={`bg-white shadow rounded-lg mb-6 overflow-hidden ${className}`}>
      <div className="p-6">
        <h3 className="text-lg font-medium leading-4 text-gray-900 mb-4">Key Metrics</h3>
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {metrics.map((metric, index) => (
            <MetricCard key={index} {...metric} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default KeyMetrics;
