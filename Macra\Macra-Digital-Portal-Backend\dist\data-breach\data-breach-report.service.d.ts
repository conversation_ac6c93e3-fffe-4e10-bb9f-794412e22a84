import { Repository } from 'typeorm';
import { DataBreachReport, DataBreachReportAttachment, DataBreachReportStatusHistory } from './data-breach-report.entity';
import { CreateDataBreachReportDto, UpdateDataBreachReportDto, DataBreachReportResponseDto, DataBreachReportFilterDto, CreateDataBreachReportAttachmentDto, UpdateDataBreachReportStatusDto } from './data-breach-report.dto';
export declare class DataBreachReportService {
    private reportRepository;
    private attachmentRepository;
    private statusHistoryRepository;
    constructor(reportRepository: Repository<DataBreachReport>, attachmentRepository: Repository<DataBreachReportAttachment>, statusHistoryRepository: Repository<DataBreachReportStatusHistory>);
    create(createDto: CreateDataBreachReportDto, reporterId: string): Promise<DataBreachReportResponseDto>;
    findAll(filterDto: DataBreachReportFilterDto, userId: string, isStaff?: boolean): Promise<{
        data: DataBreachReportResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(reportId: string, userId: string, isStaff?: boolean): Promise<DataBreachReportResponseDto>;
    update(reportId: string, updateDto: UpdateDataBreachReportDto, userId: string, isStaff?: boolean): Promise<DataBreachReportResponseDto>;
    delete(reportId: string, userId: string, isStaff?: boolean): Promise<void>;
    addAttachment(attachmentDto: CreateDataBreachReportAttachmentDto, userId: string): Promise<DataBreachReportAttachment>;
    updateStatus(reportId: string, statusDto: UpdateDataBreachReportStatusDto, userId: string): Promise<DataBreachReportResponseDto>;
    private createStatusHistory;
    private createQueryBuilder;
    private applyFilters;
    private mapToResponseDto;
}
