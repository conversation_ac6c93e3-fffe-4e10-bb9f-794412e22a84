'use client';

import React from 'react';
import { TextInput, TextArea } from '@/components/forms';
import { ManagementData, ManagementTeamMember, ApplicationFormComponentProps } from './index';

interface ManagementProps extends ApplicationFormComponentProps {
  data: ManagementData;
  onChange: (data: ManagementData) => void;
}

const Management: React.FC<ManagementProps> = ({
  data,
  onChange,
  errors = {},
  disabled = false
}) => {
  const handleInputChange = (field: keyof ManagementData, value: any) => {
    onChange({
      ...data,
      [field]: value
    });
  };

  const addManagementMember = () => {
    const newMember: ManagementTeamMember = {
      name: '',
      position: '',
      qualifications: '',
      experience: ''
    };
    handleInputChange('managementTeam', [...data.managementTeam, newMember]);
  };

  const updateManagementMember = (index: number, field: keyof ManagementTeamMember, value: string) => {
    const updatedTeam = data.managementTeam.map((member, i) =>
      i === index ? { ...member, [field]: value } : member
    );
    handleInputChange('managementTeam', updatedTeam);
  };

  const removeManagementMember = (index: number) => {
    const updatedTeam = data.managementTeam.filter((_, i) => i !== index);
    handleInputChange('managementTeam', updatedTeam);
  };

  return (
    <div className="space-y-8">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Management Team
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide information about your management team and organizational structure
        </p>
      </div>

      {/* Management Team Members */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Key Management Personnel
          </h3>
          <button
            type="button"
            onClick={addManagementMember}
            disabled={disabled}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
          >
            <i className="ri-add-line mr-1"></i>
            Add Team Member
          </button>
        </div>
        
        {data.managementTeam.map((member, index) => (
          <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">
                Team Member {index + 1}
              </h4>
              {data.managementTeam.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeManagementMember(index)}
                  disabled={disabled}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                  <i className="ri-delete-bin-line"></i>
                </button>
              )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <TextInput
                label="Full Name"
                value={member.name}
                onChange={(e) => updateManagementMember(index, 'name', e.target.value)}
                placeholder="Enter full name"
                required
                disabled={disabled}
              />
              
              <TextInput
                label="Position/Title"
                value={member.position}
                onChange={(e) => updateManagementMember(index, 'position', e.target.value)}
                placeholder="e.g., Chief Executive Officer, General Manager"
                required
                disabled={disabled}
              />
            </div>
            
            <div className="grid grid-cols-1 gap-4 mt-4">
              <TextArea
                label="Qualifications"
                value={member.qualifications}
                onChange={(e) => updateManagementMember(index, 'qualifications', e.target.value)}
                placeholder="List educational qualifications, certifications, and professional credentials"
                rows={3}
                required
                disabled={disabled}
                helperText="Include degrees, certifications, and relevant professional qualifications"
              />
              
              <TextArea
                label="Professional Experience"
                value={member.experience}
                onChange={(e) => updateManagementMember(index, 'experience', e.target.value)}
                placeholder="Describe relevant work experience, previous roles, and industry expertise"
                rows={4}
                required
                disabled={disabled}
                helperText="Include years of experience, previous companies, and relevant achievements"
              />
            </div>
          </div>
        ))}
        
        {data.managementTeam.length === 0 && (
          <div className="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
            <i className="ri-team-line text-4xl text-gray-400 dark:text-gray-500 mb-2"></i>
            <p className="text-gray-500 dark:text-gray-400 mb-4">No management team members added yet</p>
            <button
              type="button"
              onClick={addManagementMember}
              disabled={disabled}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
            >
              <i className="ri-add-line mr-2"></i>
              Add First Team Member
            </button>
          </div>
        )}
      </div>

      {/* Organizational Structure */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Organizational Structure
        </h3>
        
        <TextArea
          label="Organizational Structure Description"
          value={data.organizationalStructure}
          onChange={(e) => handleInputChange('organizationalStructure', e.target.value)}
          placeholder="Describe your company's organizational structure, reporting lines, and departmental divisions"
          rows={5}
          required
          disabled={disabled}
          error={errors.organizationalStructure}
          helperText="Include information about departments, reporting relationships, and decision-making processes"
        />
      </div>

      {/* Key Personnel Summary */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Key Personnel Summary
        </h3>
        
        <TextArea
          label="Key Personnel Overview"
          value={data.keyPersonnel}
          onChange={(e) => handleInputChange('keyPersonnel', e.target.value)}
          placeholder="Provide a summary of key personnel roles and responsibilities within the organization"
          rows={4}
          required
          disabled={disabled}
          error={errors.keyPersonnel}
          helperText="Highlight the most critical roles and how they contribute to business operations"
        />
      </div>

      {/* Information Notice */}
      <div className="bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4 border border-amber-200 dark:border-amber-800">
        <div className="flex items-start">
          <i className="ri-information-line text-amber-600 dark:text-amber-400 text-lg mr-3 mt-0.5"></i>
          <div>
            <h4 className="text-sm font-medium text-amber-900 dark:text-amber-100 mb-1">
              Management Team Requirements
            </h4>
            <p className="text-amber-700 dark:text-amber-300 text-sm">
              Please ensure that your management team has the necessary qualifications and experience 
              relevant to the license type you are applying for. Include all key decision-makers and 
              personnel who will be responsible for compliance and operations.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Management;
