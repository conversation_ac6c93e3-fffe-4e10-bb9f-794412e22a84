import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();

  // Get auth tokens from cookies
  const authToken = request.cookies.get('auth_token');
  const authUser = request.cookies.get('auth_user');

  // Parse user data if available
  let user = null;

  // Try to get user from customer auth first, then staff auth
  if (authUser) {
    try {
      user = JSON.parse(authUser.value);
    } catch (error) {
      console.error('Failed to parse user data:', error);
    }
  }

  // Always allow auth routes without redirection
  if (url.pathname.startsWith('/customer/auth/') || url.pathname.startsWith('/auth/')) {
    return NextResponse.next();
  }

  // Handle root path redirections
  if (url.pathname === '/') {
    if (user && user.roles && user.roles.includes('customer')) {
      url.pathname = '/customer';
      return NextResponse.redirect(url);
    } else if (authToken) {
      url.pathname = '/dashboard';
      return NextResponse.redirect(url);
    } else {
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }
  }

  // Handle customer routes
  if (url.pathname.startsWith('/customer')) {
    // Allow access to customer landing page without auth
    if (url.pathname === '/customer') {
      return NextResponse.next();
    }

    // For other customer routes, check authentication
    if (!authToken && !user) {
      url.pathname = '/customer/auth/login';
      return NextResponse.redirect(url);
    }

    // If user is authenticated but not a customer, redirect to staff dashboard
    if (user && user.roles && !user.roles.includes('customer')) {
      url.pathname = '/dashboard';
      return NextResponse.redirect(url);
    }


    return NextResponse.next();
  }

  // Handle staff routes
  const protectedStaffRoutes = ['/dashboard', '/users', '/applications', '/financial',
                                '/audit-trail', '/roles', '/permissions', '/telecommunications',
                                '/standards', '/postal', '/procurement', '/clf', '/help', '/profile',
                                '/consumer-affairs', '/data-breach'];

  if (protectedStaffRoutes.some(route => url.pathname.startsWith(route))) {
    // If customer tries to access staff routes, redirect to customer portal
    if (user && user.roles && user.roles.includes('customer')) {
      url.pathname = '/customer';
      return NextResponse.redirect(url);
    }

    // If not authenticated, redirect to login
    if (!authToken) {
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
  ],
};
