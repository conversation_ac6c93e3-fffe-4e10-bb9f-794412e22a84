{"version": 3, "file": "licenses.entity.js", "sourceRoot": "", "sources": ["../../src/entities/licenses.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAQ8B;AAC9B,+BAAoC;AACpC,+CAAqC;AACrC,+DAAqD;AACrD,yDAAgD;AAChD,iEAAsD;AAEtD,IAAY,aAMX;AAND,WAAY,aAAa;IACvB,kCAAiB,CAAA;IACjB,oCAAmB,CAAA;IACnB,wCAAuB,CAAA;IACvB,oCAAmB,CAAA;IACnB,8CAA6B,CAAA;AAC/B,CAAC,EANW,aAAa,6BAAb,aAAa,QAMxB;AAGM,IAAM,QAAQ,GAAd,MAAM,QAAQ;IAOnB,UAAU,CAAS;IAGnB,cAAc,CAAS;IAGvB,cAAc,CAAS;IAGvB,YAAY,CAAS;IAGrB,eAAe,CAAS;IAOxB,MAAM,CAAgB;IAGtB,UAAU,CAAO;IAGjB,WAAW,CAAO;IAGlB,SAAS,CAAS;IAGlB,UAAU,CAAU;IAGpB,UAAU,CAAO;IAGjB,UAAU,CAAS;IAGnB,UAAU,CAAO;IAGjB,UAAU,CAAU;IAGpB,UAAU,CAAQ;IAKlB,WAAW,CAAe;IAI1B,SAAS,CAAa;IAItB,YAAY,CAAe;IAI3B,MAAM,CAAO;IAIb,OAAO,CAAO;IAId,OAAO,CAAQ;IAGf,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,UAAU,GAAG,IAAA,SAAM,GAAE,CAAC;QAC7B,CAAC;IACH,CAAC;CACF,CAAA;AAtFY,4BAAQ;AAOnB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,IAAI;KACb,CAAC;;4CACiB;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;gDACnB;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;gDACF;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;8CACJ;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;iDACD;AAOxB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,aAAa,CAAC,MAAM;KAC9B,CAAC;;wCACoB;AAGtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BACb,IAAI;4CAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;8BACZ,IAAI;6CAAC;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;2CACP;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACrB;AAGpB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;4CAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;4CACN;AAGnB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;4CAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACrB;AAGpB;IADC,IAAA,0BAAgB,GAAE;8BACN,IAAI;4CAAC;AAKlB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kCAAY,CAAC;IAC7B,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;8BAC1B,kCAAY;6CAAC;AAI1B;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,6BAAU,CAAC;IAC3B,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;8BAC1B,6BAAU;2CAAC;AAItB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,mCAAY,CAAC;IAC7B,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;8BAC1B,mCAAY;8CAAC;AAI3B;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,CAAC;IACrB,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;8BAC1B,kBAAI;wCAAC;AAIb;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,CAAC;IACrB,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BAC1B,kBAAI;yCAAC;AAId;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BACzB,kBAAI;yCAAC;AAGf;IADC,IAAA,sBAAY,GAAE;;;;0CAKd;mBArFU,QAAQ;IADpB,IAAA,gBAAM,EAAC,UAAU,CAAC;GACN,QAAQ,CAsFpB"}