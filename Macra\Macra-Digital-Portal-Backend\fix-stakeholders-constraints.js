const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixStakeholdersConstraints() {
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'macra_db'
    });

    try {
        console.log('🔧 Fixing stakeholders table foreign key constraints...');
        
        // Step 1: Check if there are any existing records that would violate constraints
        console.log('\n1. Checking for constraint violations...');
        
        // Check for invalid contact_id references
        const [invalidContacts] = await connection.execute(`
            SELECT stakeholder_id, contact_id 
            FROM stakeholders 
            WHERE contact_id IS NOT NULL 
            AND contact_id NOT IN (SELECT contact_id FROM contacts WHERE contact_id IS NOT NULL)
            LIMIT 10
        `);
        
        console.log(`Found ${invalidContacts.length} stakeholders with invalid contact_id references`);
        if (invalidContacts.length > 0) {
            console.log('Invalid contact references:');
            invalidContacts.forEach(row => {
                console.log(`- Stakeholder ${row.stakeholder_id}: contact_id = ${row.contact_id}`);
            });
        }
        
        // Check for invalid created_by references
        const [invalidCreatedBy] = await connection.execute(`
            SELECT stakeholder_id, created_by 
            FROM stakeholders 
            WHERE created_by IS NOT NULL 
            AND created_by NOT IN (SELECT user_id FROM users WHERE user_id IS NOT NULL)
            LIMIT 10
        `);
        
        console.log(`Found ${invalidCreatedBy.length} stakeholders with invalid created_by references`);
        if (invalidCreatedBy.length > 0) {
            console.log('Invalid created_by references:');
            invalidCreatedBy.forEach(row => {
                console.log(`- Stakeholder ${row.stakeholder_id}: created_by = ${row.created_by}`);
            });
        }
        
        // Check for invalid updated_by references
        const [invalidUpdatedBy] = await connection.execute(`
            SELECT stakeholder_id, updated_by 
            FROM stakeholders 
            WHERE updated_by IS NOT NULL 
            AND updated_by NOT IN (SELECT user_id FROM users WHERE user_id IS NOT NULL)
            LIMIT 10
        `);
        
        console.log(`Found ${invalidUpdatedBy.length} stakeholders with invalid updated_by references`);
        
        // Step 2: Clean up invalid data
        if (invalidContacts.length > 0 || invalidCreatedBy.length > 0 || invalidUpdatedBy.length > 0) {
            console.log('\n2. Cleaning up invalid data...');
            
            if (invalidContacts.length > 0) {
                console.log('Setting invalid contact_id to NULL...');
                const [result1] = await connection.execute(`
                    UPDATE stakeholders 
                    SET contact_id = NULL 
                    WHERE contact_id IS NOT NULL 
                    AND contact_id NOT IN (SELECT contact_id FROM contacts WHERE contact_id IS NOT NULL)
                `);
                console.log(`Updated ${result1.affectedRows} records with invalid contact_id`);
            }
            
            if (invalidCreatedBy.length > 0) {
                console.log('Setting invalid created_by to NULL...');
                const [result2] = await connection.execute(`
                    UPDATE stakeholders 
                    SET created_by = NULL 
                    WHERE created_by IS NOT NULL 
                    AND created_by NOT IN (SELECT user_id FROM users WHERE user_id IS NOT NULL)
                `);
                console.log(`Updated ${result2.affectedRows} records with invalid created_by`);
            }
            
            if (invalidUpdatedBy.length > 0) {
                console.log('Setting invalid updated_by to NULL...');
                const [result3] = await connection.execute(`
                    UPDATE stakeholders 
                    SET updated_by = NULL 
                    WHERE updated_by IS NOT NULL 
                    AND updated_by NOT IN (SELECT user_id FROM users WHERE user_id IS NOT NULL)
                `);
                console.log(`Updated ${result3.affectedRows} records with invalid updated_by`);
            }
        }
        
        // Step 3: Drop existing foreign key constraints first
        console.log('\n3. Dropping existing foreign key constraints...');

        try {
            console.log('Dropping existing updated_by foreign key constraint...');
            await connection.execute(`
                ALTER TABLE stakeholders
                DROP FOREIGN KEY FK_5518ae16ebb22019f47827a3127
            `);
            console.log('✅ Dropped updated_by foreign key constraint');
        } catch (error) {
            if (error.code === 'ER_CANT_DROP_FIELD_OR_KEY') {
                console.log('⚠️ Updated_by foreign key constraint does not exist');
            } else {
                console.log('⚠️ Error dropping updated_by constraint:', error.message);
            }
        }

        // Step 4: Fix column data types to match referenced tables
        console.log('\n4. Fixing column data types...');

        // Make contact_id nullable and fix its length to match contacts table
        console.log('Updating contact_id column to varchar(36) and nullable...');
        await connection.execute(`
            ALTER TABLE stakeholders
            MODIFY COLUMN contact_id varchar(36) NULL
        `);

        // Fix created_by length to match users table
        console.log('Updating created_by column to varchar(36)...');
        await connection.execute(`
            ALTER TABLE stakeholders
            MODIFY COLUMN created_by varchar(36) NULL
        `);

        // Fix updated_by length to match users table (already nullable)
        console.log('Updating updated_by column to varchar(36)...');
        await connection.execute(`
            ALTER TABLE stakeholders
            MODIFY COLUMN updated_by varchar(36) NULL
        `);
        
        // Step 5: Add the foreign key constraints
        console.log('\n5. Adding foreign key constraints...');
        
        try {
            console.log('Adding contact_id foreign key constraint...');
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT FK_75516ad5098e0aada3ffe364bf2 
                FOREIGN KEY (contact_id) REFERENCES contacts(contact_id) 
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('✅ Contact foreign key constraint added successfully');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('⚠️ Contact foreign key constraint already exists');
            } else {
                throw error;
            }
        }
        
        try {
            console.log('Adding created_by foreign key constraint...');
            await connection.execute(`
                ALTER TABLE stakeholders 
                ADD CONSTRAINT FK_5525cda345b76b7633dba45bc3d 
                FOREIGN KEY (created_by) REFERENCES users(user_id) 
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('✅ Created_by foreign key constraint added successfully');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('⚠️ Created_by foreign key constraint already exists');
            } else {
                throw error;
            }
        }
        
        // Add updated_by foreign key constraint back
        try {
            console.log('Adding updated_by foreign key constraint...');
            await connection.execute(`
                ALTER TABLE stakeholders
                ADD CONSTRAINT FK_5518ae16ebb22019f47827a3127
                FOREIGN KEY (updated_by) REFERENCES users(user_id)
                ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            console.log('✅ Updated_by foreign key constraint added successfully');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('⚠️ Updated_by foreign key constraint already exists');
            } else {
                throw error;
            }
        }

        // Step 6: Verify the fix
        console.log('\n6. Verifying the fix...');
        
        const [finalCheck] = await connection.execute(`
            SELECT 
                kcu.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME,
                rc.DELETE_RULE,
                rc.UPDATE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
                ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
            WHERE kcu.TABLE_SCHEMA = ? 
            AND kcu.TABLE_NAME = 'stakeholders'
            AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        `, [process.env.DB_NAME || 'macra_db']);
        
        console.log('Final foreign key constraints:');
        finalCheck.forEach(constraint => {
            console.log(`✅ ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME} (DELETE: ${constraint.DELETE_RULE})`);
        });
        
        console.log('\n🎉 Stakeholders table foreign key constraints fixed successfully!');
        
    } catch (error) {
        console.error('❌ Error fixing stakeholders constraints:', error);
        throw error;
    } finally {
        await connection.end();
    }
}

fixStakeholdersConstraints();
