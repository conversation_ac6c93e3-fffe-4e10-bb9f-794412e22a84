"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAddressDto = void 0;
const class_validator_1 = require("class-validator");
class CreateAddressDto {
    address_type;
    address_origin;
    address_line_1;
    address_line_2;
    address_line_3;
    postal_code;
    country;
    city;
}
exports.CreateAddressDto = CreateAddressDto;
__decorate([
    (0, class_validator_1.IsString)({ message: "Address type invalid!" }),
    (0, class_validator_1.IsIn)(['physical', 'postal'], { message: "Address type must be one of the following: physical, postal" }),
    (0, class_validator_1.IsNotEmpty)({ message: "Address type is required!" }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "address_type", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: "Address origin invalid!" }),
    (0, class_validator_1.IsIn)(['applicant', 'stakeholder', 'contact_person', 'user'], { message: "Address origin must be one of the following: applicant, stakeholder, contact_person, user" }),
    (0, class_validator_1.IsNotEmpty)({ message: "Address origin is required!" }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "address_origin", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: "Address line 1 invalid!" }),
    (0, class_validator_1.IsNotEmpty)({ message: "Address line 1 is required!" }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "address_line_1", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: "Address line 2 invalid!" }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "address_line_2", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: "Address line 3 invalid!" }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "address_line_3", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: "Postal code is required!" }),
    (0, class_validator_1.IsString)({ message: "Postal code invalid!" }),
    (0, class_validator_1.MinLength)(6, { message: "Postal code must be at least 6 characters long!" }),
    (0, class_validator_1.MaxLength)(9, { message: "Postal code must not exceed 9 characters!" }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "postal_code", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: "Country is required!" }),
    (0, class_validator_1.IsString)({ message: "Country invalid!" }),
    (0, class_validator_1.MinLength)(3, { message: "Country must be at least 3 characters long!" }),
    (0, class_validator_1.MaxLength)(50, { message: "Country must not exceed 50 characters!" }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "country", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: "City is required!" }),
    (0, class_validator_1.IsString)({ message: "City invalid!" }),
    (0, class_validator_1.MinLength)(3, { message: "City must be at least 3 characters long!" }),
    (0, class_validator_1.MaxLength)(50, { message: "City must not exceed 50 characters!" }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "city", void 0);
//# sourceMappingURL=create.dto.js.map