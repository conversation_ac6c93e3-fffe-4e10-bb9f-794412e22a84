{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    console.log('ApplicationService.createApplication called with:', data);\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      console.log('ApplicationService.createApplication response:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('ApplicationService.createApplication error:', error);\r\n      console.error('Error response:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return response.data;\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      console.log('Creating application with data:', data);\r\n\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      console.log('Creating application with:', {\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id,\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft'\r\n      });\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft',\r\n        current_step: 1,\r\n        progress_percentage: 0 // Start with 0% progress\r\n      });\r\n\r\n      console.log('Application created:', application);\r\n\r\n      // Save applicant form data separately if needed\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        await applicationFormDataService.saveFormSection(\r\n          application.application_id,\r\n          'applicantInfo',\r\n          data.applicant_data\r\n        );\r\n        console.log('Applicant form data saved');\r\n      } catch (formDataError) {\r\n        console.warn('Could not save form data separately, continuing...', formDataError);\r\n        // Continue even if form data saving fails\r\n      }\r\n\r\n      console.log('Application created successfully');\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n\r\n        // Save section data using form data service\r\n        await applicationFormDataService.saveOrUpdateFormSection(\r\n          applicationId,\r\n          sectionName,\r\n          sectionData\r\n        );\r\n\r\n        // Get all form data to calculate progress\r\n        const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n        completedSections = Object.keys(allFormData).length;\r\n\r\n        console.log(`Form data saved using form data service`);\r\n      } catch (formDataError) {\r\n        console.warn('Form data service not available, using basic progress tracking:', formDataError);\r\n\r\n        // Fallback: estimate progress based on section name\r\n        const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory'];\r\n        const sectionIndex = sectionOrder.indexOf(sectionName);\r\n        completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n      }\r\n\r\n      // Calculate progress based on completed sections (excluding reviewSubmit from total)\r\n      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)\r\n      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Get all applications - the backend should filter by authenticated user\r\n      const response = await apiClient.get('/applications');\r\n\r\n      console.log('Applications API response:', response.data);\r\n\r\n      // Handle different response structures\r\n      let applications = [];\r\n      if (response.data?.data) {\r\n        applications = Array.isArray(response.data.data) ? response.data.data : [];\r\n      } else if (Array.isArray(response.data)) {\r\n        applications = response.data;\r\n      } else if (response.data) {\r\n        // Single application or other structure\r\n        applications = [response.data];\r\n      }\r\n\r\n      console.log('Processed applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get the application and validate its form data\r\n      const application = await this.getApplication(applicationId);\r\n      const formData = application.form_data || {};\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,QAAQ,GAAG,CAAC,qDAAqD;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,QAAQ,GAAG,CAAC,kDAAkD,SAAS,IAAI;YAC3E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ,KAAK,CAAC,mBAAoB,OAAe,UAAU;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;YACV;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;gBACR,cAAc;gBACd,qBAAqB,EAAE,yBAAyB;YAClD;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,gDAAgD;YAChD,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,MAAM,2BAA2B,eAAe,CAC9C,YAAY,cAAc,EAC1B,iBACA,KAAK,cAAc;gBAErB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,sDAAsD;YACnE,0CAA0C;YAC5C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBAEvC,4CAA4C;gBAC5C,MAAM,2BAA2B,uBAAuB,CACtD,eACA,aACA;gBAGF,0CAA0C;gBAC1C,MAAM,cAAc,MAAM,2BAA2B,sBAAsB,CAAC;gBAC5E,oBAAoB,OAAO,IAAI,CAAC,aAAa,MAAM;gBAEnD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,mEAAmE;gBAEhF,oDAAoD;gBACpD,MAAM,eAAe;oBAAC;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAgB;iBAAe;gBACxH,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAC7D;YAEA,qFAAqF;YACrF,MAAM,gBAAgB,GAAG,yDAAyD;YAClF,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,MAAM;YAE3F,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,SAAS,IAAI;YAChE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YAEvD,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,SAAS,IAAI,EAAE,MAAM;gBACvB,eAAe,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,EAAE;YAC5E,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACvC,eAAe,SAAS,IAAI;YAC9B,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,wCAAwC;gBACxC,eAAe;oBAAC,SAAS,IAAI;iBAAC;YAChC;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,iDAAiD;YACjD,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;YAC9C,MAAM,WAAW,YAAY,SAAS,IAAI,CAAC;YAE3C,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/applications/ApplicationViewPage.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Application } from '../../types/license';\nimport { applicationService } from '../../services/applicationService';\n\ninterface ApplicationViewPageProps {\n  applicationId: string;\n  departmentType: string;\n  onBack: () => void;\n}\n\ninterface ApplicationDetails extends Application {\n  applicant_details?: any;\n  company_profile?: any;\n  business_info?: any;\n  service_scope?: any;\n  business_plan?: any;\n  legal_history?: any;\n  documents?: any[];\n  form_data?: Record<string, any>;\n}\n\nexport default function ApplicationViewPage({ \n  applicationId, \n  departmentType, \n  onBack \n}: ApplicationViewPageProps) {\n  const [application, setApplication] = useState<ApplicationDetails | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState('overview');\n\n  useEffect(() => {\n    if (applicationId) {\n      fetchApplicationDetails();\n    }\n  }, [applicationId]);\n\n  const fetchApplicationDetails = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await applicationService.getApplication(applicationId);\n      setApplication(response);\n    } catch (err: any) {\n      console.error('Error fetching application details:', err);\n      setError('Failed to load application details');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getDepartmentName = (type: string) => {\n    const names: Record<string, string> = {\n      'postal': 'Postal Services',\n      'telecommunications': 'Telecommunications',\n      'standards': 'Standards Compliance',\n      'clf': 'CLF (Converged Licensing Framework)'\n    };\n    return names[type] || type;\n  };\n\n  const getStatusBadge = (status: string) => {\n    // Handle undefined or null status\n    if (!status) {\n      return (\n        <span className=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300\">\n          Unknown\n        </span>\n      );\n    }\n\n    const statusClasses: Record<string, string> = {\n      'draft': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',\n      'submitted': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',\n      'under_review': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',\n      'evaluation': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',\n      'approved': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',\n      'rejected': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',\n      'withdrawn': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',\n    };\n\n    const displayText = status.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n\n    return (\n      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}>\n        {displayText}\n      </span>\n    );\n  };\n\n  const tabs = [\n    { id: 'overview', label: 'Overview', icon: 'ri-file-text-line' },\n    { id: 'applicant', label: 'Applicant Details', icon: 'ri-user-line' },\n    { id: 'company', label: 'Company Profile', icon: 'ri-building-line' },\n    { id: 'business', label: 'Business Info', icon: 'ri-briefcase-line' },\n    { id: 'service', label: 'Service Scope', icon: 'ri-service-line' },\n    { id: 'plan', label: 'Business Plan', icon: 'ri-file-chart-line' },\n    { id: 'legal', label: 'Legal History', icon: 'ri-scales-line' },\n    { id: 'documents', label: 'Documents', icon: 'ri-folder-line' },\n    { id: 'comments', label: 'Comments', icon: 'ri-chat-3-line' },\n    { id: 'assignment', label: 'Assignment', icon: 'ri-user-settings-line' }\n  ];\n\n  const renderOverviewTab = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow\">\n          <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Application Information</h4>\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Application Number:</span>\n              <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.application_number}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Status:</span>\n              <span>{getStatusBadge(application?.status)}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Progress:</span>\n              <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.progress_percentage || 0}%</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Current Step:</span>\n              <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.current_step}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Submitted:</span>\n              <span className=\"font-medium text-gray-900 dark:text-gray-100\">\n                {application?.submitted_at ? new Date(application.submitted_at).toLocaleDateString() : 'Not submitted'}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow\">\n          <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">License Information</h4>\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600 dark:text-gray-400\">License Type:</span>\n              <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.license_category?.license_type?.name || 'N/A'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600 dark:text-gray-400\">License Category:</span>\n              <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.license_category?.name || 'N/A'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600 dark:text-gray-400\">Description:</span>\n              <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.license_category?.description || 'N/A'}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow\">\n        <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Applicant Information</h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600 dark:text-gray-400\">Name:</span>\n            <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.applicant?.name || 'N/A'}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600 dark:text-gray-400\">Email:</span>\n            <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.applicant?.email || 'N/A'}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600 dark:text-gray-400\">Phone:</span>\n            <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.applicant?.phone || 'N/A'}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600 dark:text-gray-400\">Business Registration:</span>\n            <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.applicant?.business_registration_number || 'N/A'}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600 dark:text-gray-400\">TPIN:</span>\n            <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.applicant?.tpin || 'N/A'}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600 dark:text-gray-400\">Address:</span>\n            <span className=\"font-medium text-gray-900 dark:text-gray-100\">{application?.applicant?.address || 'N/A'}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSectionData = (sectionName: string, data: any) => {\n    if (!data) {\n      return (\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow\">\n          <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\n            {sectionName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\n          </h4>\n          <p className=\"text-gray-500 dark:text-gray-400\">No data available for this section.</p>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow\">\n        <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          {sectionName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\n        </h4>\n        <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n          <pre className=\"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap overflow-x-auto\">\n            {JSON.stringify(data, null, 2)}\n          </pre>\n        </div>\n      </div>\n    );\n  };\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'overview':\n        return renderOverviewTab();\n      case 'applicant':\n        return renderSectionData('Applicant Details', application?.applicant_details || application?.applicant);\n      case 'company':\n        return renderSectionData('Company Profile', application?.company_profile);\n      case 'business':\n        return renderSectionData('Business Information', application?.business_info);\n      case 'service':\n        return renderSectionData('Service Scope', application?.service_scope);\n      case 'plan':\n        return renderSectionData('Business Plan', application?.business_plan);\n      case 'legal':\n        return renderSectionData('Legal History', application?.legal_history);\n      case 'documents':\n        return (\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow\">\n            <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Documents</h4>\n            {application?.documents && application.documents.length > 0 ? (\n              <div className=\"space-y-3\">\n                {application.documents.map((doc: any, index: number) => (\n                  <div key={index} className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <i className=\"ri-file-line text-2xl text-gray-400 mr-3\"></i>\n                      <div>\n                        <p className=\"font-medium text-gray-900 dark:text-gray-100\">{doc.name || `Document ${index + 1}`}</p>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">{doc.type || 'Unknown type'}</p>\n                      </div>\n                    </div>\n                    <button\n                      type=\"button\"\n                      className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium\"\n                    >\n                      <i className=\"ri-download-line mr-1\"></i>\n                      Download\n                    </button>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <p className=\"text-gray-500 dark:text-gray-400\">No documents uploaded</p>\n            )}\n          </div>\n        );\n      case 'comments':\n        return (\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow\">\n            <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Comments & Reviews</h4>\n            <p className=\"text-gray-500 dark:text-gray-400 mb-4\">Add comments for each section before assigning to an officer.</p>\n            <div className=\"space-y-4\">\n              <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                <h5 className=\"font-medium text-gray-900 dark:text-gray-100 mb-2\">General Comments</h5>\n                <textarea\n                  className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  rows={4}\n                  placeholder=\"Add your comments about this application...\"\n                ></textarea>\n                <button\n                  type=\"button\"\n                  className=\"mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  Save Comment\n                </button>\n              </div>\n            </div>\n          </div>\n        );\n      case 'assignment':\n        return (\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow\">\n            <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">Officer Assignment</h4>\n            <p className=\"text-gray-500 dark:text-gray-400 mb-6\">Assign this application to an officer for review and processing.</p>\n\n            <div className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Select Officer\n                </label>\n                <select className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\">\n                  <option value=\"\">Choose an officer...</option>\n                  <option value=\"officer1\">John Doe - Senior Officer</option>\n                  <option value=\"officer2\">Jane Smith - Review Officer</option>\n                  <option value=\"officer3\">Mike Johnson - Technical Officer</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Assignment Notes\n                </label>\n                <textarea\n                  className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                  rows={4}\n                  placeholder=\"Add any specific instructions or notes for the assigned officer...\"\n                ></textarea>\n              </div>\n\n              <div className=\"flex space-x-4\">\n                <button\n                  type=\"button\"\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  Assign Officer\n                </button>\n                <button\n                  type=\"button\"\n                  className=\"px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700\"\n                >\n                  Save as Draft\n                </button>\n              </div>\n            </div>\n          </div>\n        );\n      default:\n        return renderOverviewTab();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading application details...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <i className=\"ri-error-warning-line text-4xl text-red-500 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">Error Loading Application</h3>\n            <p className=\"text-gray-500 dark:text-gray-400 mb-4\">{error}</p>\n            <div className=\"space-x-4\">\n              <button\n                type=\"button\"\n                onClick={fetchApplicationDetails}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n              >\n                Try Again\n              </button>\n              <button\n                type=\"button\"\n                onClick={onBack}\n                className=\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700\"\n              >\n                Go Back\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!application) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"text-center\">\n            <i className=\"ri-file-line text-4xl text-gray-400 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No Application Data</h3>\n            <p className=\"text-gray-500 dark:text-gray-400 mb-4\">Application details could not be found.</p>\n            <button\n              type=\"button\"\n              onClick={onBack}\n              className=\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700\"\n            >\n              Go Back\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between py-6\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                type=\"button\"\n                onClick={onBack}\n                className=\"flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100\"\n              >\n                <i className=\"ri-arrow-left-line mr-2\"></i>\n                Back to {getDepartmentName(departmentType)}\n              </button>\n              <div className=\"h-6 border-l border-gray-300 dark:border-gray-600\"></div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\n                  Application Details\n                </h1>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n                  {application.application_number} • {getStatusBadge(application?.status)}\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-right\">\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Progress</p>\n                <p className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\n                  {application?.progress_percentage || 0}%\n                </p>\n              </div>\n              <div className=\"w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div\n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${application?.progress_percentage || 0}%` }}\n                ></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <nav className=\"flex space-x-8\" aria-label=\"Tabs\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                type=\"button\"\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300'\n                }`}\n              >\n                <i className={`${tab.icon} mr-2`}></i>\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {renderTabContent()}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAuBe,SAAS,oBAAoB,EAC1C,aAAa,EACb,cAAc,EACd,MAAM,EACmB;;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAC1E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,eAAe;gBACjB;YACF;QACF;wCAAG;QAAC;KAAc;IAElB,MAAM,0BAA0B;QAC9B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACzD,eAAe;QACjB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uCAAuC;YACrD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAgC;YACpC,UAAU;YACV,sBAAsB;YACtB,aAAa;YACb,OAAO;QACT;QACA,OAAO,KAAK,CAAC,KAAK,IAAI;IACxB;IAEA,MAAM,iBAAiB,CAAC;QACtB,kCAAkC;QAClC,IAAI,CAAC,QAAQ;YACX,qBACE,6LAAC;gBAAK,WAAU;0BAA8H;;;;;;QAIlJ;QAEA,MAAM,gBAAwC;YAC5C,SAAS;YACT,aAAa;YACb,gBAAgB;YAChB,cAAc;YACd,YAAY;YACZ,YAAY;YACZ,aAAa;QACf;QAEA,MAAM,cAAc,OAAO,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;QAEhF,qBACE,6LAAC;YAAK,WAAW,CAAC,8DAA8D,EAAE,aAAa,CAAC,OAAO,IAAI,iEAAiE;sBACzK;;;;;;IAGP;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAoB;QAC/D;YAAE,IAAI;YAAa,OAAO;YAAqB,MAAM;QAAe;QACpE;YAAE,IAAI;YAAW,OAAO;YAAmB,MAAM;QAAmB;QACpE;YAAE,IAAI;YAAY,OAAO;YAAiB,MAAM;QAAoB;QACpE;YAAE,IAAI;YAAW,OAAO;YAAiB,MAAM;QAAkB;QACjE;YAAE,IAAI;YAAQ,OAAO;YAAiB,MAAM;QAAqB;QACjE;YAAE,IAAI;YAAS,OAAO;YAAiB,MAAM;QAAiB;QAC9D;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM;QAAiB;QAC9D;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAiB;QAC5D;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM;QAAwB;KACxE;IAED,MAAM,oBAAoB,kBACxB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAC1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;8DAAgD,aAAa;;;;;;;;;;;;sDAE/E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;8DAAM,eAAe,aAAa;;;;;;;;;;;;sDAErC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;;wDAAgD,aAAa,uBAAuB;wDAAE;;;;;;;;;;;;;sDAExG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;8DAAgD,aAAa;;;;;;;;;;;;sDAE/E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;8DACb,aAAa,eAAe,IAAI,KAAK,YAAY,YAAY,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAM/F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAC1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;8DAAgD,aAAa,kBAAkB,cAAc,QAAQ;;;;;;;;;;;;sDAEvH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;8DAAgD,aAAa,kBAAkB,QAAQ;;;;;;;;;;;;sDAEzG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;8DAAgD,aAAa,kBAAkB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMtH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDACnD,6LAAC;4CAAK,WAAU;sDAAgD,aAAa,WAAW,QAAQ;;;;;;;;;;;;8CAElG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDACnD,6LAAC;4CAAK,WAAU;sDAAgD,aAAa,WAAW,SAAS;;;;;;;;;;;;8CAEnG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDACnD,6LAAC;4CAAK,WAAU;sDAAgD,aAAa,WAAW,SAAS;;;;;;;;;;;;8CAEnG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDACnD,6LAAC;4CAAK,WAAU;sDAAgD,aAAa,WAAW,gCAAgC;;;;;;;;;;;;8CAE1H,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDACnD,6LAAC;4CAAK,WAAU;sDAAgD,aAAa,WAAW,QAAQ;;;;;;;;;;;;8CAElG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDACnD,6LAAC;4CAAK,WAAU;sDAAgD,aAAa,WAAW,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO7G,MAAM,oBAAoB,CAAC,aAAqB;QAC9C,IAAI,CAAC,MAAM;YACT,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,YAAY,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;;;;;;kCAE9E,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;QAGtD;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BACX,YAAY,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;;;;;;8BAE9E,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,SAAS,CAAC,MAAM,MAAM;;;;;;;;;;;;;;;;;IAKtC;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,kBAAkB,qBAAqB,aAAa,qBAAqB,aAAa;YAC/F,KAAK;gBACH,OAAO,kBAAkB,mBAAmB,aAAa;YAC3D,KAAK;gBACH,OAAO,kBAAkB,wBAAwB,aAAa;YAChE,KAAK;gBACH,OAAO,kBAAkB,iBAAiB,aAAa;YACzD,KAAK;gBACH,OAAO,kBAAkB,iBAAiB,aAAa;YACzD,KAAK;gBACH,OAAO,kBAAkB,iBAAiB,aAAa;YACzD,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;wBACzE,aAAa,aAAa,YAAY,SAAS,CAAC,MAAM,GAAG,kBACxD,6LAAC;4BAAI,WAAU;sCACZ,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,KAAU,sBACpC,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;;;;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAgD,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,QAAQ,GAAG;;;;;;sEAChG,6LAAC;4DAAE,WAAU;sEAA4C,IAAI,IAAI,IAAI;;;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAE,WAAU;;;;;;gDAA4B;;;;;;;;mCAZnC;;;;;;;;;iDAmBd,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;YAIxD,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;sCACrD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCACC,WAAU;wCACV,MAAM;wCACN,aAAY;;;;;;kDAEd,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;YAOX,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAErD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;;;;;;;;;;;;;8CAI7B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;YAOX;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAE,WAAU;sCAAyC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAC1E,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;sCACrD,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC;gDAAE,WAAU;;;;;;4CAA8B;4CAClC,kBAAkB;;;;;;;kDAE7B,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DAGpE,6LAAC;gDAAE,WAAU;;oDACV,YAAY,kBAAkB;oDAAC;oDAAI,eAAe,aAAa;;;;;;;;;;;;;;;;;;;0CAKtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;0DACxD,6LAAC;gDAAE,WAAU;;oDACV,aAAa,uBAAuB;oDAAE;;;;;;;;;;;;;kDAG3C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,aAAa,uBAAuB,EAAE,CAAC,CAAC;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAiB,cAAW;kCACxC,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;gCAEC,MAAK;gCACL,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,qDACA,0HACJ;;kDAEF,6LAAC;wCAAE,WAAW,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC;;;;;;oCAC/B,IAAI,KAAK;;+BAVL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;0BAkBrB,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;GAjcwB;KAAA", "debugId": null}}, {"offset": {"line": 1658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/applications/%5Blicense-type%5D/view/%5Bapplication-id%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useParams, useRouter } from 'next/navigation';\nimport ApplicationViewPage from '../../../../../components/applications/ApplicationViewPage';\n\nexport default function ViewApplicationPage() {\n  const params = useParams();\n  const router = useRouter();\n  const licenseType = params['license-type'] as string;\n  const applicationId = params['application-id'] as string;\n\n  const handleBack = () => {\n    router.push(`/applications/${licenseType}`);\n  };\n\n  return (\n    <ApplicationViewPage\n      applicationId={applicationId}\n      departmentType={licenseType}\n      onBack={handleBack}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,MAAM,CAAC,eAAe;IAC1C,MAAM,gBAAgB,MAAM,CAAC,iBAAiB;IAE9C,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,aAAa;IAC5C;IAEA,qBACE,6LAAC,4JAAA,CAAA,UAAmB;QAClB,eAAe;QACf,gBAAgB;QAChB,QAAQ;;;;;;AAGd;GAjBwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}