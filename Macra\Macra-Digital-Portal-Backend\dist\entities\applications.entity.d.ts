import { User } from './user.entity';
import { Applicants } from './applicant.entity';
import { LicenseCategories } from './license-categories.entity';
export declare enum ApplicationStatus {
    DRAFT = "draft",
    PENDING = "pending",
    SUBMITTED = "submitted",
    UNDER_REVIEW = "under_review",
    EVALUATION = "evaluation",
    APPROVED = "approved",
    REJECTED = "rejected",
    WITHDRAWN = "withdrawn"
}
export declare class Applications {
    application_id: string;
    application_number: string;
    applicant_id: string;
    license_category_id: string;
    status: string;
    current_step: number;
    progress_percentage: number;
    submitted_at?: Date;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    applicant: Applicants;
    license_category: LicenseCategories;
    creator: User;
    updater?: User;
    generateId(): void;
}
