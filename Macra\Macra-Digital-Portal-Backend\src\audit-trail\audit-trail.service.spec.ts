import { Test, TestingModule } from '@nestjs/testing';
import { AuditTrailService } from './audit-trail.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AuditTrail, AuditAction, AuditModule, AuditStatus } from '../entities/audit-trail.entity';
import { AuditFailure } from '../entities/audit_failure.entity';
import { User } from '../entities/user.entity';
import { AdminAlert } from '../entities/admin_alerts.entity';
import { MailerService } from '@nestjs-modules/mailer';
import { Repository } from 'typeorm';

describe('AuditTrailService', () => {
  let service: AuditTrailService;
  let auditTrailRepo: Repository<AuditTrail>;
  let auditFailureRepo: Repository<AuditFailure>;
  let userRepo: Repository<User>;
  let adminAlertRepo: Repository<AdminAlert>;
  let mailerService: MailerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuditTrailService,
        {
          provide: getRepositoryToken(AuditTrail),
          useValue: {
            create: jest.fn().mockImplementation((dto) => dto),
            save: jest.fn().mockResolvedValue({ audit_id: 'mock-id', ...{} }),
            createQueryBuilder: jest.fn().mockReturnValue({
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
            }),
            findOne: jest.fn().mockResolvedValue({ audit_id: 'mock-id' }),
          },
        },
        {
          provide: getRepositoryToken(AuditFailure),
          useValue: {
            create: jest.fn().mockImplementation((dto) => dto),
            save: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            find: jest.fn().mockResolvedValue([
              { email: '<EMAIL>', first_name: 'Admin' },
            ]),
          },
        },
        {
          provide: getRepositoryToken(AdminAlert),
          useValue: {
            save: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: MailerService,
          useValue: {
            sendMail: jest.fn().mockResolvedValue({}),
          },
        },
      ],
    }).compile();

    service = module.get<AuditTrailService>(AuditTrailService);
    auditTrailRepo = module.get(getRepositoryToken(AuditTrail));
    auditFailureRepo = module.get(getRepositoryToken(AuditFailure));
    userRepo = module.get(getRepositoryToken(User));
    adminAlertRepo = module.get(getRepositoryToken(AdminAlert));
    mailerService = module.get<MailerService>(MailerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create and return an audit trail record', async () => {
      const dto = {
        action: AuditAction.LOGIN,
        module: AuditModule.AUTHENTICATION,
        status: AuditStatus.SUCCESS,
        resourceType: 'Authentication',
      };
      const result = await service.create(dto as any);
      expect(result).toHaveProperty('audit_id');
    });

    it('should throw BadRequestException for missing fields', async () => {
      await expect(
        service.create({} as any),
      ).rejects.toThrow('Action, module, and status are required fields');
    });
  });

  describe('logFailure', () => {
    it('should save failure and trigger notification if threshold exceeded', async () => {
      // Simulate near threshold
      const now = Date.now();
      service['recentFailures'] = Array(9).fill(now);
      service['lastAlertSentAt'] = now - 20 * 60 * 1000; // more than cooldown

      const spy = jest.spyOn(service as any, 'notifyAdmins').mockResolvedValue('Log Failure');

      await service.logFailure({
        action: 'login',
        module: 'authentication',
        resource_type: 'Authentication',
      });

      expect(auditFailureRepo.save).toHaveBeenCalled();
      expect(spy).toHaveBeenCalledWith(10);
    });

    it('should not notify if under threshold', async () => {
      service['recentFailures'] = Array(5).fill(Date.now());
      const spy = jest.spyOn(service as any, 'notifyAdmins').mockResolvedValue('Under threshold');

      await service.logFailure({
        action: 'login',
        module: 'authentication',
        resource_type: 'Authentication',
      });

      expect(spy).not.toHaveBeenCalled();
    });
  });
});
