@echo off
echo Running database fix for foreign key constraints...
echo.
echo Please enter your MySQL connection details:
set /p MYSQL_HOST="MySQL Host (default: localhost): "
if "%MYSQL_HOST%"=="" set MYSQL_HOST=localhost

set /p MYSQL_PORT="MySQL Port (default: 3306): "
if "%MYSQL_PORT%"=="" set MYSQL_PORT=3306

set /p MYSQL_USER="MySQL Username: "
set /p MYSQL_PASSWORD="MySQL Password: "
set /p MYSQL_DATABASE="Database Name (default: macra_db): "
if "%MYSQL_DATABASE%"=="" set MYSQL_DATABASE=macra_db

echo.
echo Connecting to MySQL and running fix...
mysql -h %MYSQL_HOST% -P %MYSQL_PORT% -u %MYSQL_USER% -p%MYSQL_PASSWORD% %MYSQL_DATABASE% < "URGENT-RUN-NOW.sql"

if %errorlevel% equ 0 (
    echo.
    echo ✅ Database fix completed successfully!
    echo You can now restart your TypeORM application.
    echo.
) else (
    echo.
    echo ❌ Error occurred while running the fix.
    echo Please check your connection details and try again.
    echo.
)

pause