-- Fix script for audit_failures foreign key constraint issue
-- This script will identify and fix orphaned records in audit_failures table

-- Step 1: Check for orphaned records in audit_failures table
SELECT 
    COUNT(*) as orphaned_count,
    'audit_failures records with user_id not in users table' as description
FROM audit_failures af
LEFT JOIN users u ON af.user_id = u.user_id
WHERE af.user_id IS NOT NULL AND u.user_id IS NULL;

-- Step 2: Show the orphaned records for review
SELECT 
    af.failure_id,
    af.user_id,
    af.action,
    af.module,
    af.created_at
FROM audit_failures af
LEFT JOIN users u ON af.user_id = u.user_id
WHERE af.user_id IS NOT NULL AND u.user_id IS NULL
ORDER BY af.created_at DESC;

-- Step 3: Option A - Set orphaned user_id to NULL (recommended)
-- This preserves the audit records but removes invalid user references
UPDATE audit_failures 
SET user_id = NULL 
WHERE user_id IS NOT NULL 
AND user_id NOT IN (SELECT user_id FROM users);

-- Alternative Step 3: Option B - Delete orphaned records (use with caution)
-- DELETE FROM audit_failures 
-- WHERE user_id IS NOT NULL 
-- AND user_id NOT IN (SELECT user_id FROM users);

-- Step 4: Verify the cleanup
SELECT 
    COUNT(*) as remaining_orphaned_count,
    'remaining audit_failures records with user_id not in users table' as description
FROM audit_failures af
LEFT JOIN users u ON af.user_id = u.user_id
WHERE af.user_id IS NOT NULL AND u.user_id IS NULL;

-- Step 5: Now the foreign key constraint should be able to be created
-- This will be handled automatically by TypeORM when you restart the application