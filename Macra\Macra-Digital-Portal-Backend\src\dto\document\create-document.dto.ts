import { IsString, <PERSON>Enum, Is<PERSON><PERSON>al, IsUUID, IsInt, IsBoolean, Length } from 'class-validator';
import { DocumentType } from '../../entities/documents.entity';

export class CreateDocumentDto {
  @IsOptional()
  @IsUUID()
  application_id?: string;

  @IsEnum(DocumentType)
  document_type: DocumentType;

  @IsString()
  @Length(1, 255)
  file_name: string;

  @IsString()
  @Length(1, 255)
  entity_type: string;

  @IsUUID()
  entity_id: string;

  @IsString()
  file_path: string;

  @IsInt()
  file_size: number;

  @IsString()
  @Length(1, 100)
  mime_type: string;

  @IsOptional()
  @IsBoolean()
  is_required?: boolean;
}
