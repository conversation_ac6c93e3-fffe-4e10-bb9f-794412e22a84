{"version": 3, "file": "user-identification.entity.js", "sourceRoot": "", "sources": ["../../src/entities/user-identification.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAUiB;AACjB,+CAAqC;AACrC,6EAAkE;AAI3D,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAE7B,sBAAsB,CAAS;IAG/B,OAAO,CAAS;IAGhB,oBAAoB,CAAS;IAG7B,UAAU,CAAO;IAGjB,UAAU,CAAO;IAGjB,UAAU,CAAQ;IAGlB,UAAU,CAAU;IAGpB,UAAU,CAAU;IAKpB,mBAAmB,CAAqB;IAIxC,IAAI,CAAO;IAIX,OAAO,CAAQ;IAIf,OAAO,CAAQ;CAChB,CAAA;AAzCY,gDAAkB;AAE7B;IADC,IAAA,uBAAa,EAAC,MAAM,CAAC;;kEACS;AAG/B;IADC,IAAA,uBAAa,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;;mDAC/B;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;gEACZ;AAG7B;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;sDAAC;AAGjB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;sDAAC;AAGjB;IADC,IAAA,0BAAgB,GAAE;8BACN,IAAI;sDAAC;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDACrB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDACrB;AAKpB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,kBAAkB,CAAC,oBAAoB,CAAC;IACpG,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC;8BAC1B,+CAAkB;+DAAC;AAIxC;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC;IACrD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;8BAC1B,kBAAI;gDAAC;AAIX;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BACzB,kBAAI;mDAAC;AAIf;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BACzB,kBAAI;mDAAC;6BAxCJ,kBAAkB;IAF9B,IAAA,gBAAM,EAAC,sBAAsB,CAAC;IAC9B,IAAA,gBAAM,EAAC,CAAC,wBAAwB,EAAE,sBAAsB,CAAC,CAAC;GAC9C,kBAAkB,CAyC9B"}