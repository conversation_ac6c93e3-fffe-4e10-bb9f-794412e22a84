"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsumerAffairsComplaintService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const consumer_affairs_complaint_entity_1 = require("./consumer-affairs-complaint.entity");
let ConsumerAffairsComplaintService = class ConsumerAffairsComplaintService {
    complaintRepository;
    attachmentRepository;
    statusHistoryRepository;
    constructor(complaintRepository, attachmentRepository, statusHistoryRepository) {
        this.complaintRepository = complaintRepository;
        this.attachmentRepository = attachmentRepository;
        this.statusHistoryRepository = statusHistoryRepository;
    }
    async create(createDto, complainantId) {
        const complaint = this.complaintRepository.create({
            ...createDto,
            complainant_id: complainantId,
            created_by: complainantId,
        });
        const savedComplaint = await this.complaintRepository.save(complaint);
        await this.createStatusHistory(savedComplaint.complaint_id, consumer_affairs_complaint_entity_1.ComplaintStatus.SUBMITTED, 'Complaint submitted', complainantId);
        return this.findOne(savedComplaint.complaint_id, complainantId);
    }
    async findAll(filterDto, userId, isStaff = false) {
        const { page = 1, limit = 10, sort_by = 'created_at', sort_order = 'DESC', ...filters } = filterDto;
        const queryBuilder = this.createQueryBuilder();
        this.applyFilters(queryBuilder, filters);
        if (!isStaff) {
            queryBuilder.andWhere('complaint.complainant_id = :userId', { userId });
        }
        queryBuilder.orderBy(`complaint.${sort_by}`, sort_order);
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);
        const [complaints, total] = await queryBuilder.getManyAndCount();
        const data = complaints.map(complaint => this.mapToResponseDto(complaint));
        return {
            data,
            total,
            page: Number(page),
            limit: Number(limit),
        };
    }
    async findOne(complaintId, userId, isStaff = false) {
        const queryBuilder = this.createQueryBuilder()
            .where('complaint.complaint_id = :complaintId', { complaintId });
        if (!isStaff) {
            queryBuilder.andWhere('complaint.complainant_id = :userId', { userId });
        }
        const complaint = await queryBuilder.getOne();
        if (!complaint) {
            throw new common_1.NotFoundException('Complaint not found');
        }
        return this.mapToResponseDto(complaint);
    }
    async update(complaintId, updateDto, userId, isStaff = false) {
        const complaint = await this.complaintRepository.findOne({
            where: { complaint_id: complaintId },
            relations: ['complainant'],
        });
        if (!complaint) {
            throw new common_1.NotFoundException('Complaint not found');
        }
        if (!isStaff && complaint.complainant_id !== userId) {
            throw new common_1.ForbiddenException('You can only update your own complaints');
        }
        if (!isStaff) {
            const allowedFields = ['title', 'description', 'category'];
            const updateFields = Object.keys(updateDto);
            const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
            if (invalidFields.length > 0) {
                throw new common_1.BadRequestException(`Customers cannot update these fields: ${invalidFields.join(', ')}`);
            }
        }
        if (updateDto.status && updateDto.status !== complaint.status) {
            await this.createStatusHistory(complaintId, updateDto.status, `Status changed from ${complaint.status} to ${updateDto.status}`, userId);
            if (updateDto.status === consumer_affairs_complaint_entity_1.ComplaintStatus.RESOLVED) {
                updateDto.resolved_at = new Date();
            }
        }
        Object.assign(complaint, updateDto);
        complaint.updated_by = userId;
        await this.complaintRepository.save(complaint);
        return this.findOne(complaintId, userId, isStaff);
    }
    async delete(complaintId, userId, isStaff = false) {
        const complaint = await this.complaintRepository.findOne({
            where: { complaint_id: complaintId },
        });
        if (!complaint) {
            throw new common_1.NotFoundException('Complaint not found');
        }
        if (!isStaff && complaint.complainant_id !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own complaints');
        }
        await this.complaintRepository.softDelete(complaintId);
    }
    async addAttachment(attachmentDto, userId) {
        const complaint = await this.complaintRepository.findOne({
            where: { complaint_id: attachmentDto.complaint_id },
        });
        if (!complaint) {
            throw new common_1.NotFoundException('Complaint not found');
        }
        const attachment = this.attachmentRepository.create({
            ...attachmentDto,
            uploaded_by: userId,
        });
        return this.attachmentRepository.save(attachment);
    }
    async updateStatus(complaintId, statusDto, userId) {
        const complaint = await this.complaintRepository.findOne({
            where: { complaint_id: complaintId },
        });
        if (!complaint) {
            throw new common_1.NotFoundException('Complaint not found');
        }
        await this.createStatusHistory(complaintId, statusDto.status, statusDto.comment, userId);
        complaint.status = statusDto.status;
        complaint.updated_by = userId;
        if (statusDto.status === consumer_affairs_complaint_entity_1.ComplaintStatus.RESOLVED) {
            complaint.resolved_at = new Date();
        }
        await this.complaintRepository.save(complaint);
        return this.findOne(complaintId, userId, true);
    }
    async createStatusHistory(complaintId, status, comment, userId) {
        const statusHistory = this.statusHistoryRepository.create({
            complaint_id: complaintId,
            status,
            comment,
            created_by: userId,
        });
        await this.statusHistoryRepository.save(statusHistory);
    }
    createQueryBuilder() {
        return this.complaintRepository
            .createQueryBuilder('complaint')
            .leftJoinAndSelect('complaint.complainant', 'complainant')
            .leftJoinAndSelect('complaint.assignee', 'assignee')
            .leftJoinAndSelect('complaint.attachments', 'attachments')
            .leftJoinAndSelect('complaint.status_history', 'status_history')
            .leftJoinAndSelect('status_history.creator', 'history_creator');
    }
    applyFilters(queryBuilder, filters) {
        if (filters.category) {
            queryBuilder.andWhere('complaint.category = :category', { category: filters.category });
        }
        if (filters.status) {
            queryBuilder.andWhere('complaint.status = :status', { status: filters.status });
        }
        if (filters.priority) {
            queryBuilder.andWhere('complaint.priority = :priority', { priority: filters.priority });
        }
        if (filters.assigned_to) {
            queryBuilder.andWhere('complaint.assigned_to = :assigned_to', { assigned_to: filters.assigned_to });
        }
        if (filters.from_date) {
            queryBuilder.andWhere('complaint.created_at >= :from_date', { from_date: filters.from_date });
        }
        if (filters.to_date) {
            queryBuilder.andWhere('complaint.created_at <= :to_date', { to_date: filters.to_date });
        }
        if (filters.search) {
            queryBuilder.andWhere('(complaint.title ILIKE :search OR complaint.description ILIKE :search)', { search: `%${filters.search}%` });
        }
    }
    mapToResponseDto(complaint) {
        return {
            complaint_id: complaint.complaint_id,
            complaint_number: complaint.complaint_number,
            complainant_id: complaint.complainant_id,
            title: complaint.title,
            description: complaint.description,
            category: complaint.category,
            status: complaint.status,
            priority: complaint.priority,
            assigned_to: complaint.assigned_to,
            resolution: complaint.resolution,
            resolved_at: complaint.resolved_at,
            created_at: complaint.created_at,
            updated_at: complaint.updated_at,
            complainant: complaint.complainant ? {
                user_id: complaint.complainant.user_id,
                first_name: complaint.complainant.first_name,
                last_name: complaint.complainant.last_name,
                email: complaint.complainant.email,
            } : undefined,
            assignee: complaint.assignee ? {
                user_id: complaint.assignee.user_id,
                first_name: complaint.assignee.first_name,
                last_name: complaint.assignee.last_name,
                email: complaint.assignee.email,
            } : undefined,
            attachments: complaint.attachments?.map(attachment => ({
                attachment_id: attachment.attachment_id,
                file_name: attachment.file_name,
                file_type: attachment.file_type,
                file_size: attachment.file_size,
                uploaded_at: attachment.uploaded_at,
            })),
            status_history: complaint.status_history?.map(history => ({
                history_id: history.history_id,
                status: history.status,
                comment: history.comment,
                created_at: history.created_at,
                creator: {
                    user_id: history.creator.user_id,
                    first_name: history.creator.first_name,
                    last_name: history.creator.last_name,
                },
            })),
        };
    }
};
exports.ConsumerAffairsComplaintService = ConsumerAffairsComplaintService;
exports.ConsumerAffairsComplaintService = ConsumerAffairsComplaintService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(consumer_affairs_complaint_entity_1.ConsumerAffairsComplaint)),
    __param(1, (0, typeorm_1.InjectRepository)(consumer_affairs_complaint_entity_1.ConsumerAffairsComplaintAttachment)),
    __param(2, (0, typeorm_1.InjectRepository)(consumer_affairs_complaint_entity_1.ConsumerAffairsComplaintStatusHistory)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ConsumerAffairsComplaintService);
//# sourceMappingURL=consumer-affairs-complaint.service.js.map