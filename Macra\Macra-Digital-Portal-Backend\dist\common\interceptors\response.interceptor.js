"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ResponseInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
let ResponseInterceptor = ResponseInterceptor_1 = class ResponseInterceptor {
    logger = new common_1.Logger(ResponseInterceptor_1.name);
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        return next.handle().pipe((0, operators_1.map)((data) => {
            if (this.isStandardResponse(data))
                return data;
            const isFileDownload = response.getHeader('content-type')?.includes('application/octet-stream') ||
                response.getHeader('content-disposition')?.includes('attachment');
            if (isFileDownload)
                return data;
            if (this.isPaginated(data)) {
                return this.wrapResponse(request, response, {
                    data: data.data,
                    meta: data.meta,
                    message: 'Data retrieved successfully',
                });
            }
            if (Array.isArray(data)) {
                return this.wrapResponse(request, response, {
                    data,
                    meta: { total: data.length },
                    message: 'Data retrieved successfully',
                });
            }
            if (typeof data === 'object' && data !== null) {
                const message = typeof data.message === 'string' && !('success' in data)
                    ? data.message
                    : this.getSuccessMessage(request.method, request.url);
                return this.wrapResponse(request, response, { data, message });
            }
            return this.wrapResponse(request, response, {
                data,
                message: this.getSuccessMessage(request.method, request.url),
            });
        }));
    }
    wrapResponse(request, response, { data, message, meta = undefined, }) {
        return {
            success: true,
            message,
            data,
            meta,
            timestamp: new Date().toISOString(),
            path: request.url,
            statusCode: response.statusCode,
        };
    }
    isStandardResponse(data) {
        return typeof data === 'object' && data?.success !== undefined && data?.timestamp !== undefined;
    }
    isPaginated(data) {
        return typeof data === 'object' && 'data' in data && 'meta' in data;
    }
    getSuccessMessage(method, url) {
        const resource = this.extractResource(url);
        switch (method.toUpperCase()) {
            case 'GET': return `${resource} retrieved successfully`;
            case 'POST': return `${resource} created successfully`;
            case 'PUT':
            case 'PATCH': return `${resource} updated successfully`;
            case 'DELETE': return `${resource} deleted successfully`;
            default: return 'Operation completed successfully';
        }
    }
    extractResource(url) {
        const segments = url.split('/').filter(Boolean);
        const lastSegment = segments[segments.length - 1] || 'resource';
        return this.capitalizeWords(lastSegment.replace(/[-_]/g, ' '));
    }
    capitalizeWords(input) {
        return input
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }
};
exports.ResponseInterceptor = ResponseInterceptor;
exports.ResponseInterceptor = ResponseInterceptor = ResponseInterceptor_1 = __decorate([
    (0, common_1.Injectable)()
], ResponseInterceptor);
//# sourceMappingURL=response.interceptor.js.map