/**
 * Application Configuration System
 * Defines which form components are required for each license type
 */

export interface ApplicationStep {
  id: string;
  name: string;
  component: string;
  required: boolean;
  description?: string;
}

export interface LicenseTypeConfig {
  licenseTypeId: string;
  name: string;
  steps: ApplicationStep[];
  estimatedTime: string;
  requirements: string[];
}

// Standard application steps available
export const AVAILABLE_STEPS: Record<string, ApplicationStep> = {
  APPLICANT_INFO: {
    id: 'applicant-info',
    name: 'Applicant Information',
    component: 'ApplicantInfo',
    required: true,
    description: 'Personal and contact information'
  },
  COMPANY_PROFILE: {
    id: 'company-profile',
    name: 'Company Profile',
    component: 'CompanyProfile',
    required: true,
    description: 'Company details, shareholders, and directors'
  },
  MANAGEMENT: {
    id: 'management',
    name: 'Management Team',
    component: 'Management',
    required: true,
    description: 'Management team and organizational structure'
  },
  PROFESSIONAL_SERVICES: {
    id: 'professional-services',
    name: 'Professional Services',
    component: 'ProfessionalServices',
    required: false,
    description: 'External consultants and service providers'
  },
  BUSINESS_INFO: {
    id: 'business-info',
    name: 'Business Information',
    component: 'BusinessInfo',
    required: true,
    description: 'Business operations, facilities, and model'
  },
  SERVICE_SCOPE: {
    id: 'service-scope',
    name: 'Service Scope',
    component: 'ServiceScope',
    required: true,
    description: 'Service definitions and coverage areas'
  },
  BUSINESS_PLAN: {
    id: 'business-plan',
    name: 'Business Plan',
    component: 'BusinessPlan',
    required: true,
    description: 'Financial projections and strategic planning'
  },
  LEGAL_HISTORY: {
    id: 'legal-history',
    name: 'Legal History',
    component: 'LegalHistory',
    required: true,
    description: 'Legal compliance and regulatory history'
  },
  REVIEW_SUBMIT: {
    id: 'review-submit',
    name: 'Review & Submit',
    component: 'ReviewSubmit',
    required: true,
    description: 'Review and submit application'
  }
};

// License type configurations
export const LICENSE_TYPE_CONFIGS: Record<string, LicenseTypeConfig> = {
  // Postal and Courier Services - Full application process
  'postal-courier': {
    licenseTypeId: 'postal-courier',
    name: 'Postal & Courier Services',
    estimatedTime: '45-60 minutes',
    requirements: [
      'Valid business registration certificate',
      'Tax clearance certificate',
      'Proof of financial capacity',
      'Insurance coverage documentation',
      'Business plan and operational procedures'
    ],
    steps: [
      AVAILABLE_STEPS.APPLICANT_INFO,
      AVAILABLE_STEPS.COMPANY_PROFILE,
      AVAILABLE_STEPS.MANAGEMENT,
      AVAILABLE_STEPS.PROFESSIONAL_SERVICES,
      AVAILABLE_STEPS.BUSINESS_INFO,
      AVAILABLE_STEPS.SERVICE_SCOPE,
      AVAILABLE_STEPS.BUSINESS_PLAN,
      AVAILABLE_STEPS.LEGAL_HISTORY,
      AVAILABLE_STEPS.REVIEW_SUBMIT
    ]
  },

  // Telecommunications - Full application process
  'telecommunications': {
    licenseTypeId: 'telecommunications',
    name: 'Telecommunications',
    estimatedTime: '45-60 minutes',
    requirements: [
      'Valid business registration certificate',
      'Tax clearance certificate',
      'Technical capacity documentation',
      'Financial capacity proof',
      'Network deployment plan'
    ],
    steps: [
      AVAILABLE_STEPS.APPLICANT_INFO,
      AVAILABLE_STEPS.COMPANY_PROFILE,
      AVAILABLE_STEPS.MANAGEMENT,
      AVAILABLE_STEPS.PROFESSIONAL_SERVICES,
      AVAILABLE_STEPS.BUSINESS_INFO,
      AVAILABLE_STEPS.SERVICE_SCOPE,
      AVAILABLE_STEPS.BUSINESS_PLAN,
      AVAILABLE_STEPS.LEGAL_HISTORY,
      AVAILABLE_STEPS.REVIEW_SUBMIT
    ]
  },

  // Standards - Simplified process
  'standards': {
    licenseTypeId: 'standards',
    name: 'Standards',
    estimatedTime: '30-45 minutes',
    requirements: [
      'Valid business registration certificate',
      'Tax clearance certificate',
      'Technical specifications',
      'Quality assurance documentation'
    ],
    steps: [
      AVAILABLE_STEPS.APPLICANT_INFO,
      AVAILABLE_STEPS.COMPANY_PROFILE,
      AVAILABLE_STEPS.BUSINESS_INFO,
      AVAILABLE_STEPS.SERVICE_SCOPE,
      AVAILABLE_STEPS.LEGAL_HISTORY,
      AVAILABLE_STEPS.REVIEW_SUBMIT
    ]
  },

  // CLF (Converged Licensing Framework) - Full process
  'clf': {
    licenseTypeId: 'clf',
    name: 'Converged Licensing Framework',
    estimatedTime: '60-75 minutes',
    requirements: [
      'Valid business registration certificate',
      'Tax clearance certificate',
      'Technical capacity documentation',
      'Financial capacity proof',
      'Convergence implementation plan',
      'Compliance framework documentation'
    ],
    steps: [
      AVAILABLE_STEPS.APPLICANT_INFO,
      AVAILABLE_STEPS.COMPANY_PROFILE,
      AVAILABLE_STEPS.MANAGEMENT,
      AVAILABLE_STEPS.PROFESSIONAL_SERVICES,
      AVAILABLE_STEPS.BUSINESS_INFO,
      AVAILABLE_STEPS.SERVICE_SCOPE,
      AVAILABLE_STEPS.BUSINESS_PLAN,
      AVAILABLE_STEPS.LEGAL_HISTORY,
      AVAILABLE_STEPS.REVIEW_SUBMIT
    ]
  }
};

// Helper functions
export const getLicenseTypeConfig = (licenseTypeId: string): LicenseTypeConfig | null => {
  return LICENSE_TYPE_CONFIGS[licenseTypeId] || null;
};

export const getConfigByLicenseTypeName = (name: string): LicenseTypeConfig | null => {
  const nameLower = name.toLowerCase();
  
  if (nameLower.includes('postal') || nameLower.includes('courier')) {
    return LICENSE_TYPE_CONFIGS['postal-courier'];
  } else if (nameLower.includes('telecom') || nameLower.includes('spectrum') || nameLower.includes('radio')) {
    return LICENSE_TYPE_CONFIGS['telecommunications'];
  } else if (nameLower.includes('standard') || nameLower.includes('approval') || nameLower.includes('certificate')) {
    return LICENSE_TYPE_CONFIGS['standards'];
  } else if (nameLower.includes('clf') || nameLower.includes('converged') || nameLower.includes('framework')) {
    return LICENSE_TYPE_CONFIGS['clf'];
  }
  
  // Default to full process for unknown types
  return LICENSE_TYPE_CONFIGS['postal-courier'];
};

export const getStepByIndex = (config: LicenseTypeConfig, stepIndex: number): ApplicationStep | null => {
  if (stepIndex < 0 || stepIndex >= config.steps.length) {
    return null;
  }
  return config.steps[stepIndex];
};

export const getStepIndex = (config: LicenseTypeConfig, stepId: string): number => {
  return config.steps.findIndex(step => step.id === stepId);
};

export const getTotalSteps = (config: LicenseTypeConfig): number => {
  return config.steps.length;
};

export const getRequiredSteps = (config: LicenseTypeConfig): ApplicationStep[] => {
  return config.steps.filter(step => step.required);
};

export const getOptionalSteps = (config: LicenseTypeConfig): ApplicationStep[] => {
  return config.steps.filter(step => !step.required);
};
