const mysql = require('mysql2/promise');

async function verifyFinalState() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: 'root',
        database: 'macra_db'
    });

    try {
        console.log('🔍 Verifying final state of stakeholders table...\n');

        // Check foreign key constraints
        const [constraints] = await connection.execute(`
            SELECT 
                kcu.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME,
                rc.DELETE_RULE,
                rc.UPDATE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
                ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
            WHERE kcu.TABLE_SCHEMA = 'macra_db' 
                AND kcu.TABLE_NAME = 'stakeholders'
                AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        `);

        console.log('🔗 Foreign key constraints:');
        if (constraints.length === 0) {
            console.log('  ❌ No foreign key constraints found');
        } else {
            constraints.forEach(constraint => {
                console.log(`  ✅ ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME} (DELETE: ${constraint.DELETE_RULE})`);
            });
        }

        console.log('\n🎉 Verification complete!');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await connection.end();
    }
}

verifyFinalState();
