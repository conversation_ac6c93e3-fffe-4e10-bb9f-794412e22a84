import { User } from './user.entity';
import { Applicants } from './applicant.entity';
import { Contacts } from './contacts.entity';
export declare enum StakeholderPosition {
    CEO = "CEO",
    SHAREHOLDER = "Shareholder",
    AUDITOR = "Auditor",
    LAWYER = "Lawyer"
}
export declare class Stakeholder {
    stakeholder_id: string;
    applicant_id: string;
    first_name: string;
    last_name: string;
    middle_name?: string;
    contact_id: string;
    nationality: string;
    position: StakeholderPosition;
    profile: string;
    cv_document_id: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    applicant: Applicants;
    contact: Contacts;
    creator: User;
    updater?: User;
    generateId(): void;
}
