'use client';

import React, { useState } from 'react';
import TextInput from '@/components/forms/TextInput';
import TextArea from '@/components/forms/TextArea';
import Select from '@/components/forms/Select';
import { dataBreachService, CreateDataBreachReportData } from '@/services/dataBreachService';

interface DataBreachModalProps {
  onClose: () => void;
  onSubmit: (data: any) => void;
}

const breachCategories = [
  'Unauthorized Data Access',
  'Data Misuse or Sharing',
  'Privacy Violations',
  'Identity Theft',
  'Phishing Attempts',
  'Data Loss or Theft',
  'Consent Violations',
  'Other'
];

const severityLevels = [
  { value: 'low', label: 'Low - Minor privacy concern' },
  { value: 'medium', label: 'Medium - Moderate data exposure' },
  { value: 'high', label: 'High - Significant data breach' },
  { value: 'critical', label: 'Critical - Severe security incident' }
];

const DataBreachModal: React.FC<DataBreachModalProps> = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    severity: '',
    incidentDate: '',
    affectedData: '',
    organization: '',
    contactAttempts: ''
  });
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setAttachments(prev => [...prev, ...newFiles]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = () => {
    const errors: {[key: string]: string} = {};

    if (!formData.title.trim()) {
      errors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    } else if (formData.description.trim().length < 20) {
      errors.description = 'Description must be at least 20 characters';
    }

    if (!formData.category) {
      errors.category = 'Category is required';
    }

    if (!formData.severity) {
      errors.severity = 'Severity level is required';
    }

    if (!formData.incidentDate) {
      errors.incidentDate = 'Incident date is required';
    }

    if (!formData.organization.trim()) {
      errors.organization = 'Organization involved is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const reportData: CreateDataBreachReportData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        severity: formData.severity,
        incident_date: formData.incidentDate,
        organization_involved: formData.organization,
        affected_data_types: formData.affectedData,
        contact_attempts: formData.contactAttempts,
        attachments: attachments
      };

      const response = await dataBreachService.createReport(reportData);

      if (response.success) {
        onSubmit(response.data);

        // Reset form
        setFormData({
          title: '',
          description: '',
          category: '',
          severity: '',
          incidentDate: '',
          affectedData: '',
          organization: '',
          contactAttempts: ''
        });
        setAttachments([]);
      } else {
        throw new Error(response.message || 'Failed to submit data breach report');
      }

    } catch (error) {
      console.error('Error submitting data breach report:', error);
      // You might want to show an error message to the user here
      alert('Failed to submit data breach report. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Report Data Breach
          </h3>
          <button
            onClick={onClose}
            aria-label="Close modal"
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex">
                <i className="ri-shield-keyhole-line text-red-600 text-lg mr-3 mt-0.5"></i>
                <div>
                  <h4 className="text-sm font-medium text-red-800 dark:text-red-300 mb-1">
                    Data Breach Reporting
                  </h4>
                  <p className="text-sm text-red-700 dark:text-red-400">
                    Report unauthorized access, misuse, or breach of your personal data. This information will be treated confidentially and investigated promptly.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Title */}
            <TextInput
              label="Incident Title *"
              id="breach-title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Brief summary of the data breach incident"
              error={formErrors.title}
              required
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Category */}
              <Select
                label="Breach Category *"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                error={formErrors.category}
                required
              >
                <option value="">Select a category</option>
                {breachCategories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </Select>

              {/* Severity */}
              <Select
                label="Severity Level *"
                name="severity"
                value={formData.severity}
                onChange={handleInputChange}
                error={formErrors.severity}
                required
              >
                <option value="">Select severity level</option>
                {severityLevels.map(level => (
                  <option key={level.value} value={level.value}>{level.label}</option>
                ))}
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Incident Date */}
              <TextInput
                label="Incident Date *"
                id="incident-date"
                name="incidentDate"
                type="date"
                value={formData.incidentDate}
                onChange={handleInputChange}
                error={formErrors.incidentDate}
                required
              />

              {/* Organization */}
              <TextInput
                label="Organization Involved *"
                id="organization"
                name="organization"
                value={formData.organization}
                onChange={handleInputChange}
                placeholder="Name of the organization responsible"
                error={formErrors.organization}
                required
              />
            </div>

            {/* Affected Data */}
            <TextArea
              label="Affected Data Types"
              id="affected-data"
              name="affectedData"
              value={formData.affectedData}
              onChange={handleInputChange}
              rows={3}
              placeholder="Describe what type of personal data was affected (e.g., names, phone numbers, addresses, financial information)"
              error={formErrors.affectedData}
            />

            {/* Description */}
            <TextArea
              label="Detailed Description *"
              id="breach-description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={6}
              placeholder="Please provide a detailed description of the incident, including how you discovered it, what happened, and any impact on you..."
              error={formErrors.description}
              helperText="Minimum 20 characters required"
              required
            />

            {/* Contact Attempts */}
            <TextArea
              label="Previous Contact Attempts"
              id="contact-attempts"
              name="contactAttempts"
              value={formData.contactAttempts}
              onChange={handleInputChange}
              rows={3}
              placeholder="Describe any attempts you made to contact the organization about this incident"
              error={formErrors.contactAttempts}
            />

            {/* File Attachments */}
            <div>
              <label htmlFor="breach-attachments" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Supporting Evidence (Optional)
              </label>
              <input
                id="breach-attachments"
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={handleFileChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Screenshots, emails, documents, or other evidence (Max 5MB per file)
              </p>

              {/* Show selected files */}
              {attachments.length > 0 && (
                <div className="mt-3 space-y-2">
                  {attachments.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded">
                      <div className="flex items-center">
                        <i className="ri-file-line text-gray-400 mr-2"></i>
                        <span className="text-sm text-gray-700 dark:text-gray-300">{file.name}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                          ({formatFileSize(file.size)})
                        </span>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeAttachment(index)}
                        className="text-red-500 hover:text-red-700"
                        aria-label={`Remove ${file.name}`}
                      >
                        <i className="ri-close-line"></i>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300"
              >
                {isSubmitting ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Submitting...
                  </>
                ) : (
                  <>
                    <i className="ri-shield-keyhole-line mr-2"></i>
                    Submit Report
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default DataBreachModal;
