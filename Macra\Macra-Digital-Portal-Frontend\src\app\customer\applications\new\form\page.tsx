'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useLicenseData } from '@/hooks/useLicenseData';
import { LicenseType } from '@/services/licenseTypeService';
import { LicenseCategory } from '@/services/licenseCategoryService';
import ApplicationForm from '@/components/customer/application/ApplicationFormTest';
import ClientOnly from '@/components/common/ClientOnly';
import { logFormError } from '@/utils/formSafety';
import { applicationService } from '@/services/applicationService';

const ApplicationFormPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const { licenseTypes, loading: licenseLoading, getCategoriesByType } = useLicenseData();

  const [selectedLicenseType, setSelectedLicenseType] = useState<LicenseType | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<LicenseCategory | null>(null);
  const [mounted, setMounted] = useState(false);

  // Get URL parameters
  const typeId = searchParams.get('type');
  const categoryId = searchParams.get('category');
  const continueApplicationId = searchParams.get('continue');
  const isContinuing = !!continueApplicationId;

  // Set mounted state
  useEffect(() => {
    setMounted(true);
  }, []);

  // Memoize the data loading logic to prevent infinite loops
  const loadApplicationData = useCallback(() => {
    if (!mounted || authLoading || licenseLoading) return;

    if (!isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }

    if (licenseTypes && typeId) {
      const licenseType = licenseTypes.find(lt => lt.license_type_id === typeId);
      if (licenseType) {
        setSelectedLicenseType(licenseType);
      } else {
        console.warn('License type not found for ID:', typeId);
      }
    }

    // Handle category loading - either with typeId or by searching all categories
    if (categoryId) {
      try {
        let category = null;

        if (typeId) {
          // If we have typeId, use the normal flow
          const categories = getCategoriesByType(typeId);
          category = categories.find(cat => cat.license_category_id === categoryId);
        } else {
          // If we don't have typeId (e.g., from continue), search all categories
          console.log('No typeId provided, searching all categories for:', categoryId);

          // Search through all license types to find the category
          for (const licenseType of licenseTypes || []) {
            const categories = getCategoriesByType(licenseType.license_type_id);
            const foundCategory = categories.find(cat => cat.license_category_id === categoryId);
            if (foundCategory) {
              category = foundCategory;
              // Also set the license type since we found it
              setSelectedLicenseType(licenseType);
              console.log('Found category and license type:', { category: foundCategory, licenseType });
              break;
            }
          }
        }

        if (category) {
          setSelectedCategory(category);
        } else {
          console.warn('Category not found for ID:', categoryId);
        }
      } catch (error) {
        console.error('Error getting categories:', error);
      }
    }
  }, [mounted, isAuthenticated, authLoading, licenseLoading, router, licenseTypes, typeId, categoryId, getCategoriesByType]);

  useEffect(() => {
    loadApplicationData();
  }, [loadApplicationData]);

  // Handle form submission
  const handleApplicationSubmit = async (formData: Record<string, unknown>) => {
    try {
      console.log('Submitting application:', formData);

      const applicationId = formData.applicationId as string | undefined;
      if (!applicationId || typeof applicationId !== 'string') {
        throw new Error('No application ID available for submission');
      }

      // Submit the application
      await applicationService.submitApplication(applicationId);

      // Redirect to success page or applications list
      router.push('/customer/applications?submitted=true');
    } catch (error) {
      logFormError('Application Submission', error, formData);
      throw error;
    }
  };

  // Don't render if not mounted (prevents hydration issues)
  if (!mounted) {
    return null;
  }

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Loading state
  if (authLoading || licenseLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Error state - missing parameters (but allow continue mode with just categoryId)
  if (!categoryId || (!typeId && !isContinuing)) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <i className="ri-error-warning-line text-4xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Missing Application Parameters
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              License type and category information is required to start an application.
            </p>
            <button
              type="button"
              onClick={() => router.push('/customer/applications')}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors"
            >
              Back to License Types
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                  title="Go back"
                  aria-label="Go back"
                >
                  <i className="ri-arrow-left-line text-xl"></i>
                </button>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    License Application
                  </h1>
                  {selectedLicenseType && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {selectedLicenseType.name}
                      {selectedCategory && ` - ${selectedCategory.name}`}
                    </p>
                  )}
                </div>
              </div>
              
              {/* Status indicator */}
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <i className="ri-save-line mr-1"></i>
                  Auto-save enabled
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Application Form */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Continue Application Indicator */}
          {isContinuing && (
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-center">
                <i className="ri-play-circle-line text-blue-600 dark:text-blue-400 text-lg mr-3"></i>
                <div>
                  <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Continuing Application
                  </h3>
                  <p className="text-blue-700 dark:text-blue-300 text-sm mt-1">
                    You are continuing your application. Your previous progress has been saved.
                    {continueApplicationId && (
                      <span className="ml-2">
                        Application ID: {continueApplicationId.slice(0, 8)}...
                      </span>
                    )}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Show loading while auto-detecting license type in continue mode */}
          {isContinuing && !selectedLicenseType && categoryId ? (
            <div className="flex items-center justify-center min-h-96">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">
                  Auto-detecting license type for continue mode...
                </p>
              </div>
            </div>
          ) : selectedLicenseType && selectedCategory ? (
            (() => {
              console.log('Rendering ApplicationForm with props:', {
                licenseType: selectedLicenseType.name,
                licenseCategory: selectedCategory.name,
                licenseTypeId: selectedLicenseType.license_type_id,
                licenseCategoryId: selectedCategory.license_category_id,
                applicationId: continueApplicationId,
                isContinuing
              });
              return null;
            })(),
            <ClientOnly>
              <ApplicationForm
                licenseType={selectedLicenseType.name}
                licenseCategory={selectedCategory.name}
                licenseTypeId={selectedLicenseType.license_type_id}
                licenseCategoryId={selectedCategory.license_category_id}
                applicationId={continueApplicationId || undefined}
                onSubmit={handleApplicationSubmit}
              />
            </ClientOnly>
          ) : (
            <div className="flex items-center justify-center min-h-96">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">Loading license information...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </CustomerLayout>
  );
};

export default ApplicationFormPage;
