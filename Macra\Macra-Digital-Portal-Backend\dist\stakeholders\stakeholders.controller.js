"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StakeholdersController = void 0;
const common_1 = require("@nestjs/common");
const stakeholders_service_1 = require("./stakeholders.service");
const create_stakeholder_dto_1 = require("../dto/stakeholder/create-stakeholder.dto");
const update_stakeholder_dto_1 = require("../dto/stakeholder/update-stakeholder.dto");
const swagger_1 = require("@nestjs/swagger");
const stakeholders_entity_1 = require("../entities/stakeholders.entity");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let StakeholdersController = class StakeholdersController {
    stakeholderService;
    constructor(stakeholderService) {
        this.stakeholderService = stakeholderService;
    }
    create(createDto, req) {
        return this.stakeholderService.create(createDto, req.user.userId);
    }
    findAll() {
        return this.stakeholderService.findAll();
    }
    findOne(id) {
        return this.stakeholderService.findOne(id);
    }
    update(id, updateDto, req) {
        return this.stakeholderService.update(id, updateDto, req.user.userId);
    }
    remove(id) {
        return this.stakeholderService.softDelete(id);
    }
};
exports.StakeholdersController = StakeholdersController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new stakeholder' }),
    (0, swagger_1.ApiBody)({ type: create_stakeholder_dto_1.CreateStakeholderDto, description: 'Create stakeholder DTO' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Stakeholder created successfully', type: stakeholders_entity_1.Stakeholder }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_stakeholder_dto_1.CreateStakeholderDto, Object]),
    __metadata("design:returntype", void 0)
], StakeholdersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all stakeholders' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of stakeholders', type: [stakeholders_entity_1.Stakeholder] }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], StakeholdersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get stakeholder by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', description: 'Stakeholder UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Stakeholder found', type: stakeholders_entity_1.Stakeholder }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], StakeholdersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update stakeholder by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiBody)({ type: update_stakeholder_dto_1.UpdateStakeholderDto, description: 'Update stakeholder DTO' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Stakeholder updated', type: stakeholders_entity_1.Stakeholder }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_stakeholder_dto_1.UpdateStakeholderDto, Object]),
    __metadata("design:returntype", void 0)
], StakeholdersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete stakeholder by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Stakeholder deleted' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], StakeholdersController.prototype, "remove", null);
exports.StakeholdersController = StakeholdersController = __decorate([
    (0, swagger_1.ApiTags)('Stakeholders'),
    (0, common_1.Controller)('stakeholders'),
    __metadata("design:paramtypes", [stakeholders_service_1.StakeholdersService])
], StakeholdersController);
//# sourceMappingURL=stakeholders.controller.js.map