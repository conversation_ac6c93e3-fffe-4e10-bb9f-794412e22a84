'use client';

import React, { useState, useEffect, useCallback } from 'react';
import TextInput from '@/components/common/TextInput';
import Select from '@/components/common/Select';
import { validateSection } from '@/utils/formValidation';

interface BusinessInfoProps {
  formData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<string>;
  errors: Record<string, string>;
  applicationId?: string;
  isLoading?: boolean;
}

const BusinessInfo: React.FC<BusinessInfoProps> = ({
  formData,
  onChange,
  onSave,
  errors,
  applicationId,
  isLoading = false
}) => {
  const [localData, setLocalData] = useState({
    business_model: '',
    operational_structure: '',
    target_market: '',
    competitive_advantage: '',
    facilities_description: '',
    equipment_description: '',
    operational_areas: '',
    service_delivery_model: '',
    quality_assurance: '',
    customer_support: '',
    ...formData
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Sync formData to localData only when formData changes and is different
  useEffect(() => {
    if (formData && Object.keys(formData).length > 0) {
      setLocalData((prev: any) => {
        const newData = { ...prev, ...formData };
        // Only update if there are actual changes to prevent infinite loops
        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);
        return hasChanges ? newData : prev;
      });
    }
  }, [formData]);

  const handleLocalChange = useCallback((field: string, value: any) => {
    setLocalData((prev: any) => ({ ...prev, [field]: value }));

    // Call onChange with the field and value
    if (onChange) {
      onChange(field, value);
    }

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }
  }, [onChange, validationErrors]);

  const validateForm = () => {
    const validation = validateSection(localData, 'businessInfo');
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      await onSave(localData);
      console.log('Business info saved');
    } catch (error) {
      console.error('Error saving business info:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleBlur = (field: string) => {
    if (localData[field] && !validationErrors[field]) {
      onChange(field, localData[field]);
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Business Information
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Describe your business operations, structure, and service delivery model.
        </p>
      </div>

      {/* Business Model */}
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Business Model *
          </label>
          <textarea
            value={localData.business_model || ''}
            onChange={(e) => handleLocalChange('business_model', e.target.value)}
            onBlur={() => handleBlur('business_model')}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your business model, revenue streams, and value proposition..."
          />
          {(validationErrors.business_model || errors.business_model) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.business_model || errors.business_model}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Operational Structure *
          </label>
          <textarea
            value={localData.operational_structure || ''}
            onChange={(e) => handleLocalChange('operational_structure', e.target.value)}
            onBlur={() => handleBlur('operational_structure')}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your organizational structure, departments, and operational processes..."
          />
          {(validationErrors.operational_structure || errors.operational_structure) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.operational_structure || errors.operational_structure}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Target Market *
          </label>
          <textarea
            value={localData.target_market || ''}
            onChange={(e) => handleLocalChange('target_market', e.target.value)}
            onBlur={() => handleBlur('target_market')}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="Describe your target customers and market segments..."
          />
          {(validationErrors.target_market || errors.target_market) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.target_market || errors.target_market}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Competitive Advantage *
          </label>
          <textarea
            value={localData.competitive_advantage || ''}
            onChange={(e) => handleLocalChange('competitive_advantage', e.target.value)}
            onBlur={() => handleBlur('competitive_advantage')}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
            placeholder="What makes your business unique and competitive in the market..."
          />
          {(validationErrors.competitive_advantage || errors.competitive_advantage) && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
              {validationErrors.competitive_advantage || errors.competitive_advantage}
            </p>
          )}
        </div>
      </div>

      {/* Facilities and Equipment */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Facilities and Equipment
        </h4>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Facilities Description *
            </label>
            <textarea
              value={localData.facilities_description || ''}
              onChange={(e) => handleLocalChange('facilities_description', e.target.value)}
              onBlur={() => handleBlur('facilities_description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe your office locations, facilities, and infrastructure..."
            />
            {(validationErrors.facilities_description || errors.facilities_description) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.facilities_description || errors.facilities_description}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Equipment Description *
            </label>
            <textarea
              value={localData.equipment_description || ''}
              onChange={(e) => handleLocalChange('equipment_description', e.target.value)}
              onBlur={() => handleBlur('equipment_description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="List and describe your equipment, technology, and tools..."
            />
            {(validationErrors.equipment_description || errors.equipment_description) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.equipment_description || errors.equipment_description}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Operational Areas *
            </label>
            <textarea
              value={localData.operational_areas || ''}
              onChange={(e) => handleLocalChange('operational_areas', e.target.value)}
              onBlur={() => handleBlur('operational_areas')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe the geographic areas where you operate or plan to operate..."
            />
            {(validationErrors.operational_areas || errors.operational_areas) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.operational_areas || errors.operational_areas}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Service Delivery */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Service Delivery
        </h4>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Service Delivery Model *
            </label>
            <textarea
              value={localData.service_delivery_model || ''}
              onChange={(e) => handleLocalChange('service_delivery_model', e.target.value)}
              onBlur={() => handleBlur('service_delivery_model')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe how you deliver services to customers..."
            />
            {(validationErrors.service_delivery_model || errors.service_delivery_model) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.service_delivery_model || errors.service_delivery_model}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Quality Assurance *
            </label>
            <textarea
              value={localData.quality_assurance || ''}
              onChange={(e) => handleLocalChange('quality_assurance', e.target.value)}
              onBlur={() => handleBlur('quality_assurance')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe your quality control and assurance processes..."
            />
            {(validationErrors.quality_assurance || errors.quality_assurance) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.quality_assurance || errors.quality_assurance}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Customer Support *
            </label>
            <textarea
              value={localData.customer_support || ''}
              onChange={(e) => handleLocalChange('customer_support', e.target.value)}
              onBlur={() => handleBlur('customer_support')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
              placeholder="Describe your customer support and service processes..."
            />
            {(validationErrors.customer_support || errors.customer_support) && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {validationErrors.customer_support || errors.customer_support}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleSave}
          disabled={isSaving || isLoading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving || isLoading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              Save Business Information
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default BusinessInfo;

// Create placeholder components for the remaining steps
export { default as ServiceScope } from './ServiceScope';
export { default as BusinessPlan } from './BusinessPlan';
export { default as LegalHistory } from './LegalHistory';
export { default as ReviewSubmit } from './ReviewSubmit';
