{"version": 3, "file": "audit-trail.service.js", "sourceRoot": "", "sources": ["../../src/audit-trail/audit-trail.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAyE;AACzE,6CAAmD;AACnD,qCAAoD;AACpD,uEAAmG;AAGnG,qDAA0E;AAC1E,oFAAmG;AACnG,2EAAiE;AACjE,+BAA4B;AAC5B,8CAA2C;AAC3C,0CAAoC;AACpC,mDAAuD;AACvD,yEAA8D;AAGvD,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAYlB;IAEA;IAEA;IAEA;IACA;IAlBO,MAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;IAErD,cAAc,GAAa,EAAE,CAAC;IAErB,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChC,iBAAiB,GAAG,EAAE,CAAC;IAChC,eAAe,GAAG,CAAC,CAAC;IACX,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAEjD,YAEU,oBAA4C,EAE5C,sBAAgD,EAEhD,eAAiC,EAEjC,oBAA4C,EAC5C,aAA4B;QAP5B,yBAAoB,GAApB,oBAAoB,CAAwB;QAE5C,2BAAsB,GAAtB,sBAAsB,CAA0B;QAEhD,oBAAe,GAAf,eAAe,CAAkB;QAEjC,yBAAoB,GAApB,oBAAoB,CAAwB;QAC5C,kBAAa,GAAb,aAAa,CAAe;IAClC,CAAC;IAEL,KAAK,CAAC,MAAM,CAAC,mBAAwC;QACnD,IAAI,CAAC;YAEH,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;gBAC9F,MAAM,IAAI,4BAAmB,CAAC,gDAAgD,CAAC,CAAC;YAClF,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBAClD,GAAG,mBAAmB;gBACtB,aAAa,EAAE,mBAAmB,CAAC,YAAY;aAChD,CAAC,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzE,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,KAAoB,EAAE,OAA4B;QAC9D,IAAI,CAAC;YAEH,MAAM,MAAM,GAA+B;gBACzC,eAAe,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;gBACxE,iBAAiB,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,YAAY,CAAC;gBACjE,aAAa,EAAE,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBACvC,YAAY,EAAE,EAAE;gBAChB,QAAQ,EAAE,GAAG;gBACb,iBAAiB,EAAE;oBACjB,MAAM,EAAE,IAAI;oBACZ,MAAM,EAAE,IAAI;oBACZ,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,IAAI;oBACb,aAAa,EAAE,IAAI;oBACnB,UAAU,EAAE,IAAI;iBACjB;gBACD,SAAS,EAAE,CAAC,MAAM,CAAC;aACpB,CAAC;YAGF,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB;iBAC3C,kBAAkB,CAAC,aAAa,CAAC;iBACjC,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAGjD,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAC3C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,KAAK,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;YAC3D,MAAM,iBAAiB,GAAG,4CAAqB,CAAC,SAAS,CAAa,MAAM,CAAC,CAAC;YAE9E,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,YAAiB,EAAE,OAA2B;QACjE,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACvC,YAAY,CAAC,QAAQ,CAAC,sDAAsD,EAAE;gBAC5E,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC5B,YAAY,CAAC,QAAQ,CAAC,qCAAqC,EAAE;gBAC3D,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YAC1B,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE;gBACzD,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,YAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE;gBACrD,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE;gBACpD,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE;gBACpD,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,YAAY,CAAC,QAAQ,CAAC,8BAA8B,EAAE;gBACpD,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,YAAY,CAAC,QAAQ,CAAC,wCAAwC,EAAE;gBAC9D,SAAS,EAAE,IAAI,OAAO,CAAC,SAAS,GAAG;aACpC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,YAAY,CAAC,QAAQ,CAAC,2CAA2C,EAAE;gBACjE,YAAY,EAAE,OAAO,CAAC,YAAY;aACnC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,YAAY,CAAC,QAAQ,CAAC,uCAAuC,EAAE;gBAC7D,UAAU,EAAE,OAAO,CAAC,UAAU;aAC/B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBAC7C,KAAK,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE;gBACvB,SAAS,EAAE,CAAC,MAAM,CAAC;aACpB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,aAAa,CACjB,MAAmB,EACnB,MAAmB,EACnB,YAAoB,EACpB,MAAe,EACf,UAAmB,EACnB,WAAoB,EACpB,SAA+B,EAC/B,SAA+B,EAC/B,QAA8B,EAC9B,SAAkB,EAClB,SAAkB,EAClB,SAAkB,EAClB,SAAsB,gCAAW,CAAC,OAAO,EACzC,YAAqB;QAErB,IAAI,CAAC;YACH,MAAM,aAAa,GAAwB;gBACzC,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,YAAY;gBACZ,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,YAAY;gBACZ,MAAM;aACP,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,YAAY,CAChB,MAAmB,EACnB,MAAe,EACf,SAAkB,EAClB,SAAkB,EAClB,SAAkB,EAClB,SAAsB,gCAAW,CAAC,OAAO,EACzC,YAAqB,EACrB,QAA8B;QAE9B,OAAO,IAAI,CAAC,aAAa,CACvB,MAAM,EACN,gCAAW,CAAC,cAAc,EAC1B,gBAAgB,EAChB,MAAM,EACN,SAAS,EACT,QAAQ,MAAM,UAAU,EACxB,SAAS,EACT,SAAS,EACT,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,YAAY,CACb,CAAC;IACJ,CAAC;IAMD,KAAK,CAAC,UAAU,CAAC,IAA2B;QAC1C,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;gBACjD,GAAG,IAAI;gBACP,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,SAAS;gBAClC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,SAAS;aAC7B,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAGhD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;YAGrC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAC9C,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAC5C,CAAC;YAEF,IACE,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB;gBACpD,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,EACvD,CAAC;gBACD,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACpD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACpC,CAAC;QAEH,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,YAAoB;QAG7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,YAAY,yBAAyB,IAAI,CAAC,cAAc,GAAG,KAAK,UAAU,CAAC,CAAC;QACjI,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC5C,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,EAAE;SAC5C,CAAC,CAAC;QACH,KAAK,CAAC,OAAO,CAAC,KAAK,EAAC,IAAI,EAAC,EAAE;YACzB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;gBACtB,EAAE,EAAE,IAAI,CAAC,KAAK;gBACd,OAAO,EAAE,gDAAgD;gBACzD,QAAQ,EAAE,eAAe;gBACzB,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI,CAAC,UAAU;oBACzB,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBAC9B,QAAQ,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,aAAa;oBAClD,OAAO,EAAE,mBAAmB,YAAY,+BAA+B,IAAI,CAAC,cAAc,GAAG,KAAK,+BAA+B;iBAClI;gBACD,WAAW,EAAE;oBACX;wBACE,QAAQ,EAAE,gBAAgB;wBAC1B,IAAI,EAAE,IAAA,WAAI,EAAC,sBAAS,EAAE,gBAAgB,CAAC;wBACvC,GAAG,EAAE,YAAY;qBAClB;iBACF;aACF,CAAC;gBACF,IAAI,CAAC,KAAK,IAAG,EAAE,CACb,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBACnC,QAAQ,EAAE,eAAe;gBACzB,OAAO,EAAE,gBAAgB,YAAY,+CAA+C,IAAI,CAAC,eAAe,EAAE;aAC7G,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yDAAyD,EAAE,KAAK,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACT,CAAC;CACF,CAAA;AAlSY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;IAYR,WAAA,IAAA,0BAAgB,EAAC,+BAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,eAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,gCAAU,CAAC,CAAA;qCALC,oBAAU;QAER,oBAAU;QAEjB,oBAAU;QAEL,oBAAU;QACjB,sBAAa;GAnB3B,iBAAiB,CAkS7B"}