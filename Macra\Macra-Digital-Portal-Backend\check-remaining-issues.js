const mysql = require('mysql2/promise');

async function checkRemainingIssues() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'macra_db'
    });

    try {
        console.log('Checking remaining consumer affairs complaints with invalid complainant_id...');
        
        // Check for records with empty string complainant_id
        const [emptyRows] = await connection.execute(`
            SELECT complaint_id, complainant_id, title, created_at
            FROM consumer_affairs_complaints
            WHERE complainant_id = '' OR complainant_id IS NULL
            LIMIT 20
        `);

        console.log(`Found ${emptyRows.length} records with empty/null complainant_id:`);
        emptyRows.forEach(row => {
            console.log(`- ID: ${row.complaint_id}, Complainant ID: '${row.complainant_id}', Title: ${row.title}`);
        });

        // Check for records with non-existent complainant_id
        const [invalidRows] = await connection.execute(`
            SELECT complaint_id, complainant_id, title, created_at
            FROM consumer_affairs_complaints
            WHERE complainant_id IS NOT NULL
            AND complainant_id != ''
            AND complainant_id NOT IN (SELECT user_id FROM users WHERE user_id IS NOT NULL)
            LIMIT 20
        `);

        console.log(`Found ${invalidRows.length} records with invalid complainant_id:`);
        invalidRows.forEach(row => {
            console.log(`- ID: ${row.complaint_id}, Complainant ID: ${row.complainant_id}, Title: ${row.title}`);
        });

        const totalProblematic = emptyRows.length + invalidRows.length;
        if (totalProblematic > 0) {
            console.log('\nDeleting all problematic records...');
            const [result] = await connection.execute(`
                DELETE FROM consumer_affairs_complaints
                WHERE complainant_id = ''
                OR complainant_id IS NULL
                OR (complainant_id IS NOT NULL
                    AND complainant_id != ''
                    AND complainant_id NOT IN (SELECT user_id FROM users WHERE user_id IS NOT NULL))
            `);
            console.log(`Deleted ${result.affectedRows} records`);
        }

        console.log('\nVerification - checking if any issues remain...');
        const [verification] = await connection.execute(`
            SELECT COUNT(*) as count
            FROM consumer_affairs_complaints
            WHERE complainant_id = ''
            OR complainant_id IS NULL
            OR (complainant_id IS NOT NULL
                AND complainant_id != ''
                AND complainant_id NOT IN (SELECT user_id FROM users WHERE user_id IS NOT NULL))
        `);
        
        console.log(`Remaining problematic records: ${verification[0].count}`);
        
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await connection.end();
    }
}

checkRemainingIssues();
