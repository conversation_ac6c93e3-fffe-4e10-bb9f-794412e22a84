import { User } from './user.entity';
import { Applicants } from './applicant.entity';
export declare class ContactPersons {
    contact_id: string;
    applicant_id: string;
    first_name: string;
    last_name: string;
    middle_name?: string;
    designation: string;
    email: string;
    phone: string;
    is_primary: boolean;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    applicant: Applicants;
    creator: User;
    updater?: User;
    generateId(): void;
}
