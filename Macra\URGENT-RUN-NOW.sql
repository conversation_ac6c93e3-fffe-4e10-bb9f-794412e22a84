-- !! URGENT: Run this SQL script NOW before restarting TypeORM !!
-- This will fix the complainant_id foreign key constraint error

-- Step 1: Check what records will be affected
SELECT 
    complaint_id,
    complainant_id,
    title,
    'WILL BE DELETED - invalid complainant_id' as action
FROM consumer_affairs_complaints cac
WHERE complainant_id NOT IN (SELECT user_id FROM users)
LIMIT 20;

-- Step 2: Count total affected records
SELECT 
    COUNT(*) as total_records_to_delete,
    'consumer_affairs_complaints with invalid complainant_id' as description
FROM consumer_affairs_complaints cac
WHERE complainant_id NOT IN (SELECT user_id FROM users);

-- Step 3: Delete the problematic records
-- (complainant_id is required, so we must delete invalid records)
DELETE FROM consumer_affairs_complaints 
WHERE complainant_id NOT IN (SELECT user_id FROM users);

-- Step 4: Also clean up related data that might cause issues
DELETE FROM consumer_affairs_complaint_attachments 
WHERE complaint_id NOT IN (SELECT complaint_id FROM consumer_affairs_complaints);

DELETE FROM consumer_affairs_complaint_status_history 
WHERE complaint_id NOT IN (SELECT complaint_id FROM consumer_affairs_complaints);

-- Step 5: Fix any remaining user reference issues
UPDATE consumer_affairs_complaints 
SET created_by = NULL 
WHERE created_by IS NOT NULL 
AND created_by NOT IN (SELECT user_id FROM users);

UPDATE consumer_affairs_complaints 
SET updated_by = NULL 
WHERE updated_by IS NOT NULL 
AND updated_by NOT IN (SELECT user_id FROM users);

UPDATE consumer_affairs_complaints 
SET assigned_to = NULL 
WHERE assigned_to IS NOT NULL 
AND assigned_to NOT IN (SELECT user_id FROM users);

-- Step 6: Verify the fix
SELECT 
    COUNT(*) as remaining_invalid_complainants,
    'Should be 0 for TypeORM to work' as status
FROM consumer_affairs_complaints cac
WHERE complainant_id NOT IN (SELECT user_id FROM users);

SELECT 'SUCCESS: Now you can restart your TypeORM application!' as message;