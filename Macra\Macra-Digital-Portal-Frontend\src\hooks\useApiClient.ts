/**
 * Hook for using the enhanced API client with React components
 * Provides automatic error handling and loading states
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { apiClient } from '@/utils/enhancedApiClient';

interface UseApiClientOptions {
  onError?: (error: any) => void;
  onSuccess?: (data: any) => void;
  retryOnRateLimit?: boolean;
  priority?: number;
}

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: any;
  retryCount: number;
}

export const useApiClient = <T = any>(options: UseApiClientOptions = {}) => {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    retryCount: 0
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const {
    onError,
    onSuccess,
    retryOnRateLimit = true,
    priority = 3
  } = options;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const executeRequest = useCallback(async (
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    endpoint: string,
    body?: any,
    requestOptions: any = {}
  ): Promise<T | null> => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    try {
      let response: T;
      const options = {
        priority,
        signal: abortControllerRef.current.signal,
        ...requestOptions
      };

      switch (method) {
        case 'GET':
          response = await apiClient.get<T>(endpoint, options);
          break;
        case 'POST':
          response = await apiClient.post<T>(endpoint, body, options);
          break;
        case 'PUT':
          response = await apiClient.put<T>(endpoint, body, options);
          break;
        case 'DELETE':
          response = await apiClient.delete<T>(endpoint, options);
          break;
        case 'PATCH':
          response = await apiClient.patch<T>(endpoint, body, options);
          break;
        default:
          throw new Error(`Unsupported method: ${method}`);
      }

      setState(prev => ({
        ...prev,
        data: response,
        loading: false,
        error: null,
        retryCount: 0
      }));

      if (onSuccess) {
        onSuccess(response);
      }

      return response;
    } catch (error: any) {
      // Handle rate limiting with automatic retry
      if (error.status === 429 && retryOnRateLimit && state.retryCount < 3) {
        const retryAfter = error.retryAfter ? parseInt(error.retryAfter) * 1000 : 2000;
        
        setState(prev => ({
          ...prev,
          loading: true,
          retryCount: prev.retryCount + 1
        }));

        // Wait and retry
        setTimeout(() => {
          executeRequest(method, endpoint, body, requestOptions);
        }, retryAfter);

        return null;
      }

      setState(prev => ({
        ...prev,
        loading: false,
        error,
        retryCount: error.status === 429 ? prev.retryCount + 1 : 0
      }));

      if (onError) {
        onError(error);
      }

      throw error;
    }
  }, [priority, retryOnRateLimit, state.retryCount, onError, onSuccess]);

  // Convenience methods
  const get = useCallback((endpoint: string, options?: any) => {
    return executeRequest('GET', endpoint, undefined, options);
  }, [executeRequest]);

  const post = useCallback((endpoint: string, body?: any, options?: any) => {
    return executeRequest('POST', endpoint, body, options);
  }, [executeRequest]);

  const put = useCallback((endpoint: string, body?: any, options?: any) => {
    return executeRequest('PUT', endpoint, body, options);
  }, [executeRequest]);

  const del = useCallback((endpoint: string, options?: any) => {
    return executeRequest('DELETE', endpoint, undefined, options);
  }, [executeRequest]);

  const patch = useCallback((endpoint: string, body?: any, options?: any) => {
    return executeRequest('PATCH', endpoint, body, options);
  }, [executeRequest]);

  // Manual retry function
  const retry = useCallback(() => {
    if (state.error && state.retryCount < 3) {
      setState(prev => ({ ...prev, error: null, retryCount: prev.retryCount + 1 }));
      // Note: This would need to store the last request details to retry
      console.log('Manual retry triggered');
    }
  }, [state.error, state.retryCount]);

  // Reset state
  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      retryCount: 0
    });
  }, []);

  // Cancel current request
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setState(prev => ({ ...prev, loading: false }));
    }
  }, []);

  return {
    // State
    data: state.data,
    loading: state.loading,
    error: state.error,
    retryCount: state.retryCount,
    
    // Methods
    get,
    post,
    put,
    delete: del,
    patch,
    
    // Utilities
    retry,
    reset,
    cancel,
    
    // Status checks
    isRateLimit: state.error?.status === 429,
    canRetry: state.retryCount < 3,
    isRetrying: state.loading && state.retryCount > 0
  };
};

// Specialized hook for data fetching with automatic loading
export const useApiData = <T = any>(
  endpoint: string,
  options: UseApiClientOptions & { 
    immediate?: boolean;
    dependencies?: any[];
  } = {}
) => {
  const { immediate = true, dependencies = [], ...apiOptions } = options;
  const api = useApiClient<T>(apiOptions);

  useEffect(() => {
    if (immediate && endpoint) {
      api.get(endpoint);
    }
  }, [endpoint, immediate, ...dependencies]);

  return api;
};

// Hook for form submissions with enhanced error handling
export const useApiSubmit = <T = any>(options: UseApiClientOptions = {}) => {
  const api = useApiClient<T>({
    priority: 4, // Higher priority for user actions
    ...options
  });

  const submit = useCallback(async (
    endpoint: string,
    data: any,
    method: 'POST' | 'PUT' | 'PATCH' = 'POST'
  ) => {
    try {
      const result = await api[method.toLowerCase() as 'post' | 'put' | 'patch'](endpoint, data);
      return result;
    } catch (error) {
      // Enhanced error handling for form submissions
      if ((error as any).status === 429) {
        throw new Error('Too many requests. Please wait a moment and try again.');
      } else if ((error as any).status >= 500) {
        throw new Error('Server error. Please try again later.');
      } else if ((error as any).status === 422) {
        throw new Error('Validation error. Please check your input.');
      }
      throw error;
    }
  }, [api]);

  return {
    ...api,
    submit,
    isSubmitting: api.loading
  };
};

export default useApiClient;
