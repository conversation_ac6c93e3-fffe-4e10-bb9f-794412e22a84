"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsumerAffairsComplaintStatusHistory = exports.ConsumerAffairsComplaintAttachment = exports.ConsumerAffairsComplaint = exports.ComplaintPriority = exports.ComplaintStatus = exports.ComplaintCategory = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const class_validator_1 = require("class-validator");
const user_entity_1 = require("../entities/user.entity");
var ComplaintCategory;
(function (ComplaintCategory) {
    ComplaintCategory["BILLING_CHARGES"] = "Billing & Charges";
    ComplaintCategory["SERVICE_QUALITY"] = "Service Quality";
    ComplaintCategory["NETWORK_ISSUES"] = "Network Issues";
    ComplaintCategory["CUSTOMER_SERVICE"] = "Customer Service";
    ComplaintCategory["CONTRACT_DISPUTES"] = "Contract Disputes";
    ComplaintCategory["ACCESSIBILITY"] = "Accessibility";
    ComplaintCategory["FRAUD_SCAMS"] = "Fraud & Scams";
    ComplaintCategory["OTHER"] = "Other";
})(ComplaintCategory || (exports.ComplaintCategory = ComplaintCategory = {}));
var ComplaintStatus;
(function (ComplaintStatus) {
    ComplaintStatus["SUBMITTED"] = "submitted";
    ComplaintStatus["UNDER_REVIEW"] = "under_review";
    ComplaintStatus["INVESTIGATING"] = "investigating";
    ComplaintStatus["RESOLVED"] = "resolved";
    ComplaintStatus["CLOSED"] = "closed";
})(ComplaintStatus || (exports.ComplaintStatus = ComplaintStatus = {}));
var ComplaintPriority;
(function (ComplaintPriority) {
    ComplaintPriority["LOW"] = "low";
    ComplaintPriority["MEDIUM"] = "medium";
    ComplaintPriority["HIGH"] = "high";
    ComplaintPriority["URGENT"] = "urgent";
})(ComplaintPriority || (exports.ComplaintPriority = ComplaintPriority = {}));
let ConsumerAffairsComplaint = class ConsumerAffairsComplaint {
    complaint_id;
    complaint_number;
    complainant_id;
    title;
    description;
    category;
    status;
    priority;
    assigned_to;
    resolution;
    internal_notes;
    resolved_at;
    created_at;
    updated_at;
    deleted_at;
    created_by;
    updated_by;
    complainant;
    assignee;
    creator;
    updater;
    attachments;
    status_history;
    generateId() {
        if (!this.complaint_id) {
            this.complaint_id = (0, uuid_1.v4)();
        }
        if (!this.complaint_number) {
            const year = new Date().getFullYear();
            const randomNum = Math.floor(Math.random() * 999) + 1;
            this.complaint_number = `COMP-${year}-${randomNum.toString().padStart(3, '0')}`;
        }
    }
};
exports.ConsumerAffairsComplaint = ConsumerAffairsComplaint;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "complaint_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', unique: true }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "complaint_number", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "complainant_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ComplaintCategory,
    }),
    (0, class_validator_1.IsEnum)(ComplaintCategory),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ComplaintStatus,
        default: ComplaintStatus.SUBMITTED,
    }),
    (0, class_validator_1.IsEnum)(ComplaintStatus),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ComplaintPriority,
        default: ComplaintPriority.MEDIUM,
    }),
    (0, class_validator_1.IsEnum)(ComplaintPriority),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "assigned_to", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "resolution", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "internal_notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaint.prototype, "resolved_at", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaint.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaint.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaint.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaint.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'complainant_id' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaint.prototype, "complainant", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'assigned_to' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaint.prototype, "assignee", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaint.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaint.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ConsumerAffairsComplaintAttachment, (attachment) => attachment.complaint),
    __metadata("design:type", Array)
], ConsumerAffairsComplaint.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => ConsumerAffairsComplaintStatusHistory, (history) => history.complaint),
    __metadata("design:type", Array)
], ConsumerAffairsComplaint.prototype, "status_history", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ConsumerAffairsComplaint.prototype, "generateId", null);
exports.ConsumerAffairsComplaint = ConsumerAffairsComplaint = __decorate([
    (0, typeorm_1.Entity)('consumer_affairs_complaints')
], ConsumerAffairsComplaint);
let ConsumerAffairsComplaintAttachment = class ConsumerAffairsComplaintAttachment {
    attachment_id;
    complaint_id;
    file_name;
    file_path;
    file_type;
    file_size;
    uploaded_at;
    uploaded_by;
    complaint;
    uploader;
    generateId() {
        if (!this.attachment_id) {
            this.attachment_id = (0, uuid_1.v4)();
        }
    }
};
exports.ConsumerAffairsComplaintAttachment = ConsumerAffairsComplaintAttachment;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintAttachment.prototype, "attachment_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintAttachment.prototype, "complaint_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintAttachment.prototype, "file_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintAttachment.prototype, "file_path", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintAttachment.prototype, "file_type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint' }),
    __metadata("design:type", Number)
], ConsumerAffairsComplaintAttachment.prototype, "file_size", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaintAttachment.prototype, "uploaded_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintAttachment.prototype, "uploaded_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ConsumerAffairsComplaint, (complaint) => complaint.attachments),
    (0, typeorm_1.JoinColumn)({ name: 'complaint_id' }),
    __metadata("design:type", ConsumerAffairsComplaint)
], ConsumerAffairsComplaintAttachment.prototype, "complaint", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'uploaded_by' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaintAttachment.prototype, "uploader", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ConsumerAffairsComplaintAttachment.prototype, "generateId", null);
exports.ConsumerAffairsComplaintAttachment = ConsumerAffairsComplaintAttachment = __decorate([
    (0, typeorm_1.Entity)('consumer_affairs_complaint_attachments')
], ConsumerAffairsComplaintAttachment);
let ConsumerAffairsComplaintStatusHistory = class ConsumerAffairsComplaintStatusHistory {
    history_id;
    complaint_id;
    status;
    comment;
    created_at;
    created_by;
    complaint;
    creator;
    generateId() {
        if (!this.history_id) {
            this.history_id = (0, uuid_1.v4)();
        }
    }
};
exports.ConsumerAffairsComplaintStatusHistory = ConsumerAffairsComplaintStatusHistory;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "history_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "complaint_id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ComplaintStatus,
    }),
    (0, class_validator_1.IsEnum)(ComplaintStatus),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "comment", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ConsumerAffairsComplaintStatusHistory.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintStatusHistory.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ConsumerAffairsComplaint, (complaint) => complaint.status_history),
    (0, typeorm_1.JoinColumn)({ name: 'complaint_id' }),
    __metadata("design:type", ConsumerAffairsComplaint)
], ConsumerAffairsComplaintStatusHistory.prototype, "complaint", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ConsumerAffairsComplaintStatusHistory.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ConsumerAffairsComplaintStatusHistory.prototype, "generateId", null);
exports.ConsumerAffairsComplaintStatusHistory = ConsumerAffairsComplaintStatusHistory = __decorate([
    (0, typeorm_1.Entity)('consumer_affairs_complaint_status_history')
], ConsumerAffairsComplaintStatusHistory);
//# sourceMappingURL=consumer-affairs-complaint.entity.js.map