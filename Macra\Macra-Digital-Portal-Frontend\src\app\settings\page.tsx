'use client';

import { useState, useEffect } from 'react';
import { LicenseType } from '../../services/licenseTypeService';
import { LicenseCategory } from '../../services/licenseCategoryService';
import { IdentificationType } from '../../services/identificationTypeService';
import { LicenseCategoryDocument } from '../../services/licenseCategoryDocumentService';
import SettingsTabs from '../../components/settings/SettingsTabs';
import LicenseTypesTab from '../../components/settings/LicenseTypesTab';
import LicenseCategoriesTab from '../../components/settings/LicenseCategoriesTab';
import IdentificationTypesTab from '../../components/settings/IdentificationTypesTab';
import LicenseCategoryDocumentsTab from '../../components/settings/LicenseCategoryDocumentsTab';
import LicenseTypeModal from '../../components/settings/LicenseTypeModal';
import LicenseCategoryModal from '../../components/settings/LicenseCategoryModal';
import IdentificationTypeModal from '../../components/settings/IdentificationTypeModal';
import LicenseCategoryDocumentModal from '../../components/settings/LicenseCategoryDocumentModal';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('license-types');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // License Types Modal State
  const [isLicenseTypeModalOpen, setIsLicenseTypeModalOpen] = useState(false);
  const [editingLicenseType, setEditingLicenseType] = useState<LicenseType | null>(null);

  // License Categories Modal State
  const [isLicenseCategoryModalOpen, setIsLicenseCategoryModalOpen] = useState(false);
  const [editingLicenseCategory, setEditingLicenseCategory] = useState<LicenseCategory | null>(null);

  // Identification Types Modal State
  const [isIdentificationTypeModalOpen, setIsIdentificationTypeModalOpen] = useState(false);
  const [editingIdentificationType, setEditingIdentificationType] = useState<IdentificationType | null>(null);

  // License Category Documents Modal State
  const [isLicenseCategoryDocumentModalOpen, setIsLicenseCategoryDocumentModalOpen] = useState(false);
  const [editingLicenseCategoryDocument, setEditingLicenseCategoryDocument] = useState<LicenseCategoryDocument | null>(null);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // Refresh functions for each tab
  const [refreshLicenseTypes, setRefreshLicenseTypes] = useState(0);
  const [refreshLicenseCategories, setRefreshLicenseCategories] = useState(0);
  const [refreshIdentificationTypes, setRefreshIdentificationTypes] = useState(0);
  const [refreshLicenseCategoryDocuments, setRefreshLicenseCategoryDocuments] = useState(0);

  // License Type Handlers
  const handleCreateLicenseType = () => {
    setEditingLicenseType(null);
    setIsLicenseTypeModalOpen(true);
  };

  const handleEditLicenseType = (licenseType: LicenseType) => {
    setEditingLicenseType(licenseType);
    setIsLicenseTypeModalOpen(true);
  };

  const handleLicenseTypeModalClose = () => {
    setIsLicenseTypeModalOpen(false);
    setEditingLicenseType(null);
  };

  const handleLicenseTypeSaved = (licenseTypeName: string, isEdit: boolean = false) => {
    handleLicenseTypeModalClose();
    const action = isEdit ? 'updated' : 'created';
    setSuccessMessage(`License type "${licenseTypeName}" has been ${action} successfully!`);
    setErrorMessage('');
    setTimeout(() => setSuccessMessage(''), 5000);
    // Trigger refresh of license types table
    setRefreshLicenseTypes(prev => prev + 1);
  };

  // License Category Handlers
  const handleCreateLicenseCategory = () => {
    setEditingLicenseCategory(null);
    setIsLicenseCategoryModalOpen(true);
  };

  const handleEditLicenseCategory = (licenseCategory: LicenseCategory) => {
    setEditingLicenseCategory(licenseCategory);
    setIsLicenseCategoryModalOpen(true);
  };

  const handleLicenseCategoryModalClose = () => {
    setIsLicenseCategoryModalOpen(false);
    setEditingLicenseCategory(null);
  };

  const handleLicenseCategorySaved = (licenseCategoryName: string, isEdit: boolean = false) => {
    handleLicenseCategoryModalClose();
    const action = isEdit ? 'updated' : 'created';
    setSuccessMessage(`License category "${licenseCategoryName}" has been ${action} successfully!`);
    setErrorMessage('');
    setTimeout(() => setSuccessMessage(''), 5000);
    // Trigger refresh of license categories table
    setRefreshLicenseCategories(prev => prev + 1);
  };

  // Identification Type Handlers
  const handleCreateIdentificationType = () => {
    setEditingIdentificationType(null);
    setIsIdentificationTypeModalOpen(true);
  };

  const handleEditIdentificationType = (identificationType: IdentificationType) => {
    setEditingIdentificationType(identificationType);
    setIsIdentificationTypeModalOpen(true);
  };

  const handleIdentificationTypeModalClose = () => {
    setIsIdentificationTypeModalOpen(false);
    setEditingIdentificationType(null);
  };

  const handleIdentificationTypeSaved = (identificationTypeName: string, isEdit: boolean = false) => {
    handleIdentificationTypeModalClose();
    const action = isEdit ? 'updated' : 'created';
    setSuccessMessage(`Identification type "${identificationTypeName}" has been ${action} successfully!`);
    setErrorMessage('');
    setTimeout(() => setSuccessMessage(''), 5000);
    // Trigger refresh of identification types table
    setRefreshIdentificationTypes(prev => prev + 1);
  };

  // License Category Document Handlers
  const handleCreateLicenseCategoryDocument = () => {
    setEditingLicenseCategoryDocument(null);
    setIsLicenseCategoryDocumentModalOpen(true);
  };

  const handleEditLicenseCategoryDocument = (document: LicenseCategoryDocument) => {
    setEditingLicenseCategoryDocument(document);
    setIsLicenseCategoryDocumentModalOpen(true);
  };

  const handleLicenseCategoryDocumentModalClose = () => {
    setIsLicenseCategoryDocumentModalOpen(false);
    setEditingLicenseCategoryDocument(null);
  };

  const handleLicenseCategoryDocumentSaved = (documentName: string, isEdit: boolean = false) => {
    handleLicenseCategoryDocumentModalClose();
    const action = isEdit ? 'updated' : 'created';
    setSuccessMessage(`Document requirement "${documentName}" has been ${action} successfully!`);
    setErrorMessage('');
    setTimeout(() => setSuccessMessage(''), 5000);
    // Trigger refresh of license category documents table
    setRefreshLicenseCategoryDocuments(prev => prev + 1);
  };

  // Define tabs for the tab system
  const tabs = [
    {
      id: 'license-types',
      label: 'License Types',
      content: (
        <LicenseTypesTab
          onEditLicenseType={handleEditLicenseType}
          onCreateLicenseType={handleCreateLicenseType}
          refreshTrigger={refreshLicenseTypes}
        />
      )
    },
    {
      id: 'license-categories',
      label: 'License Categories',
      content: (
        <LicenseCategoriesTab
          onEditLicenseCategory={handleEditLicenseCategory}
          onCreateLicenseCategory={handleCreateLicenseCategory}
          refreshTrigger={refreshLicenseCategories}
        />
      )
    },

    {
      id: 'license-category-documents',
      label: 'Document Requirements',
      content: (
        <LicenseCategoryDocumentsTab
          onEditLicenseCategoryDocument={handleEditLicenseCategoryDocument}
          onCreateLicenseCategoryDocument={handleCreateLicenseCategoryDocument}
          refreshTrigger={refreshLicenseCategoryDocuments}
        />
      )
    },    {
      id: 'identification-types',
      label: 'Identification Types',
      content: (
        <IdentificationTypesTab
          onEditIdentificationType={handleEditIdentificationType}
          onCreateIdentificationType={handleCreateIdentificationType}
          refreshTrigger={refreshIdentificationTypes}
        />
      )
    },
  ];

  return (
    <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage license types, categories, and identification types
          </p>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="h-5 w-5 text-green-400 dark:text-green-500">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200 mb-1">
                  Success
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300">
                  {successMessage}
                </p>
              </div>
              <div className="ml-4 flex-shrink-0">
                <button
                  type="button"
                  onClick={() => setSuccessMessage('')}
                  className="inline-flex text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200"
                  aria-label="Dismiss success message"
                >
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {errorMessage && (
          <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="h-5 w-5 text-red-400 dark:text-red-500">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                  Error
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300">
                  {errorMessage}
                </p>
              </div>
              <div className="ml-4 flex-shrink-0">
                <button
                  type="button"
                  onClick={() => setErrorMessage('')}
                  className="inline-flex text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200"
                  aria-label="Dismiss error message"
                >
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        <SettingsTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
      </div>

      {/* Modals */}
      <LicenseTypeModal
        isOpen={isLicenseTypeModalOpen}
        onClose={handleLicenseTypeModalClose}
        onSave={handleLicenseTypeSaved}
        licenseType={editingLicenseType}
      />

      <LicenseCategoryModal
        isOpen={isLicenseCategoryModalOpen}
        onClose={handleLicenseCategoryModalClose}
        onSave={handleLicenseCategorySaved}
        licenseCategory={editingLicenseCategory}
      />

      <IdentificationTypeModal
        isOpen={isIdentificationTypeModalOpen}
        onClose={handleIdentificationTypeModalClose}
        onSave={handleIdentificationTypeSaved}
        identificationType={editingIdentificationType}
      />

      <LicenseCategoryDocumentModal
        isOpen={isLicenseCategoryDocumentModalOpen}
        onClose={handleLicenseCategoryDocumentModalClose}
        onSave={handleLicenseCategoryDocumentSaved}
        document={editingLicenseCategoryDocument}
      />
    </main>
  );
}
