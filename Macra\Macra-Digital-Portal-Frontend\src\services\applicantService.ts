import { apiClient } from '../lib/apiClient';
import { Applicant } from '../types/license';

export interface CreateApplicantData {
  name: string;
  business_registration_number: string;
  tpin: string;
  website: string;
  email: string;
  phone: string;
  fax?: string;
  level_of_insurance_cover?: string;
  address_id?: string;
  contact_id?: string;
  date_incorporation: Date;
  place_incorporation: string;
}

export const applicantService = {
  // Create new applicant
  async createApplicant(data: CreateApplicantData): Promise<Applicant> {
    try {
      console.log('Creating applicant with data:', data);

      const response = await apiClient.post('/applicants', data);

      console.log('Raw applicant response:', response);
      console.log('Applicant response data:', response.data);
      console.log('Response data structure:', JSON.stringify(response.data, null, 2));

      // Handle different response formats
      if (response.data) {
        // Check if it's a standard success response format
        if (response.data.success !== undefined && response.data.data) {
          console.log('Standard response format detected');
          return response.data.data;
        }
        // Check if it's direct data format
        else if (response.data.applicant_id || response.data.id) {
          console.log('Direct data format detected');
          return response.data;
        }
        // Fallback: assume it's the applicant data
        else {
          console.log('Fallback: treating response.data as applicant');
          return response.data;
        }
      }

      throw new Error('Invalid response format from applicant creation');
    } catch (error) {
      console.error('Error creating applicant:', error);
      console.error('Error details:', (error as any)?.response?.data);
      throw error;
    }
  },

  // Get applicant by ID
  async getApplicant(id: string): Promise<Applicant> {
    try {
      const response = await apiClient.get(`/applicants/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching applicant:', error);
      throw error;
    }
  },

  // Update applicant
  async updateApplicant(id: string, data: Partial<CreateApplicantData>): Promise<Applicant> {
    try {
      console.log('Updating applicant:', id, data);
      
      const response = await apiClient.put(`/applicants/${id}`, data);
      
      console.log('Applicant updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating applicant:', error);
      throw error;
    }
  },

  // Get applicants by user (if user can have multiple applicants)
  async getApplicantsByUser(): Promise<Applicant[]> {
    try {
      const response = await apiClient.get('/applicants/by-user');
      return response.data;
    } catch (error) {
      console.error('Error fetching user applicants:', error);
      throw error;
    }
  },

  // Delete applicant
  async deleteApplicant(id: string): Promise<{ message: string }> {
    try {
      const response = await apiClient.delete(`/applicants/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting applicant:', error);
      throw error;
    }
  }
};
