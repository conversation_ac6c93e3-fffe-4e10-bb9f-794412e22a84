'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';

interface Payment {
  id: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  dueDate: string;
  paidDate?: string;
  issueDate: string;
  description: string;
  paymentType: string;
  clientName: string;
  clientEmail: string;
  paymentMethod?: string;
  notes?: string;
}

// Sample payment data with different types including procurement
const samplePayments: Payment[] = [
  {
    id: 'PAY-2025-001',
    invoiceNumber: 'INV-2025-001',
    amount: ********,
    currency: 'MWK',
    status: 'paid',
    dueDate: '2025-04-11',
    paidDate: '2025-03-15',
    issueDate: '2025-03-11',
    description: 'Internet Service Provider License - 5 year license for telecommunications services',
    paymentType: 'License Fee',
    clientName: 'Airtel Malawi',
    clientEmail: '<EMAIL>',
    paymentMethod: 'Bank Transfer',
    notes: 'Payment for internet service provider license renewal'
  },
  {
    id: 'PAY-2025-002',
    invoiceNumber: 'INV-2025-002',
    amount: 150000,
    currency: 'MWK',
    status: 'paid',
    dueDate: '2025-02-15',
    paidDate: '2025-02-10',
    issueDate: '2025-01-15',
    description: 'Tender document for procurement - ICT equipment procurement tender documentation',
    paymentType: 'Procurement Fee',
    clientName: 'TechSolutions Ltd',
    clientEmail: '<EMAIL>',
    paymentMethod: 'Mobile Money',
    notes: 'Payment for tender document access and procurement process participation'
  },
  {
    id: 'PAY-2025-003',
    invoiceNumber: 'INV-2025-003',
    amount: 6565000,
    currency: 'MWK',
    status: 'pending',
    dueDate: '2025-01-25',
    issueDate: '2024-12-25',
    description: 'Radio Broadcasting License - Commercial radio broadcasting license for FM frequency',
    paymentType: 'License Fee',
    clientName: 'Dawn FM',
    clientEmail: '<EMAIL>',
    notes: 'Radio broadcasting license for 3 years'
  },
  {
    id: 'PAY-2025-004',
    invoiceNumber: 'INV-2025-004',
    amount: 75000,
    currency: 'MWK',
    status: 'paid',
    dueDate: '2025-03-01',
    paidDate: '2025-02-28',
    issueDate: '2025-02-01',
    description: 'Tender document for procurement - Network infrastructure upgrade tender',
    paymentType: 'Procurement Fee',
    clientName: 'NetworkPro Systems',
    clientEmail: '<EMAIL>',
    paymentMethod: 'Credit Card',
    notes: 'Tender documentation fee for network infrastructure procurement'
  },
  {
    id: 'PAY-2025-005',
    invoiceNumber: 'INV-2025-005',
    amount: 50000000,
    currency: 'MWK',
    status: 'overdue',
    dueDate: '2025-02-01',
    issueDate: '2025-01-01',
    description: 'TV Broadcasting License - Digital terrestrial television broadcasting license',
    paymentType: 'License Fee',
    clientName: 'Crunchyroll TV',
    clientEmail: '<EMAIL>',
    notes: 'TV broadcasting license for digital terrestrial services'
  },
  {
    id: 'PAY-2025-006',
    invoiceNumber: 'INV-2025-006',
    amount: 25000,
    currency: 'MWK',
    status: 'paid',
    dueDate: '2025-01-20',
    paidDate: '2025-01-18',
    issueDate: '2024-12-20',
    description: 'Tender document for procurement - Regulatory compliance software procurement',
    paymentType: 'Procurement Fee',
    clientName: 'ComplianceTech Solutions',
    clientEmail: '<EMAIL>',
    paymentMethod: 'Bank Transfer',
    notes: 'Procurement tender for regulatory compliance management system'
  },
  {
    id: 'PAY-2024-007',
    invoiceNumber: 'INV-2024-007',
    amount: 2500000,
    currency: 'MWK',
    status: 'paid',
    dueDate: '2024-10-31',
    paidDate: '2024-10-25',
    issueDate: '2024-10-01',
    description: 'Mobile Network License - Mobile virtual network operator license',
    paymentType: 'License Fee',
    clientName: 'MobileConnect Ltd',
    clientEmail: '<EMAIL>',
    paymentMethod: 'Bank Transfer',
    notes: 'MVNO license for mobile telecommunications services'
  }
];

const paymentTypes = [
  'All Types',
  'License Fee',
  'Procurement Fee',
  'Application Fee',
  'Renewal Fee',
  'Penalty Fee'
];

const CustomerPaymentsPage = () => {
  const { isAuthenticated} = useAuth();
  const router = useRouter();

  const [payments, setPayments] = useState<Payment[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Filter states
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  // Fetch payments data
  useEffect(() => {
    const fetchPayments = async () => {
      if (!isAuthenticated) return;

      try {
        setIsLoading(true);
        setError('');

        // In a real implementation, this would be an API call
        // const response = await customerApi.getPayments();
        
        // For now, use sample data
        setPayments(samplePayments);
        setFilteredPayments(samplePayments);

      } catch (err) {
        console.error('Error fetching payments:', err);
        setError('Failed to load payments. Please try refreshing the page.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPayments();
  }, [isAuthenticated]);

  // Apply filters
  useEffect(() => {
    let filtered = payments;

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter(payment => payment.status === statusFilter);
    }

    // Type filter
    if (typeFilter && typeFilter !== 'All Types') {
      filtered = filtered.filter(payment => payment.paymentType === typeFilter);
    }

    // Date filter
    if (dateFilter) {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter) {
        case 'last-30':
          filterDate.setDate(now.getDate() - 30);
          break;
        case 'last-90':
          filterDate.setDate(now.getDate() - 90);
          break;
        case 'last-year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          filterDate.setFullYear(1970); // Show all
      }
      
      filtered = filtered.filter(payment => 
        new Date(payment.issueDate) >= filterDate
      );
    }

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(payment =>
        payment.invoiceNumber.toLowerCase().includes(term) ||
        payment.clientName.toLowerCase().includes(term) ||
        payment.description.toLowerCase().includes(term) ||
        payment.paymentType.toLowerCase().includes(term)
      );
    }

    setFilteredPayments(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [payments, statusFilter, typeFilter, dateFilter, searchTerm]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'License Fee': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'Procurement Fee': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'Application Fee': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      case 'Renewal Fee': return 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400';
      case 'Penalty Fee': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatAmount = (amount: number, currency: string) => {
    return `${currency} ${amount.toLocaleString()}`;
  };

  // Pagination calculations
  const totalPages = Math.ceil(filteredPayments.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPayments = filteredPayments.slice(startIndex, endIndex);

  const clearFilters = () => {
    setStatusFilter('');
    setTypeFilter('');
    setDateFilter('');
    setSearchTerm('');
  };

  if (isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <Loader message="Loading payments..." />
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-6">
          <p>{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="mt-2 text-sm underline hover:no-underline"
          >
            Try again
          </button>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Payments</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                View all your payment records including license fees, procurement payments, and other transactions.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Total: {filteredPayments.length} payment{filteredPayments.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md mb-6">
          <div className="px-4 py-5 sm:p-6">
            {/* Search Bar */}
            <div className="mb-4">
              <label htmlFor="search" className="sr-only">Search payments</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i className="ri-search-line text-gray-400"></i>
                </div>
                <input
                  id="search"
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm text-gray-900 dark:text-gray-100"
                  placeholder="Search by invoice number, description, or payment type..."
                />
              </div>
            </div>

            {/* Filter Controls */}
            <div className="grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6">
              <div>
                <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                <select
                  id="status-filter"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                >
                  <option value="">All Statuses</option>
                  <option value="paid">Paid</option>
                  <option value="pending">Pending</option>
                  <option value="overdue">Overdue</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
              <div>
                <label htmlFor="type-filter" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Payment Type</label>
                <select
                  id="type-filter"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                >
                  {paymentTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="date-filter" className="block text-sm font-medium text-gray-700 dark:text-gray-300">Date Range</label>
                <select
                  id="date-filter"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                >
                  <option value="">All Time</option>
                  <option value="last-30">Last 30 Days</option>
                  <option value="last-90">Last 90 Days</option>
                  <option value="last-year">Last Year</option>
                </select>
              </div>
              <div className="flex items-end">
                <button
                  type="button"
                  onClick={clearFilters}
                  className="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-white bg-white dark:bg-gray-700 border border-primary rounded-full hover:bg-red-700 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Payments Table */}
        <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex flex-col">
              <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                  <div className="overflow-hidden border border-gray-200 dark:border-gray-700 sm:rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-900">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Invoice #
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Client
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Payment Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Description
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Issue Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Due Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Amount
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="relative px-6 py-3">
                            <span className="sr-only">Actions</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {currentPayments.length === 0 ? (
                          <tr>
                            <td colSpan={9} className="px-6 py-12 text-center">
                              <div className="text-gray-500 dark:text-gray-400">
                                <i className="ri-file-list-line text-4xl mb-4"></i>
                                <p className="text-lg font-medium">No payments found</p>
                                <p className="text-sm">Try adjusting your search criteria or filters.</p>
                              </div>
                            </td>
                          </tr>
                        ) : (
                          currentPayments.map((payment) => (
                            <tr key={payment.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{payment.invoiceNumber}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{payment.clientName}</div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400">{payment.clientEmail}</div>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeColor(payment.paymentType)}`}>
                                  {payment.paymentType}
                                </span>
                              </td>
                              <td className="px-6 py-4">
                                <div className="text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate" title={payment.description}>
                                  {payment.description}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900 dark:text-gray-100">{formatDate(payment.issueDate)}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900 dark:text-gray-100">{formatDate(payment.dueDate)}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  {formatAmount(payment.amount, payment.currency)}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(payment.status)}`}>
                                  {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button className="text-primary hover:text-primary-dark mr-3 transition-colors">
                                  View
                                </button>
                                <button className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors">
                                  Download
                                </button>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                      <span className="font-medium">{Math.min(endIndex, filteredPayments.length)}</span> of{' '}
                      <span className="font-medium">{filteredPayments.length}</span> payments
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Previous</span>
                        <i className="ri-arrow-left-s-line"></i>
                      </button>
                      
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }
                        
                        return (
                          <button
                            key={pageNum}
                            onClick={() => setCurrentPage(pageNum)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              currentPage === pageNum
                                ? 'z-10 bg-primary border-primary text-white'
                                : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                      
                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Next</span>
                        <i className="ri-arrow-right-s-line"></i>
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default CustomerPaymentsPage;