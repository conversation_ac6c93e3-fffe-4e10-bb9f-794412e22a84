-- Quick fix for the immediate foreign key constraint error
-- Run these commands one by one

-- Step 1: Check the problem
SELECT 
    COUNT(*) as problem_records,
    'audit_failures records with invalid user_id references' as issue
FROM audit_failures af
LEFT JOIN users u ON af.user_id = u.user_id
WHERE af.user_id IS NOT NULL AND u.user_id IS NULL;

-- Step 2: Fix the data (set invalid user_id to NULL)
UPDATE audit_failures 
SET user_id = NULL 
WHERE user_id IS NOT NULL 
AND user_id NOT IN (SELECT user_id FROM users);

-- Step 3: Verify the fix
SELECT 
    COUNT(*) as remaining_problem_records,
    'Should be 0 if fixed' as status
FROM audit_failures af
LEFT JOIN users u ON af.user_id = u.user_id
WHERE af.user_id IS NOT NULL AND u.user_id IS NULL;

-- Now restart your TypeORM application and the foreign key constraint should be created successfully