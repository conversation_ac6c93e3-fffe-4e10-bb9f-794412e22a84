# PowerShell script to run the database fix
Write-Host "🔧 Running Database Foreign Key Constraint Fix" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Get MySQL connection details
$mysqlHost = Read-Host "MySQL Host (default: localhost)"
if ([string]::IsNullOrEmpty($mysqlHost)) { $mysqlHost = "localhost" }

$mysqlPort = Read-Host "MySQL Port (default: 3306)"
if ([string]::IsNullOrEmpty($mysqlPort)) { $mysqlPort = "3306" }

$mysqlUser = Read-Host "MySQL Username"
$mysqlPassword = Read-Host "MySQL Password" -AsSecureString
$mysqlPasswordPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::Secure<PERSON>tringToBSTR($mysqlPassword))

$mysqlDatabase = Read-Host "Database Name (default: macra_db)"
if ([string]::IsNullOrEmpty($mysqlDatabase)) { $mysqlDatabase = "macra_db" }

Write-Host "`n🔄 Connecting to MySQL and running fix..." -ForegroundColor Yellow

try {
    # Run the SQL script
    $mysqlCmd = "mysql -h $mysqlHost -P $mysqlPort -u $mysqlUser -p$mysqlPasswordPlain $mysqlDatabase"
    Get-Content "URGENT-RUN-NOW.sql" | & mysql -h $mysqlHost -P $mysqlPort -u $mysqlUser -p$mysqlPasswordPlain $mysqlDatabase
    
    Write-Host "`n✅ Database fix completed successfully!" -ForegroundColor Green
    Write-Host "You can now restart your TypeORM application." -ForegroundColor Green
} catch {
    Write-Host "`n❌ Error occurred while running the fix:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host "Please check your connection details and try again." -ForegroundColor Yellow
}

Read-Host "`nPress Enter to exit"