"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserIdentification = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
const identification_type_entity_1 = require("./identification-type.entity");
let UserIdentification = class UserIdentification {
    identification_type_id;
    user_id;
    identification_value;
    created_at;
    updated_at;
    deleted_at;
    created_by;
    updated_by;
    identification_type;
    user;
    creator;
    updater;
};
exports.UserIdentification = UserIdentification;
__decorate([
    (0, typeorm_1.PrimaryColumn)('uuid'),
    __metadata("design:type", String)
], UserIdentification.prototype, "identification_type_id", void 0);
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', length: 36 }),
    __metadata("design:type", String)
], UserIdentification.prototype, "user_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], UserIdentification.prototype, "identification_value", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], UserIdentification.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], UserIdentification.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], UserIdentification.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], UserIdentification.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], UserIdentification.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => identification_type_entity_1.IdentificationType, (identificationType) => identificationType.user_identifications),
    (0, typeorm_1.JoinColumn)({ name: 'identification_type_id' }),
    __metadata("design:type", identification_type_entity_1.IdentificationType)
], UserIdentification.prototype, "identification_type", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.identifications),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], UserIdentification.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], UserIdentification.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], UserIdentification.prototype, "updater", void 0);
exports.UserIdentification = UserIdentification = __decorate([
    (0, typeorm_1.Entity)('user_identifications'),
    (0, typeorm_1.Unique)(['identification_type_id', 'identification_value'])
], UserIdentification);
//# sourceMappingURL=user-identification.entity.js.map