-- Comprehensive Database Foreign Key Fix Script
-- This script fixes all foreign key constraint issues by cleaning orphaned data
-- and standardizing column types

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- ==================================================
-- STEP 1: IDENTIFY ALL TABLES WITH FOREIGN KEY ISSUES
-- ==================================================

-- Check for orphaned records in each table
SELECT 'CHECKING ORPHANED RECORDS' as action;

-- Check audit_failures
SELECT 
    COUNT(*) as count,
    'audit_failures with invalid user_id' as table_issue
FROM audit_failures af
LEFT JOIN users u ON af.user_id = u.user_id
WHERE af.user_id IS NOT NULL AND u.user_id IS NULL;

-- Check audit_trail
SELECT 
    COUNT(*) as count,
    'audit_trail with invalid user_id' as table_issue
FROM audit_trail at
LEFT JOIN users u ON at.user_id = u.user_id
WHERE at.user_id IS NOT NULL AND u.user_id IS NULL;

-- Check notifications
SELECT 
    COUNT(*) as count,
    'notifications with invalid user_id' as table_issue
FROM notifications n
LEFT JOIN users u ON n.user_id = u.user_id
WHERE n.user_id IS NOT NULL AND u.user_id IS NULL;

-- Check consumer_affairs_complaints
SELECT 
    COUNT(*) as count,
    'consumer_affairs_complaints with invalid created_by' as table_issue
FROM consumer_affairs_complaints cac
LEFT JOIN users u ON cac.created_by = u.user_id
WHERE cac.created_by IS NOT NULL AND u.user_id IS NULL;

SELECT 
    COUNT(*) as count,
    'consumer_affairs_complaints with invalid updated_by' as table_issue
FROM consumer_affairs_complaints cac
LEFT JOIN users u ON cac.updated_by = u.user_id
WHERE cac.updated_by IS NOT NULL AND u.user_id IS NULL;

SELECT 
    COUNT(*) as count,
    'consumer_affairs_complaints with invalid complainant_id' as table_issue
FROM consumer_affairs_complaints cac
LEFT JOIN users u ON cac.complainant_id = u.user_id
WHERE cac.complainant_id IS NOT NULL AND u.user_id IS NULL;

SELECT 
    COUNT(*) as count,
    'consumer_affairs_complaints with invalid assigned_to' as table_issue
FROM consumer_affairs_complaints cac
LEFT JOIN users u ON cac.assigned_to = u.user_id
WHERE cac.assigned_to IS NOT NULL AND u.user_id IS NULL;

-- ==================================================
-- STEP 2: CLEAN UP ORPHANED RECORDS
-- ==================================================

SELECT 'CLEANING ORPHANED RECORDS' as action;

-- Clean audit_failures
UPDATE audit_failures 
SET user_id = NULL 
WHERE user_id IS NOT NULL 
AND user_id NOT IN (SELECT user_id FROM users);

-- Clean audit_trail
UPDATE audit_trail 
SET user_id = NULL 
WHERE user_id IS NOT NULL 
AND user_id NOT IN (SELECT user_id FROM users);

-- Clean notifications (delete orphaned records as they're not useful without a user)
DELETE FROM notifications 
WHERE user_id IS NOT NULL 
AND user_id NOT IN (SELECT user_id FROM users);

-- Clean consumer_affairs_complaints
UPDATE consumer_affairs_complaints 
SET created_by = NULL 
WHERE created_by IS NOT NULL 
AND created_by NOT IN (SELECT user_id FROM users);

UPDATE consumer_affairs_complaints 
SET updated_by = NULL 
WHERE updated_by IS NOT NULL 
AND updated_by NOT IN (SELECT user_id FROM users);

UPDATE consumer_affairs_complaints 
SET assigned_to = NULL 
WHERE assigned_to IS NOT NULL 
AND assigned_to NOT IN (SELECT user_id FROM users);

-- For complainant_id, we need to be more careful as it's a required field
-- Let's check if there are any orphaned complainant_id records
DELETE FROM consumer_affairs_complaints 
WHERE complainant_id IS NOT NULL 
AND complainant_id NOT IN (SELECT user_id FROM users);

-- Clean complaint attachments
DELETE FROM consumer_affairs_complaint_attachments 
WHERE uploaded_by IS NOT NULL 
AND uploaded_by NOT IN (SELECT user_id FROM users);

DELETE FROM consumer_affairs_complaint_attachments 
WHERE complaint_id IS NOT NULL 
AND complaint_id NOT IN (SELECT complaint_id FROM consumer_affairs_complaints);

-- Clean complaint status history
DELETE FROM consumer_affairs_complaint_status_history 
WHERE created_by IS NOT NULL 
AND created_by NOT IN (SELECT user_id FROM users);

DELETE FROM consumer_affairs_complaint_status_history 
WHERE complaint_id IS NOT NULL 
AND complaint_id NOT IN (SELECT complaint_id FROM consumer_affairs_complaints);

-- ==================================================
-- STEP 3: STANDARDIZE COLUMN TYPES
-- ==================================================

SELECT 'STANDARDIZING COLUMN TYPES' as action;

-- Fix audit_failures
ALTER TABLE `audit_failures` 
MODIFY COLUMN `user_id` varchar(36) NULL;

-- Fix audit_trail
ALTER TABLE `audit_trail` 
MODIFY COLUMN `user_id` varchar(36) NULL;

-- Fix notifications
ALTER TABLE `notifications` 
MODIFY COLUMN `user_id` varchar(36) NOT NULL;

-- Fix user_identifications
ALTER TABLE `user_identifications` 
MODIFY COLUMN `user_id` varchar(36) NOT NULL;

-- Fix employees
ALTER TABLE `employees` 
MODIFY COLUMN `user_id` varchar(36) NOT NULL;

-- Fix consumer_affairs_complaints
ALTER TABLE `consumer_affairs_complaints` 
MODIFY COLUMN `complainant_id` varchar(36) NOT NULL;

ALTER TABLE `consumer_affairs_complaints` 
MODIFY COLUMN `assigned_to` varchar(36) NULL;

ALTER TABLE `consumer_affairs_complaints` 
MODIFY COLUMN `created_by` varchar(36) NULL;

ALTER TABLE `consumer_affairs_complaints` 
MODIFY COLUMN `updated_by` varchar(36) NULL;

-- Fix consumer_affairs_complaint_attachments
ALTER TABLE `consumer_affairs_complaint_attachments` 
MODIFY COLUMN `complaint_id` varchar(36) NOT NULL;

ALTER TABLE `consumer_affairs_complaint_attachments` 
MODIFY COLUMN `uploaded_by` varchar(36) NOT NULL;

-- Fix consumer_affairs_complaint_status_history
ALTER TABLE `consumer_affairs_complaint_status_history` 
MODIFY COLUMN `complaint_id` varchar(36) NOT NULL;

ALTER TABLE `consumer_affairs_complaint_status_history` 
MODIFY COLUMN `created_by` varchar(36) NOT NULL;

-- ==================================================
-- STEP 4: RE-CREATE FOREIGN KEY CONSTRAINTS
-- ==================================================

SELECT 'CREATING FOREIGN KEY CONSTRAINTS' as action;

-- Drop existing constraints that might exist
ALTER TABLE `audit_failures` DROP FOREIGN KEY IF EXISTS `FK_audit_failures_user`;
ALTER TABLE `audit_trail` DROP FOREIGN KEY IF EXISTS `FK_audit_trail_user`;
ALTER TABLE `notifications` DROP FOREIGN KEY IF EXISTS `FK_notifications_user`;
ALTER TABLE `user_identifications` DROP FOREIGN KEY IF EXISTS `FK_user_identifications_user`;
ALTER TABLE `employees` DROP FOREIGN KEY IF EXISTS `FK_employees_user`;
ALTER TABLE `consumer_affairs_complaints` DROP FOREIGN KEY IF EXISTS `FK_consumer_affairs_complaints_complainant`;
ALTER TABLE `consumer_affairs_complaints` DROP FOREIGN KEY IF EXISTS `FK_consumer_affairs_complaints_assigned`;
ALTER TABLE `consumer_affairs_complaints` DROP FOREIGN KEY IF EXISTS `FK_consumer_affairs_complaints_created_by`;
ALTER TABLE `consumer_affairs_complaints` DROP FOREIGN KEY IF EXISTS `FK_consumer_affairs_complaints_updated_by`;
ALTER TABLE `consumer_affairs_complaint_attachments` DROP FOREIGN KEY IF EXISTS `FK_complaint_attachments_complaint`;
ALTER TABLE `consumer_affairs_complaint_attachments` DROP FOREIGN KEY IF EXISTS `FK_complaint_attachments_uploader`;
ALTER TABLE `consumer_affairs_complaint_status_history` DROP FOREIGN KEY IF EXISTS `FK_complaint_history_complaint`;
ALTER TABLE `consumer_affairs_complaint_status_history` DROP FOREIGN KEY IF EXISTS `FK_complaint_history_creator`;

-- Create new foreign key constraints

-- audit_failures
ALTER TABLE `audit_failures` 
ADD CONSTRAINT `FK_audit_failures_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- audit_trail
ALTER TABLE `audit_trail` 
ADD CONSTRAINT `FK_audit_trail_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- notifications
ALTER TABLE `notifications` 
ADD CONSTRAINT `FK_notifications_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- user_identifications
ALTER TABLE `user_identifications` 
ADD CONSTRAINT `FK_user_identifications_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- employees
ALTER TABLE `employees` 
ADD CONSTRAINT `FK_employees_user` 
FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- consumer_affairs_complaints
ALTER TABLE `consumer_affairs_complaints` 
ADD CONSTRAINT `FK_consumer_affairs_complaints_complainant` 
FOREIGN KEY (`complainant_id`) REFERENCES `users`(`user_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `consumer_affairs_complaints` 
ADD CONSTRAINT `FK_consumer_affairs_complaints_assigned` 
FOREIGN KEY (`assigned_to`) REFERENCES `users`(`user_id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `consumer_affairs_complaints` 
ADD CONSTRAINT `FK_consumer_affairs_complaints_created_by` 
FOREIGN KEY (`created_by`) REFERENCES `users`(`user_id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `consumer_affairs_complaints` 
ADD CONSTRAINT `FK_consumer_affairs_complaints_updated_by` 
FOREIGN KEY (`updated_by`) REFERENCES `users`(`user_id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- consumer_affairs_complaint_attachments
ALTER TABLE `consumer_affairs_complaint_attachments` 
ADD CONSTRAINT `FK_complaint_attachments_complaint` 
FOREIGN KEY (`complaint_id`) REFERENCES `consumer_affairs_complaints`(`complaint_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `consumer_affairs_complaint_attachments` 
ADD CONSTRAINT `FK_complaint_attachments_uploader` 
FOREIGN KEY (`uploaded_by`) REFERENCES `users`(`user_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- consumer_affairs_complaint_status_history
ALTER TABLE `consumer_affairs_complaint_status_history` 
ADD CONSTRAINT `FK_complaint_history_complaint` 
FOREIGN KEY (`complaint_id`) REFERENCES `consumer_affairs_complaints`(`complaint_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `consumer_affairs_complaint_status_history` 
ADD CONSTRAINT `FK_complaint_history_creator` 
FOREIGN KEY (`created_by`) REFERENCES `users`(`user_id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- ==================================================
-- STEP 5: RE-ENABLE FOREIGN KEY CHECKS AND VERIFY
-- ==================================================

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify that all orphaned records are cleaned up
SELECT 'VERIFICATION RESULTS' as status;

SELECT 
    COUNT(*) as remaining_issues,
    'audit_failures with invalid user_id' as table_check
FROM audit_failures af
LEFT JOIN users u ON af.user_id = u.user_id
WHERE af.user_id IS NOT NULL AND u.user_id IS NULL;

SELECT 
    COUNT(*) as remaining_issues,
    'consumer_affairs_complaints with invalid created_by' as table_check
FROM consumer_affairs_complaints cac
LEFT JOIN users u ON cac.created_by = u.user_id
WHERE cac.created_by IS NOT NULL AND u.user_id IS NULL;

SELECT 'Database schema fix completed successfully!' as message;