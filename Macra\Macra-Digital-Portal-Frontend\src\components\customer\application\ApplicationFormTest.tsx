'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { applicationService } from '@/services/applicationService';
import { applicationFormDataService } from '@/services/applicationFormDataService';

// Import form components from the correct locations
// Some components are in /steps/, others are in /applications/
import ApplicantInfo from '@/components/customer/application/steps/ApplicantInfo';
import CompanyProfile from '@/components/customer/application/steps/CompanyProfile';
import BusinessInfo from '@/components/customer/application/steps/BusinessInfo';
import ServiceScope from '@/components/customer/application/steps/ServiceScope';
import BusinessPlan from '@/components/customer/application/steps/BusinessPlan';
import LegalHistory from '@/components/customer/application/steps/LegalHistory';
import ReviewSubmit from '@/components/customer/application/steps/ReviewSubmit';

// These components are only available in /applications/
import { Management, ProfessionalServices } from '@/components/applications';

// Import types from the applications index
import { type ApplicationFormData } from '@/components/applications';

interface ApplicationFormProps {
  licenseType: string;
  licenseCategory?: string;
  licenseTypeId?: string;
  licenseCategoryId?: string;
  applicationId?: string;
  onSubmit: (formData: any) => Promise<void>;
}

const ApplicationForm: React.FC<ApplicationFormProps> = ({
  licenseType,
  licenseCategory,
  licenseTypeId,
  licenseCategoryId,
  applicationId: initialApplicationId,
  onSubmit
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();

  // Get continue parameters from URL
  const continueApplicationId = searchParams.get('continue');
  const continueStep = searchParams.get('step');
  const isContinuing = !!continueApplicationId;

  const [currentStep, setCurrentStep] = useState(() => {
    // If continuing an application, start at the specified step (convert to 0-based index)
    if (isContinuing && continueStep) {
      const step = parseInt(continueStep, 10) - 1; // Convert to 0-based index
      return step >= 0 && step < 9 ? step : 0; // Ensure valid range
    }
    return 0;
  });

  const [applicationId, setApplicationId] = useState<string | null>(
    continueApplicationId || initialApplicationId || null
  );

  // Form data state - using proper ApplicationFormData type
  const [formData, setFormData] = useState<ApplicationFormData>({
    applicantInfo: {
      applicantName: '',
      postalPoBox: '',
      postalCity: '',
      postalCountry: '',
      physicalStreet: '',
      physicalCity: '',
      physicalCountry: '',
      telephone: '',
      fax: '',
      email: ''
    },
    companyProfile: {
      shareholders: [],
      directors: [],
      foreignOwnership: '',
      businessRegistrationNo: '',
      tpin: '',
      website: '',
      dateOfIncorporation: '',
      placeOfIncorporation: ''
    },
    management: {
      managementTeam: [],
      organizationalStructure: '',
      keyPersonnel: ''
    },
    professionalServices: {
      consultants: '',
      serviceProviders: '',
      technicalSupport: '',
      maintenanceArrangements: ''
    },
    businessInfo: {
      businessDescription: '',
      targetMarket: '',
      competitiveAdvantage: '',
      operationalPlan: ''
    },
    serviceScope: {
      servicesOffered: '',
      geographicCoverage: '',
      customerSegments: '',
      pricingStrategy: ''
    },
    businessPlan: {
      marketAnalysis: '',
      financialProjections: '',
      competitiveAdvantage: '',
      riskAssessment: '',
      implementationTimeline: ''
    },
    legalHistory: {
      previousViolations: '',
      courtCases: '',
      regulatoryHistory: '',
      complianceRecord: ''
    }
  });

  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataLoadError, setDataLoadError] = useState<string | null>(null);

  const steps = [
    'Applicant Information',
    'Company Profile',
    'Management',
    'Professional Services',
    'Business Information',
    'Service Scope',
    'Business Plan',
    'Legal History',
    'Review & Submit'
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Load existing application data when continuing
  useEffect(() => {
    const loadExistingApplicationData = async () => {
      if (isContinuing && continueApplicationId) {
        setIsLoadingData(true);
        setDataLoadError(null);

        try {
          console.log(`Loading existing application data for ID: ${continueApplicationId}`);

          // Fetch the existing application basic info
          const existingApplication = await applicationService.getApplication(continueApplicationId);

          if (existingApplication) {
            console.log('Loaded existing application:', existingApplication);

            // Update current step if different from URL
            if (existingApplication.current_step && existingApplication.current_step !== currentStep + 1) {
              console.log(`Updating step from ${currentStep + 1} to ${existingApplication.current_step}`);
              setCurrentStep(existingApplication.current_step - 1); // Convert to 0-based
            }
          }

          // Fetch the form data separately using applicationFormDataService
          console.log('Loading form data using applicationFormDataService...');

          try {
            const existingFormData = await applicationFormDataService.getApplicationFormData(continueApplicationId);

            if (existingFormData && Object.keys(existingFormData).length > 0) {
              console.log('✅ Loaded existing form data:', existingFormData);

              // Merge existing data with the initial form structure
              setFormData(prevData => {
                const mergedData = { ...prevData };

                // Merge each section if it exists in the loaded data
                Object.keys(existingFormData).forEach(sectionName => {
                  if (mergedData[sectionName as keyof ApplicationFormData]) {
                    console.log(`Merging section: ${sectionName}`, existingFormData[sectionName]);
                    mergedData[sectionName as keyof ApplicationFormData] = {
                      ...mergedData[sectionName as keyof ApplicationFormData],
                      ...existingFormData[sectionName]
                    };
                  } else {
                    console.warn(`Section ${sectionName} not found in form structure`);
                  }
                });

                console.log('Final merged form data:', mergedData);
                return mergedData;
              });

              console.log('✅ Form data populated and merged successfully');
            } else {
              console.log('ℹ️ No existing form data found, using initial form structure');
            }
          } catch (formDataError) {
            console.error('❌ Error loading form data:', formDataError);

            // Check if it's a 404 error (no data exists yet)
            if ((formDataError as any)?.response?.status === 404) {
              console.log('ℹ️ No form data exists yet for this application (404). This is normal for new applications.');
            } else {
              console.error('Unexpected error loading form data:', formDataError);
              setDataLoadError('Failed to load form data');
            }
          }
        } catch (error) {
          console.error('Error loading existing application data:', error);
          setDataLoadError('Failed to load application data');
        } finally {
          setIsLoadingData(false);
        }
      }
    };

    loadExistingApplicationData();
  }, [isContinuing, continueApplicationId, currentStep]);

  // Function to save current step data
  const saveCurrentStepData = async (stepData: any) => {
    if (!applicationId) {
      console.warn('No application ID available for saving');
      return;
    }

    try {
      const stepNames = [
        'applicantInfo',
        'companyProfile',
        'management',
        'professionalServices',
        'businessInfo',
        'serviceScope',
        'businessPlan',
        'legalHistory',
        'reviewSubmit'
      ];

      const sectionName = stepNames[currentStep];
      if (!sectionName) {
        console.warn('Invalid step for saving:', currentStep);
        return;
      }

      console.log(`Saving step ${currentStep + 1} (${sectionName}) data:`, stepData);

      await applicationFormDataService.saveOrUpdateFormSection(
        applicationId,
        sectionName,
        stepData
      );

      // Update local form data
      setFormData(prev => ({
        ...prev,
        [sectionName]: stepData
      }));

      console.log(`Step ${currentStep + 1} data saved successfully`);
    } catch (error) {
      console.error('Error saving step data:', error);
    }
  };

  // Function to handle form data updates from child components
  const handleFormDataUpdate = (sectionName: keyof ApplicationFormData, sectionData: any) => {
    setFormData(prev => ({
      ...prev,
      [sectionName]: {
        ...prev[sectionName],
        ...sectionData
      }
    }));
  };

  // Function to handle field changes from step components
  const handleFieldChange = (field: string, value: any) => {
    // Update the current step's data
    const stepNames = [
      'applicantInfo', 'companyProfile', 'management', 'professionalServices',
      'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'
    ];

    const currentSectionName = stepNames[currentStep] as keyof ApplicationFormData;

    setFormData(prev => ({
      ...prev,
      [currentSectionName]: {
        ...prev[currentSectionName],
        [field]: value
      }
    }));
  };

  // Function to render the current step component
  const renderCurrentStep = () => {
    const stepNames = [
      'applicantInfo', 'companyProfile', 'management', 'professionalServices',
      'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'
    ];

    const currentSectionName = stepNames[currentStep] as keyof ApplicationFormData;
    const currentSectionData = formData[currentSectionName];

    // Props for /steps/ components (ApplicantInfo, CompanyProfile, BusinessInfo, etc.)
    const stepsProps = {
      formData: currentSectionData,
      onChange: handleFieldChange,
      onSave: saveCurrentStepData,
      errors: {},
      licenseTypeId: licenseTypeId,
      licenseCategoryId: licenseCategoryId,
      applicationId: applicationId || undefined,
      isLoading: false
    };

    // Props for /applications/ components (Management, ProfessionalServices)
    const applicationsProps = {
      data: currentSectionData,
      onChange: (data: any) => handleFormDataUpdate(currentSectionName, data),
      errors: {},
      disabled: false
    };

    switch (currentStep) {
      case 0: // ApplicantInfo - from /steps/
        return <ApplicantInfo {...stepsProps} />;
      case 1: // CompanyProfile - from /steps/
        return <CompanyProfile {...stepsProps} />;
      case 2: // Management - from /applications/
        return <Management {...applicationsProps} />;
      case 3: // ProfessionalServices - from /applications/
        return <ProfessionalServices {...applicationsProps} />;
      case 4: // BusinessInfo - from /steps/
        return <BusinessInfo {...stepsProps} />;
      case 5: // ServiceScope - from /steps/
        return <ServiceScope {...stepsProps} />;
      case 6: // BusinessPlan - from /steps/
        return <BusinessPlan {...stepsProps} />;
      case 7: // LegalHistory - from /steps/
        return <LegalHistory {...stepsProps} />;
      case 8: // ReviewSubmit - from /steps/
        return (
          <ReviewSubmit
            formData={formData}
            onChange={handleFieldChange}
            onSave={saveCurrentStepData}
            errors={{}}
            applicationId={applicationId || undefined}
            isLoading={false}
          />
        );
      default:
        return <div>Invalid step</div>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Continue Application Indicator */}
      {isContinuing && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center">
            <i className="ri-play-circle-line text-green-600 dark:text-green-400 text-lg mr-3"></i>
            <div>
              <h3 className="text-sm font-medium text-green-900 dark:text-green-100">
                Continuing Application
              </h3>
              <p className="text-green-700 dark:text-green-300 text-sm mt-1">
                You are continuing your application from Step {currentStep + 1}. Your previous progress has been saved.
                {applicationId && (
                  <span className="ml-2">
                    Application ID: {applicationId.slice(0, 8)}...
                  </span>
                )}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Step Navigation */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 mb-4">
          {steps.map((step, index) => (
            <button
              key={index}
              type="button"
              onClick={() => setCurrentStep(index)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                index === currentStep
                  ? 'bg-primary text-white shadow-md'
                  : index < currentStep
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {index < currentStep && <i className="ri-check-line mr-1"></i>}
              {step}
            </button>
          ))}
        </div>

        {/* Current Step Info */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {steps[currentStep]}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Step {currentStep + 1} of {steps.length}
            {applicationId && (
              <span className="ml-4">
                Application ID: {applicationId.slice(0, 8)}...
              </span>
            )}
          </p>
        </div>
      </div>

      {/* Form Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6">
          {/* Loading State */}
          {isLoadingData ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">
                Loading your saved application data...
              </p>
            </div>
          ) : dataLoadError ? (
            <div className="text-center py-12">
              <div className="text-red-600 dark:text-red-400 mb-4">
                <i className="ri-error-warning-line text-4xl"></i>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Error Loading Application Data
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {dataLoadError}
              </p>
              <button
                type="button"
                onClick={() => window.location.reload()}
                className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors"
              >
                Retry
              </button>
            </div>
          ) : (
            <div>
              {/* Render the current step component */}
              {renderCurrentStep()}

              {/* Debug information - only show in development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    🔧 Debug Information
                  </h4>
                  <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                    <p>Current Step: {currentStep + 1} ({steps[currentStep]})</p>
                    <p>License Type: {licenseType}</p>
                    <p>License Category: {licenseCategory}</p>
                    {isContinuing && <p>Continuing Application: {applicationId}</p>}
                    <p>Form Data Sections: {Object.keys(formData).filter(key =>
                      formData[key as keyof ApplicationFormData] &&
                      Object.keys(formData[key as keyof ApplicationFormData]).some(k =>
                        formData[key as keyof ApplicationFormData][k as any] !== '' &&
                        formData[key as keyof ApplicationFormData][k as any] !== null &&
                        formData[key as keyof ApplicationFormData][k as any] !== undefined
                      )
                    ).join(', ') || 'None'}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApplicationForm;
