'use client';

import React from 'react';
import { TextArea } from '@/components/forms';
import { LegalHistoryData, ApplicationFormComponentProps } from './index';

interface LegalHistoryProps extends ApplicationFormComponentProps {
  data: LegalHistoryData;
  onChange: (data: LegalHistoryData) => void;
}

const LegalHistory: React.FC<LegalHistoryProps> = ({
  data,
  onChange,
  errors = {},
  disabled = false
}) => {
  const handleInputChange = (field: keyof LegalHistoryData, value: string) => {
    onChange({
      ...data,
      [field]: value
    });
  };

  return (
    <div className="space-y-8">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Legal History and Compliance
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide complete information about legal history, compliance record, and any regulatory issues
        </p>
      </div>

      {/* Previous Violations */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Previous Violations
        </h3>
        
        <TextArea
          label="Previous Regulatory Violations"
          value={data.previousViolations}
          onChange={(e) => handleInputChange('previousViolations', e.target.value)}
          placeholder="List any previous regulatory violations, penalties, or sanctions. If none, please state 'None' or 'Not Applicable'"
          rows={5}
          required
          disabled={disabled}
          error={errors.previousViolations}
          helperText="Include details of violations, dates, penalties imposed, and corrective actions taken"
        />
      </div>

      {/* Court Cases */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Legal Proceedings
        </h3>
        
        <TextArea
          label="Court Cases and Legal Proceedings"
          value={data.courtCases}
          onChange={(e) => handleInputChange('courtCases', e.target.value)}
          placeholder="Describe any ongoing or past court cases, legal disputes, or proceedings involving the company or key personnel. If none, please state 'None'"
          rows={5}
          required
          disabled={disabled}
          error={errors.courtCases}
          helperText="Include case details, current status, outcomes, and any impact on business operations"
        />
      </div>

      {/* Regulatory History */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Regulatory History
        </h3>
        
        <TextArea
          label="Regulatory History and Interactions"
          value={data.regulatoryHistory}
          onChange={(e) => handleInputChange('regulatoryHistory', e.target.value)}
          placeholder="Provide details of your history with regulatory bodies, including previous licenses, applications, and regulatory interactions"
          rows={6}
          required
          disabled={disabled}
          error={errors.regulatoryHistory}
          helperText="Include previous licenses held, application outcomes, regulatory inspections, and compliance assessments"
        />
      </div>

      {/* Compliance Record */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Compliance Record
        </h3>
        
        <TextArea
          label="Compliance Record and Measures"
          value={data.complianceRecord}
          onChange={(e) => handleInputChange('complianceRecord', e.target.value)}
          placeholder="Describe your compliance record, internal compliance measures, and commitment to regulatory adherence"
          rows={6}
          required
          disabled={disabled}
          error={errors.complianceRecord}
          helperText="Include compliance policies, training programs, monitoring systems, and commitment to ongoing compliance"
        />
      </div>

      {/* Legal History Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800">
          <div className="flex items-start">
            <i className="ri-alert-line text-red-600 dark:text-red-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-red-900 dark:text-red-100 mb-1">
                Full Disclosure Required
              </h4>
              <p className="text-red-700 dark:text-red-300 text-sm">
                Complete and honest disclosure of all legal issues is mandatory. 
                Failure to disclose may result in application rejection.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
          <div className="flex items-start">
            <i className="ri-shield-check-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">
                Compliance Commitment
              </h4>
              <p className="text-green-700 dark:text-green-300 text-sm">
                Demonstrate your commitment to regulatory compliance and 
                continuous improvement in compliance practices.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Declaration Section */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Legal Declaration
        </h4>
        <div className="space-y-4">
          <div className="flex items-start">
            <i className="ri-checkbox-line text-primary text-lg mr-3 mt-0.5"></i>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              I hereby declare that all information provided in this legal history section is true, 
              complete, and accurate to the best of my knowledge.
            </p>
          </div>
          <div className="flex items-start">
            <i className="ri-checkbox-line text-primary text-lg mr-3 mt-0.5"></i>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              I understand that providing false or misleading information may result in the 
              rejection of this application or revocation of any license granted.
            </p>
          </div>
          <div className="flex items-start">
            <i className="ri-checkbox-line text-primary text-lg mr-3 mt-0.5"></i>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              I commit to maintaining compliance with all applicable laws and regulations 
              if this license is granted.
            </p>
          </div>
        </div>
      </div>

      {/* Information Notice */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <div className="flex items-start">
          <i className="ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"></i>
          <div>
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              Legal History Guidelines
            </h4>
            <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
              <li>• Provide complete and honest disclosure of all legal matters</li>
              <li>• Include details of any violations, even if resolved or minor</li>
              <li>• Explain circumstances and corrective actions taken</li>
              <li>• If no legal issues exist, clearly state "None" or "Not Applicable"</li>
              <li>• Include legal matters involving key personnel and directors</li>
              <li>• Demonstrate commitment to future compliance</li>
              <li>• Attach supporting documentation where relevant</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LegalHistory;
