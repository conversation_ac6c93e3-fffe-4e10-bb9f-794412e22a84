import { User } from './user.entity';
import { Role } from './role.entity';
export declare const PERMISSION_NAMES: {
    readonly USER_CREATE: "user:create";
    readonly USER_READ: "user:read";
    readonly USER_UPDATE: "user:update";
    readonly USER_DELETE: "user:delete";
    readonly ROLE_CREATE: "role:create";
    readonly ROLE_READ: "role:read";
    readonly ROLE_UPDATE: "role:update";
    readonly ROLE_DELETE: "role:delete";
    readonly PERMISSION_CREATE: "permission:create";
    readonly PERMISSION_READ: "permission:read";
    readonly PERMISSION_UPDATE: "permission:update";
    readonly PERMISSION_DELETE: "permission:delete";
    readonly LICENSE_CREATE: "license:create";
    readonly LICENSE_READ: "license:read";
    readonly LICENSE_UPDATE: "license:update";
    readonly LICENSE_DELETE: "license:delete";
    readonly LICENSE_APPROVE: "license:approve";
    readonly LICENSE_REJECT: "license:reject";
    readonly LICENSE_TYPE_CREATE: "license_type:create";
    readonly LICENSE_TYPE_READ: "license_type:read";
    readonly LICENSE_TYPE_UPDATE: "license_type:update";
    readonly LICENSE_TYPE_DELETE: "license_type:delete";
    readonly LICENSE_CATEGORY_CREATE: "license_category:create";
    readonly LICENSE_CATEGORY_READ: "license_category:read";
    readonly LICENSE_CATEGORY_UPDATE: "license_category:update";
    readonly LICENSE_CATEGORY_DELETE: "license_category:delete";
    readonly APPLICATION_CREATE: "application:create";
    readonly APPLICATION_READ: "application:read";
    readonly APPLICATION_UPDATE: "application:update";
    readonly APPLICATION_DELETE: "application:delete";
    readonly APPLICATION_EVALUATE: "application:evaluate";
    readonly APPLICATION_SUBMIT: "application:submit";
    readonly APPLICATION_APPROVE: "application:approve";
    readonly APPLICATION_REJECT: "application:reject";
    readonly FINANCIAL_READ: "financial:read";
    readonly FINANCIAL_UPDATE: "financial:update";
    readonly FINANCIAL_REPORTS: "financial:reports";
    readonly INVOICE_CREATE: "invoice:create";
    readonly INVOICE_READ: "invoice:read";
    readonly INVOICE_UPDATE: "invoice:update";
    readonly INVOICE_DELETE: "invoice:delete";
    readonly PAYMENT_CREATE: "payment:create";
    readonly PAYMENT_READ: "payment:read";
    readonly PAYMENT_UPDATE: "payment:update";
    readonly PAYMENT_DELETE: "payment:delete";
    readonly DOCUMENT_CREATE: "document:create";
    readonly DOCUMENT_READ: "document:read";
    readonly DOCUMENT_UPDATE: "document:update";
    readonly DOCUMENT_DELETE: "document:delete";
    readonly DOCUMENT_DOWNLOAD: "document:download";
    readonly IDENTIFICATION_TYPE_CREATE: "identification_type:create";
    readonly IDENTIFICATION_TYPE_READ: "identification_type:read";
    readonly IDENTIFICATION_TYPE_UPDATE: "identification_type:update";
    readonly IDENTIFICATION_TYPE_DELETE: "identification_type:delete";
    readonly CONTACT_CREATE: "contact:create";
    readonly CONTACT_READ: "contact:read";
    readonly CONTACT_UPDATE: "contact:update";
    readonly CONTACT_DELETE: "contact:delete";
    readonly APPLICANT_CREATE: "applicant:create";
    readonly APPLICANT_READ: "applicant:read";
    readonly APPLICANT_UPDATE: "applicant:update";
    readonly APPLICANT_DELETE: "applicant:delete";
    readonly EVALUATION_CREATE: "evaluation:create";
    readonly EVALUATION_READ: "evaluation:read";
    readonly EVALUATION_UPDATE: "evaluation:update";
    readonly EVALUATION_DELETE: "evaluation:delete";
    readonly EVALUATION_SUBMIT: "evaluation:submit";
    readonly NOTIFICATION_CREATE: "notification:create";
    readonly NOTIFICATION_READ: "notification:read";
    readonly NOTIFICATION_UPDATE: "notification:update";
    readonly NOTIFICATION_DELETE: "notification:delete";
    readonly NOTIFICATION_SEND: "notification:send";
    readonly SYSTEM_SETTINGS: "system:settings";
    readonly SYSTEM_AUDIT: "system:audit";
    readonly SYSTEM_BACKUP: "system:backup";
    readonly SYSTEM_MAINTENANCE: "system:maintenance";
    readonly REPORT_GENERATE: "report:generate";
    readonly REPORT_VIEW: "report:view";
    readonly REPORT_EXPORT: "report:export";
    readonly REPORT_SCHEDULE: "report:schedule";
};
export declare class Permission {
    permission_id: string;
    name: string;
    description: string;
    category: string;
    created_at: Date;
    updated_at: Date;
    deleted_at?: Date;
    created_by?: string;
    updated_by?: string;
    creator?: User;
    updater?: User;
    roles: Role[];
    generateId(): void;
}
