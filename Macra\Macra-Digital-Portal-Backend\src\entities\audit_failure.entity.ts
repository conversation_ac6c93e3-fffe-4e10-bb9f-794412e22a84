import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';

@Entity('audit_failures')
export class AuditFailure {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  failure_id: string;

  @Column({ type: 'varchar', length: 255 })
  action: string;

  @Column({ type: 'varchar', length: 255 })
  module: string;

  @Column({ type: 'varchar', length: 255 })
  resource_type: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  resource_id: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'json', nullable: true })
  old_values: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  new_values: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'varchar', length: 45, nullable: true })
  ip_address: string;

  @Column({ type: 'text', nullable: true })
  user_agent: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  session_id: string;

  @Column({ type: 'text', nullable: true })
  error_message: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'varchar', length: 36, nullable: true })
  user_id: string;

  @CreateDateColumn()
  created_at: Date;

  @BeforeInsert()
  generateId() {
    if (!this.failure_id) {
      this.failure_id = uuidv4();
    }
  }
}
