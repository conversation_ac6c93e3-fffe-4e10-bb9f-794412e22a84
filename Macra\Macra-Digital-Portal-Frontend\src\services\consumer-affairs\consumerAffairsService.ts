import { apiClient } from '@/lib/apiClient';
import { documentService } from '../documentService';

export interface ConsumerAffairsComplaint {
  complaint_id: string;
  title: string;
  description: string;
  category: string;
  status: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
}

export interface CreateConsumerAffairsComplaintData {
  title: string;
  description: string;
  category: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  attachments?: File[];
}

export interface ConsumerAffairsComplaintFilter {
  status?: string;
  category?: string;
  priority?: string;
  search?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

export interface ConsumerAffairsComplaintListResponse {
  success: boolean;
  message: string;
  data: ConsumerAffairsComplaint[];
  total: number;
  page: number;
  limit: number;
}

export interface ConsumerAffairsComplaintResponse {
  success: boolean;
  message: string;
  data: ConsumerAffairsComplaint;
}

class ConsumerAffairsService {
  private baseUrl = '/consumer-affairs-complaints';

  async createComplaint(data: CreateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaintResponse> {
    try {
      console.log('🔄 Creating consumer affairs complaint:', {
        title: data.title,
        category: data.category,
        hasAttachments: data.attachments && data.attachments.length > 0
      });

      const formData = new FormData();
      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('category', data.category);
      
      if (data.priority) {
        formData.append('priority', data.priority);
      }

      // Add attachments if provided
      if (data.attachments && data.attachments.length > 0) {
        data.attachments.forEach((file, index) => {
          formData.append('attachments', file);
        });
      }

      const response = await apiClient.post(this.baseUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Consumer affairs complaint created successfully:', response.data);

      // Upload documents to polymorphic document table if complaint was created successfully
      if (response.data.success && data.attachments && data.attachments.length > 0) {
        try {
          const complaintId = response.data.data.complaint_id;
          await documentService.uploadMultipleDocuments(
            data.attachments,
            'consumer_affairs_complaint',
            complaintId,
            'COMPLAINT_ATTACHMENT',
            false
          );
          console.log('✅ Complaint attachments uploaded to document table');
        } catch (docError) {
          console.warn('⚠️ Failed to upload documents to document table:', docError);
          // Don't fail the complaint creation if document upload fails
        }
      }

      return response.data;
    } catch (error) {
      console.error('❌ Error creating consumer affairs complaint:', error);
      throw error;
    }
  }

  async getComplaints(filter: ConsumerAffairsComplaintFilter = {}): Promise<ConsumerAffairsComplaintListResponse> {
    try {
      console.log('🔄 Fetching consumer affairs complaints with filter:', filter);

      const params = new URLSearchParams();
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`${this.baseUrl}?${params.toString()}`);

      console.log('✅ Consumer affairs complaints fetched successfully:', {
        total: response.data.total,
        count: response.data.data?.length || 0
      });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching consumer affairs complaints:', error);
      throw error;
    }
  }

  async getComplaint(complaintId: string): Promise<ConsumerAffairsComplaintResponse> {
    try {
      console.log('🔄 Fetching consumer affairs complaint:', complaintId);

      const response = await apiClient.get(`${this.baseUrl}/${complaintId}`);

      console.log('✅ Consumer affairs complaint fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching consumer affairs complaint:', error);
      throw error;
    }
  }

  async updateComplaint(
    complaintId: string,
    updateData: Partial<CreateConsumerAffairsComplaintData>
  ): Promise<ConsumerAffairsComplaintResponse> {
    try {
      console.log('🔄 Updating consumer affairs complaint:', complaintId, updateData);

      const response = await apiClient.put(`${this.baseUrl}/${complaintId}`, updateData);

      console.log('✅ Consumer affairs complaint updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating consumer affairs complaint:', error);
      throw error;
    }
  }

  async updateComplaintStatus(
    complaintId: string,
    status: ConsumerAffairsComplaint['status']
  ): Promise<ConsumerAffairsComplaintResponse> {
    try {
      console.log('🔄 Updating consumer affairs complaint status:', complaintId, status);

      const response = await apiClient.patch(`${this.baseUrl}/${complaintId}/status`, { status });

      console.log('✅ Consumer affairs complaint status updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating consumer affairs complaint status:', error);
      throw error;
    }
  }

  async deleteComplaint(complaintId: string): Promise<void> {
    try {
      console.log('🔄 Deleting consumer affairs complaint:', complaintId);

      await apiClient.delete(`${this.baseUrl}/${complaintId}`);

      console.log('✅ Consumer affairs complaint deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting consumer affairs complaint:', error);
      throw error;
    }
  }

  // Helper methods
  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  getPriorityColor(priority: string): string {
    switch (priority?.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  getStatusOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'submitted', label: 'Submitted' },
      { value: 'under_review', label: 'Under Review' },
      { value: 'investigating', label: 'Investigating' },
      { value: 'resolved', label: 'Resolved' },
      { value: 'closed', label: 'Closed' }
    ];
  }

  getCategoryOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'Billing & Charges', label: 'Billing & Charges' },
      { value: 'Service Quality', label: 'Service Quality' },
      { value: 'Network Issues', label: 'Network Issues' },
      { value: 'Customer Service', label: 'Customer Service' },
      { value: 'Other', label: 'Other' }
    ];
  }

  getPriorityOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'urgent', label: 'Urgent' }
    ];
  }
}

export const consumerAffairsService = new ConsumerAffairsService();
