'use client';

import React from 'react';
import { TextArea } from '@/components/forms';
import { BusinessInfoData, ApplicationFormComponentProps } from './index';

interface BusinessInfoProps extends ApplicationFormComponentProps {
  data: BusinessInfoData;
  onChange: (data: BusinessInfoData) => void;
}

const BusinessInfo: React.FC<BusinessInfoProps> = ({
  data,
  onChange,
  errors = {},
  disabled = false
}) => {
  const handleInputChange = (field: keyof BusinessInfoData, value: string) => {
    onChange({
      ...data,
      [field]: value
    });
  };

  return (
    <div className="space-y-8">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Business Information
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide detailed information about your business operations, facilities, and model
        </p>
      </div>

      {/* Business Description */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Business Description
        </h3>
        
        <TextArea
          label="Business Description"
          value={data.businessDescription}
          onChange={(e) => handleInputChange('businessDescription', e.target.value)}
          placeholder="Provide a comprehensive description of your business, its purpose, and core activities"
          rows={6}
          required
          disabled={disabled}
          error={errors.businessDescription}
          helperText="Include your company's mission, vision, and primary business objectives"
        />
      </div>

      {/* Operational Areas */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Operational Areas
        </h3>
        
        <TextArea
          label="Areas of Operation"
          value={data.operationalAreas}
          onChange={(e) => handleInputChange('operationalAreas', e.target.value)}
          placeholder="Describe the geographical areas where you plan to operate and provide services"
          rows={5}
          required
          disabled={disabled}
          error={errors.operationalAreas}
          helperText="Include specific regions, cities, or districts where services will be offered"
        />
      </div>

      {/* Facilities */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Facilities and Infrastructure
        </h3>
        
        <TextArea
          label="Facilities Description"
          value={data.facilities}
          onChange={(e) => handleInputChange('facilities', e.target.value)}
          placeholder="Detail your physical facilities, office locations, warehouses, and infrastructure"
          rows={5}
          required
          disabled={disabled}
          error={errors.facilities}
          helperText="Include addresses, sizes, ownership status (owned/leased), and facility purposes"
        />
      </div>

      {/* Equipment */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Equipment and Technology
        </h3>
        
        <TextArea
          label="Equipment and Technology"
          value={data.equipment}
          onChange={(e) => handleInputChange('equipment', e.target.value)}
          placeholder="List major equipment, technology systems, and technical infrastructure"
          rows={5}
          required
          disabled={disabled}
          error={errors.equipment}
          helperText="Include specifications, capacity, and how equipment supports your service delivery"
        />
      </div>

      {/* Business Model */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Business Model
        </h3>
        
        <TextArea
          label="Business Model Description"
          value={data.businessModel}
          onChange={(e) => handleInputChange('businessModel', e.target.value)}
          placeholder="Explain your business model, revenue streams, and how you plan to generate income"
          rows={6}
          required
          disabled={disabled}
          error={errors.businessModel}
          helperText="Include pricing strategy, customer segments, and value proposition"
        />
      </div>

      {/* Business Information Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start">
            <i className="ri-building-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                Facilities
              </h4>
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                Provide complete details about all business locations and infrastructure.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
          <div className="flex items-start">
            <i className="ri-settings-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">
                Equipment
              </h4>
              <p className="text-green-700 dark:text-green-300 text-sm">
                List all major equipment and technology that will be used in operations.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
          <div className="flex items-start">
            <i className="ri-lightbulb-line text-purple-600 dark:text-purple-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-1">
                Business Model
              </h4>
              <p className="text-purple-700 dark:text-purple-300 text-sm">
                Clearly explain how your business will operate and generate revenue.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Information Notice */}
      <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
        <div className="flex items-start">
          <i className="ri-information-line text-orange-600 dark:text-orange-400 text-lg mr-3 mt-0.5"></i>
          <div>
            <h4 className="text-sm font-medium text-orange-900 dark:text-orange-100 mb-2">
              Business Information Requirements
            </h4>
            <ul className="text-orange-700 dark:text-orange-300 text-sm space-y-1">
              <li>• Provide comprehensive details about all aspects of your business operations</li>
              <li>• Include specific locations, addresses, and facility specifications</li>
              <li>• List all major equipment with technical specifications where relevant</li>
              <li>• Explain how your business model aligns with the license requirements</li>
              <li>• Demonstrate operational readiness and capacity to deliver services</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessInfo;
