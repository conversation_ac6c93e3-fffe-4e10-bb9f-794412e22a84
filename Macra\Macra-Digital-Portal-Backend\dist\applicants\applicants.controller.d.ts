import { ApplicantsService } from './applicants.service';
import { CreateApplicantDto } from '../dto/applicant/create-applicant.dto';
import { UpdateApplicantDto } from '../dto/applicant/update-applicant.dto';
import { Applicants } from '../entities/applicant.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class ApplicantsController {
    private readonly applicantsService;
    constructor(applicantsService: ApplicantsService);
    create(createApplicantDto: CreateApplicantDto, req: any): Promise<Applicants>;
    findAll(query: PaginateQuery): Promise<PaginatedResult<Applicants>>;
    search(searchTerm: string): Promise<Applicants[]>;
    findByBusinessRegistrationNumber(businessRegistrationNumber: string): Promise<Applicants | null>;
    findByTpin(tpin: string): Promise<Applicants | null>;
    findOne(id: string): Promise<Applicants>;
    update(id: string, updateApplicantDto: UpdateApplicantDto, req: any): Promise<Applicants>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
