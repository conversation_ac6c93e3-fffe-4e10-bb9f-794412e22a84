'use client';

import React from 'react';
import { TextArea } from '@/components/forms';
import { ServiceScopeData, ApplicationFormComponentProps } from './index';

interface ServiceScopeProps extends ApplicationFormComponentProps {
  data: ServiceScopeData;
  onChange: (data: ServiceScopeData) => void;
}

const ServiceScope: React.FC<ServiceScopeProps> = ({
  data,
  onChange,
  errors = {},
  disabled = false
}) => {
  const handleInputChange = (field: keyof ServiceScopeData, value: string) => {
    onChange({
      ...data,
      [field]: value
    });
  };

  return (
    <div className="space-y-8">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Service Scope
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Define the scope and nature of services you plan to provide under this license
        </p>
      </div>

      {/* Services Offered */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Services Offered
        </h3>
        
        <TextArea
          label="Detailed Service Description"
          value={data.servicesOffered}
          onChange={(e) => handleInputChange('servicesOffered', e.target.value)}
          placeholder="Provide a comprehensive description of all services you plan to offer under this license"
          rows={6}
          required
          disabled={disabled}
          error={errors.servicesOffered}
          helperText="Include specific service types, features, and any specialized offerings"
        />
      </div>

      {/* Target Market */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Target Market
        </h3>
        
        <TextArea
          label="Target Market Analysis"
          value={data.targetMarket}
          onChange={(e) => handleInputChange('targetMarket', e.target.value)}
          placeholder="Describe your target market, customer segments, and market demographics"
          rows={5}
          required
          disabled={disabled}
          error={errors.targetMarket}
          helperText="Include customer types (individuals, businesses, government), market size, and customer needs"
        />
      </div>

      {/* Geographic Coverage */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Geographic Coverage
        </h3>
        
        <TextArea
          label="Service Coverage Area"
          value={data.geographicCoverage}
          onChange={(e) => handleInputChange('geographicCoverage', e.target.value)}
          placeholder="Define the geographic areas where you will provide services"
          rows={5}
          required
          disabled={disabled}
          error={errors.geographicCoverage}
          helperText="Include specific regions, cities, districts, or coverage boundaries"
        />
      </div>

      {/* Service Standards */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Service Standards and Quality
        </h3>
        
        <TextArea
          label="Service Standards"
          value={data.serviceStandards}
          onChange={(e) => handleInputChange('serviceStandards', e.target.value)}
          placeholder="Describe the service standards, quality measures, and performance indicators you will maintain"
          rows={6}
          required
          disabled={disabled}
          error={errors.serviceStandards}
          helperText="Include quality metrics, service level agreements, and compliance standards"
        />
      </div>

      {/* Service Scope Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start">
            <i className="ri-service-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                Service Definition
              </h4>
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                Clearly define all services you plan to offer and ensure they align with 
                the license category requirements.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
          <div className="flex items-start">
            <i className="ri-map-pin-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">
                Coverage Area
              </h4>
              <p className="text-green-700 dark:text-green-300 text-sm">
                Specify exact geographic boundaries and ensure you have the capacity 
                to serve the proposed coverage area.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
          <div className="flex items-start">
            <i className="ri-group-line text-purple-600 dark:text-purple-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-1">
                Target Market
              </h4>
              <p className="text-purple-700 dark:text-purple-300 text-sm">
                Identify your target customers and demonstrate understanding of 
                their needs and market dynamics.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
          <div className="flex items-start">
            <i className="ri-star-line text-orange-600 dark:text-orange-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-orange-900 dark:text-orange-100 mb-1">
                Quality Standards
              </h4>
              <p className="text-orange-700 dark:text-orange-300 text-sm">
                Define measurable quality standards and show commitment to 
                maintaining high service levels.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Information Notice */}
      <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4 border border-indigo-200 dark:border-indigo-800">
        <div className="flex items-start">
          <i className="ri-information-line text-indigo-600 dark:text-indigo-400 text-lg mr-3 mt-0.5"></i>
          <div>
            <h4 className="text-sm font-medium text-indigo-900 dark:text-indigo-100 mb-2">
              Service Scope Guidelines
            </h4>
            <ul className="text-indigo-700 dark:text-indigo-300 text-sm space-y-1">
              <li>• Ensure all proposed services fall within the license category scope</li>
              <li>• Provide realistic geographic coverage based on your operational capacity</li>
              <li>• Define measurable service standards and quality metrics</li>
              <li>• Demonstrate understanding of your target market and customer needs</li>
              <li>• Show how your services will benefit the community or market</li>
              <li>• Include any innovative or unique aspects of your service offering</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceScope;
