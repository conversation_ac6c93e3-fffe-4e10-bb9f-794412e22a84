const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkStakeholdersIssue() {
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'macra_db'
    });

    try {
        console.log('Checking stakeholders table structure and constraints...');
        
        // Check if stakeholders table exists
        const [tables] = await connection.execute(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'stakeholders'
        `, [process.env.DB_NAME || 'macra_db']);
        
        console.log(`Stakeholders table exists: ${tables.length > 0}`);
        
        if (tables.length > 0) {
            // Check table structure
            const [columns] = await connection.execute(`
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY, COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'stakeholders'
                ORDER BY ORDINAL_POSITION
            `, [process.env.DB_NAME || 'macra_db']);
            
            console.log('\nStakeholders table structure:');
            columns.forEach(col => {
                console.log(`- ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'} ${col.COLUMN_KEY ? `(${col.COLUMN_KEY})` : ''}`);
            });
            
            // Check existing foreign key constraints
            const [constraints] = await connection.execute(`
                SELECT
                    kcu.CONSTRAINT_NAME,
                    kcu.COLUMN_NAME,
                    kcu.REFERENCED_TABLE_NAME,
                    kcu.REFERENCED_COLUMN_NAME,
                    rc.DELETE_RULE,
                    rc.UPDATE_RULE
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                    ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
                WHERE kcu.TABLE_SCHEMA = ?
                AND kcu.TABLE_NAME = 'stakeholders'
                AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
            `, [process.env.DB_NAME || 'macra_db']);

            console.log('\nExisting foreign key constraints:');
            constraints.forEach(constraint => {
                console.log(`- ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME} (DELETE: ${constraint.DELETE_RULE}, UPDATE: ${constraint.UPDATE_RULE})`);
            });
        }
        
        // Check contacts table structure
        const [contactColumns] = await connection.execute(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'contacts'
            AND COLUMN_NAME = 'contact_id'
        `, [process.env.DB_NAME || 'macra_db']);
        
        console.log('\nContacts table contact_id column:');
        contactColumns.forEach(col => {
            console.log(`- ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'} ${col.COLUMN_KEY ? `(${col.COLUMN_KEY})` : ''}`);
        });
        
        // Check users table structure
        const [userColumns] = await connection.execute(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'
            AND COLUMN_NAME = 'user_id'
        `, [process.env.DB_NAME || 'macra_db']);
        
        console.log('\nUsers table user_id column:');
        userColumns.forEach(col => {
            console.log(`- ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'} ${col.COLUMN_KEY ? `(${col.COLUMN_KEY})` : ''}`);
        });
        
        // Check for data type mismatches
        if (tables.length > 0) {
            console.log('\nChecking for potential data type mismatches...');
            
            // Check contact_id field in stakeholders vs contacts
            const [stakeholderContactId] = await connection.execute(`
                SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'stakeholders' AND COLUMN_NAME = 'contact_id'
            `, [process.env.DB_NAME || 'macra_db']);
            
            const [contactsContactId] = await connection.execute(`
                SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'contacts' AND COLUMN_NAME = 'contact_id'
            `, [process.env.DB_NAME || 'macra_db']);
            
            console.log(`Stakeholders.contact_id: ${stakeholderContactId[0]?.DATA_TYPE}(${stakeholderContactId[0]?.CHARACTER_MAXIMUM_LENGTH})`);
            console.log(`Contacts.contact_id: ${contactsContactId[0]?.DATA_TYPE}(${contactsContactId[0]?.CHARACTER_MAXIMUM_LENGTH})`);
            
            // Check created_by field in stakeholders vs users
            const [stakeholderCreatedBy] = await connection.execute(`
                SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'stakeholders' AND COLUMN_NAME = 'created_by'
            `, [process.env.DB_NAME || 'macra_db']);
            
            const [usersUserId] = await connection.execute(`
                SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND COLUMN_NAME = 'user_id'
            `, [process.env.DB_NAME || 'macra_db']);
            
            console.log(`Stakeholders.created_by: ${stakeholderCreatedBy[0]?.DATA_TYPE}(${stakeholderCreatedBy[0]?.CHARACTER_MAXIMUM_LENGTH})`);
            console.log(`Users.user_id: ${usersUserId[0]?.DATA_TYPE}(${usersUserId[0]?.CHARACTER_MAXIMUM_LENGTH})`);
        }
        
    } catch (error) {
        console.error('Error checking stakeholders issue:', error);
    } finally {
        await connection.end();
    }
}

checkStakeholdersIssue();
