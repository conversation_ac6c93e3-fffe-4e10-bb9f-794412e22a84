import { apiClient } from '@/lib/apiClient';

export interface Document {
  document_id: string;
  application_id?: string;
  document_type: string;
  file_name: string;
  entity_type: string;
  entity_id: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  is_required: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
}

export interface CreateDocumentData {
  document_type: string;
  file_name: string;
  entity_type: string;
  entity_id: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  is_required?: boolean;
  application_id?: string;
}

export interface DocumentFilter {
  entity_type?: string;
  entity_id?: string;
  document_type?: string;
  is_required?: boolean;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

export interface DocumentListResponse {
  success: boolean;
  message: string;
  data: Document[];
  total: number;
  page: number;
  limit: number;
}

export interface DocumentResponse {
  success: boolean;
  message: string;
  data: Document;
}

class DocumentService {
  private baseUrl = '/documents';

  async uploadDocument(
    file: File,
    entityType: string,
    entityId: string,
    documentType: string = 'OTHER',
    isRequired: boolean = false,
    applicationId?: string
  ): Promise<DocumentResponse> {
    try {
      console.log('🔄 Uploading document:', {
        fileName: file.name,
        entityType,
        entityId,
        documentType
      });

      const formData = new FormData();
      formData.append('file', file);
      formData.append('entity_type', entityType);
      formData.append('entity_id', entityId);
      formData.append('document_type', documentType);
      formData.append('file_name', file.name);
      formData.append('file_size', file.size.toString());
      formData.append('mime_type', file.type);
      formData.append('is_required', isRequired.toString());
      
      if (applicationId) {
        formData.append('application_id', applicationId);
      }

      const response = await apiClient.post(`${this.baseUrl}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Document uploaded successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error uploading document:', error);
      throw error;
    }
  }

  async uploadMultipleDocuments(
    files: File[],
    entityType: string,
    entityId: string,
    documentType: string = 'OTHER',
    isRequired: boolean = false,
    applicationId?: string
  ): Promise<DocumentResponse[]> {
    try {
      console.log('🔄 Uploading multiple documents:', {
        fileCount: files.length,
        entityType,
        entityId,
        documentType
      });

      const uploadPromises = files.map(file => 
        this.uploadDocument(file, entityType, entityId, documentType, isRequired, applicationId)
      );

      const results = await Promise.all(uploadPromises);
      console.log('✅ All documents uploaded successfully');
      return results;
    } catch (error) {
      console.error('❌ Error uploading multiple documents:', error);
      throw error;
    }
  }

  async getDocuments(filter: DocumentFilter = {}): Promise<DocumentListResponse> {
    try {
      console.log('🔄 Fetching documents with filter:', filter);

      const params = new URLSearchParams();
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`${this.baseUrl}?${params.toString()}`);

      console.log('✅ Documents fetched successfully:', {
        total: response.data.total,
        count: response.data.data?.length || 0
      });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching documents:', error);
      throw error;
    }
  }

  async getDocumentsByEntity(entityType: string, entityId: string): Promise<Document[]> {
    try {
      console.log('🔄 Fetching documents by entity:', { entityType, entityId });

      const response = await apiClient.get(`${this.baseUrl}/by-entity/${entityType}/${entityId}`);

      console.log('✅ Entity documents fetched successfully:', {
        count: response.data?.length || 0
      });
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching entity documents:', error);
      throw error;
    }
  }

  async getDocument(documentId: string): Promise<DocumentResponse> {
    try {
      console.log('🔄 Fetching document:', documentId);

      const response = await apiClient.get(`${this.baseUrl}/${documentId}`);

      console.log('✅ Document fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching document:', error);
      throw error;
    }
  }

  async downloadDocument(documentId: string): Promise<Blob> {
    try {
      console.log('🔄 Downloading document:', documentId);

      const response = await apiClient.get(`${this.baseUrl}/${documentId}/download`, {
        responseType: 'blob'
      });

      console.log('✅ Document downloaded successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Error downloading document:', error);
      throw error;
    }
  }

  async deleteDocument(documentId: string): Promise<void> {
    try {
      console.log('🔄 Deleting document:', documentId);

      await apiClient.delete(`${this.baseUrl}/${documentId}`);

      console.log('✅ Document deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting document:', error);
      throw error;
    }
  }

  async updateDocument(
    documentId: string,
    updateData: Partial<CreateDocumentData>
  ): Promise<DocumentResponse> {
    try {
      console.log('🔄 Updating document:', documentId, updateData);

      const response = await apiClient.put(`${this.baseUrl}/${documentId}`, updateData);

      console.log('✅ Document updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating document:', error);
      throw error;
    }
  }

  // Helper method to format file size
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Helper method to get file icon based on mime type
  getFileIcon(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'ri-image-line';
    if (mimeType.includes('pdf')) return 'ri-file-pdf-line';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'ri-file-word-line';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'ri-file-excel-line';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'ri-file-ppt-line';
    if (mimeType.startsWith('video/')) return 'ri-video-line';
    if (mimeType.startsWith('audio/')) return 'ri-music-line';
    return 'ri-file-line';
  }
}

export const documentService = new DocumentService();
