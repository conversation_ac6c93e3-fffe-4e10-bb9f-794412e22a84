'use client';

import React from 'react';
import { TextArea } from '@/components/forms';
import { BusinessPlanData, ApplicationFormComponentProps } from './index';

interface BusinessPlanProps extends ApplicationFormComponentProps {
  data: BusinessPlanData;
  onChange: (data: BusinessPlanData) => void;
}

const BusinessPlan: React.FC<BusinessPlanProps> = ({
  data,
  onChange,
  errors = {},
  disabled = false
}) => {
  const handleInputChange = (field: keyof BusinessPlanData, value: string) => {
    onChange({
      ...data,
      [field]: value
    });
  };

  return (
    <div className="space-y-8">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Business Plan
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide comprehensive business planning information including financial projections and strategic analysis
        </p>
      </div>

      {/* Market Analysis */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Market Analysis
        </h3>
        
        <TextArea
          label="Market Analysis and Research"
          value={data.marketAnalysis}
          onChange={(e) => handleInputChange('marketAnalysis', e.target.value)}
          placeholder="Provide detailed market analysis including market size, trends, competition, and opportunities"
          rows={6}
          required
          disabled={disabled}
          error={errors.marketAnalysis}
          helperText="Include market research data, competitor analysis, and market positioning strategy"
        />
      </div>

      {/* Financial Projections */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Financial Projections
        </h3>
        
        <TextArea
          label="Financial Projections and Cash Flow"
          value={data.financialProjections}
          onChange={(e) => handleInputChange('financialProjections', e.target.value)}
          placeholder="Provide detailed financial projections for at least 3 years including revenue, expenses, and cash flow"
          rows={7}
          required
          disabled={disabled}
          error={errors.financialProjections}
          helperText="Include revenue forecasts, operating expenses, capital requirements, and break-even analysis"
        />
      </div>

      {/* Competitive Advantage */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Competitive Advantage
        </h3>
        
        <TextArea
          label="Competitive Advantage and Differentiation"
          value={data.competitiveAdvantage}
          onChange={(e) => handleInputChange('competitiveAdvantage', e.target.value)}
          placeholder="Describe your competitive advantages, unique value proposition, and how you differentiate from competitors"
          rows={5}
          required
          disabled={disabled}
          error={errors.competitiveAdvantage}
          helperText="Include unique features, cost advantages, technology, or market positioning that sets you apart"
        />
      </div>

      {/* Risk Assessment */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Risk Assessment and Mitigation
        </h3>
        
        <TextArea
          label="Risk Assessment"
          value={data.riskAssessment}
          onChange={(e) => handleInputChange('riskAssessment', e.target.value)}
          placeholder="Identify potential business risks and describe your mitigation strategies"
          rows={6}
          required
          disabled={disabled}
          error={errors.riskAssessment}
          helperText="Include operational, financial, regulatory, and market risks with corresponding mitigation plans"
        />
      </div>

      {/* Implementation Timeline */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Implementation Timeline
        </h3>
        
        <TextArea
          label="Implementation Timeline and Milestones"
          value={data.implementationTimeline}
          onChange={(e) => handleInputChange('implementationTimeline', e.target.value)}
          placeholder="Provide a detailed timeline for business implementation including key milestones and deliverables"
          rows={6}
          required
          disabled={disabled}
          error={errors.implementationTimeline}
          helperText="Include phases, timelines, resource requirements, and critical success factors"
        />
      </div>

      {/* Business Plan Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start">
            <i className="ri-line-chart-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                Financial Planning
              </h4>
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                Provide realistic financial projections with supporting assumptions and methodology.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
          <div className="flex items-start">
            <i className="ri-trophy-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">
                Competitive Edge
              </h4>
              <p className="text-green-700 dark:text-green-300 text-sm">
                Clearly articulate what makes your business unique and sustainable.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800">
          <div className="flex items-start">
            <i className="ri-shield-check-line text-red-600 dark:text-red-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-red-900 dark:text-red-100 mb-1">
                Risk Management
              </h4>
              <p className="text-red-700 dark:text-red-300 text-sm">
                Demonstrate thorough risk analysis and practical mitigation strategies.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Information Notice */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 border border-yellow-200 dark:border-yellow-800">
        <div className="flex items-start">
          <i className="ri-information-line text-yellow-600 dark:text-yellow-400 text-lg mr-3 mt-0.5"></i>
          <div>
            <h4 className="text-sm font-medium text-yellow-900 dark:text-yellow-100 mb-2">
              Business Plan Requirements
            </h4>
            <ul className="text-yellow-700 dark:text-yellow-300 text-sm space-y-1">
              <li>• Provide detailed financial projections for at least 3 years</li>
              <li>• Include comprehensive market research and analysis</li>
              <li>• Demonstrate clear understanding of competitive landscape</li>
              <li>• Show realistic implementation timeline with achievable milestones</li>
              <li>• Address potential risks and provide mitigation strategies</li>
              <li>• Ensure financial projections align with proposed service scope</li>
              <li>• Include supporting documentation and data sources where applicable</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlan;
