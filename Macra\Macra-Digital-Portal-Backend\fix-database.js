const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function fixDatabase() {
    console.log('🔧 Running Database Foreign Key Constraint Fix');
    console.log('=============================================');
    
    // Database connection configuration
    const config = {
        host: 'localhost',
        port: 3306,
        user: 'root', // Change this to your MySQL username
        password: '', // Change this to your MySQL password
        database: 'macra_db'
    };
    
    try {
        console.log('🔄 Connecting to MySQL...');
        const connection = await mysql.createConnection(config);
        
        console.log('✅ Connected to database');
        
        // Read the SQL fix script from parent directory
        const sqlScript = fs.readFileSync(path.join(__dirname, '..', 'URGENT-RUN-NOW.sql'), 'utf8');
        
        // Split the script into individual statements
        const statements = sqlScript
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        console.log(`📝 Found ${statements.length} SQL statements to execute`);
        
        // Execute each statement
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            if (statement.toLowerCase().startsWith('select')) {
                console.log(`\n🔍 Executing query ${i + 1}:`);
                const [rows] = await connection.execute(statement);
                console.table(rows);
            } else {
                console.log(`\n⚡ Executing statement ${i + 1}: ${statement.substring(0, 50)}...`);
                const [result] = await connection.execute(statement);
                if (result.affectedRows !== undefined) {
                    console.log(`   ✅ Affected ${result.affectedRows} rows`);
                }
            }
        }
        
        await connection.end();
        console.log('\n🎉 Database fix completed successfully!');
        console.log('You can now restart your TypeORM application.');
        
    } catch (error) {
        console.error('\n❌ Error occurred while running the fix:');
        console.error(error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Tips:');
            console.log('- Make sure MySQL server is running');
            console.log('- Check the connection details in this script');
            console.log('- Verify the database name is correct');
        } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('\n💡 Tips:');
            console.log('- Check your MySQL username and password');
            console.log('- Make sure the user has sufficient privileges');
        }
    }
}

fixDatabase();
