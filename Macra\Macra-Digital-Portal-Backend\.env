# # # Database Configuration
DB_DRIVER=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=
DB_NAME=macra_db
DB_SSL=false


# DB_DRIVER=postgres
# DB_HOST=ep-nameless-dawn-a88ehm5h-pooler.eastus2.azure.neon.tech
# DB_PORT=5432
# DB_USERNAME=macra_db_owner
# DB_PASSWORD=npg_EnGrpePIyf09
# DB_NAME=macra_db
# DB_SSL=true

# Application Configuration
NODE_ENV=development
PORT=3001

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000

EMAIL_USER="<EMAIL>"
EMAIL_PWD= "zkqwdkfwtxhdojwd"
