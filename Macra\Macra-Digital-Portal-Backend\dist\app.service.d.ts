import { Repository } from 'typeorm';
import { PostalCode } from './entities/postal-code.entity';
import { SearchPostalCodeDTO } from './dto/postal-code/search.dto';
import { StandardResponse } from './common/interceptors/response.interceptor';
export declare class AppService {
    private readonly postalCodeRepository;
    constructor(postalCodeRepository: Repository<PostalCode>);
    getHello(): string;
    searchPostalCodes(searchCode: SearchPostalCodeDTO): Promise<StandardResponse<PostalCode[]>>;
}
