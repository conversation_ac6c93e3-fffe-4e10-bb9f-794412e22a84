import { IsString, IsEnum, IsOptional, IsUUID, IsInt, IsDateString, Min, Max, Matches, IsNotEmpty, IsIn } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApplicationStatus } from '../../entities/applications.entity';

export class CreateApplicationDto {
  @IsString()
  application_number: string;

  @IsUUID()
  applicant_id: string;

  @IsUUID()
  license_category_id: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    // If no status provided, default to draft
    if (!value || value === '') {
      return 'draft';
    }
    // Ensure the value is a valid status value
    const validStatuses = ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn'];
    return validStatuses.includes(value) ? value : 'draft';
  })
  status?: string;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(6)
  current_step?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(100)
  progress_percentage?: number;

  @IsOptional()
  @IsDateString()
  submitted_at?: Date;
}
