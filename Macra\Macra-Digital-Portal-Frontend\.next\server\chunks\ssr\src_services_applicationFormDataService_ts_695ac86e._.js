module.exports = {

"[project]/src/services/applicationFormDataService.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_services_applicationFormDataService_ts_f3538550._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/services/applicationFormDataService.ts [app-ssr] (ecmascript)");
    });
});
}}),

};