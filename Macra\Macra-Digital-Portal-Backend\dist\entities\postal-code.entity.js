"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostalCode = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
let PostalCode = class PostalCode {
    postal_code_id;
    region;
    district;
    location;
    postal_code;
    created_at;
    updated_at;
    deleted_at;
    generateId() {
        if (!this.postal_code_id) {
            this.postal_code_id = (0, uuid_1.v4)();
        }
    }
};
exports.PostalCode = PostalCode;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], PostalCode.prototype, "postal_code_id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PostalCode.prototype, "region", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PostalCode.prototype, "district", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], PostalCode.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], PostalCode.prototype, "postal_code", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], PostalCode.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], PostalCode.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], PostalCode.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PostalCode.prototype, "generateId", null);
exports.PostalCode = PostalCode = __decorate([
    (0, typeorm_1.Entity)('postal_codes')
], PostalCode);
//# sourceMappingURL=postal-code.entity.js.map