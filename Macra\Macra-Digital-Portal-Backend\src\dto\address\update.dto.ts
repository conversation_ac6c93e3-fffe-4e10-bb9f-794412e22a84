import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>I<PERSON>, ValidateIf } from "class-validator";

export class UpdateAddressDto {
  @IsNotEmpty({ message: "Address ID is required!"})
  @IsString({ message: "Address ID invalid!" })
  @IsUUID()
  address_id: string;

  @IsOptional()
  @IsString({ message: "Address type invalid!" })
  @IsIn(['physical', 'postal'], { message: "Address type must be one of the following: physical, postal" })
  address_type?: string;
    
  @ValidateIf((o) => o.postcode !== undefined)
  @IsString({ message: "Address line 1 invalid!" })
  @IsNotEmpty({ message: "Address line 1 is required if changing city!"})
  address_line_1: string;

  @IsOptional()
  @IsString({ message: "Address line 2 invalid!" })
  address_line_2?: string;

  @IsOptional()
  @IsString({ message: "Address line 3 invalid!" })
  address_line_3?: string;

  @ValidateIf((o) => o.city !== undefined)
  @IsNotEmpty({ message: "Postal code is required if changing city!"})
  @IsString({ message: "Postal code invalid!" })
  @MinLength(6, { message: "Postal code must be at least 6 characters long!" })
  @MaxLength(9, { message: "Postal code must not exceed 9 characters!" })
  postal_code: string;

  @ValidateIf((o) => o.city !== undefined)
  @IsNotEmpty({ message: "Country is required if changing city!"})
  @IsString({ message: "Country invalid!" })
  @MinLength(3, { message: "Country must be at least 3 characters long!" })
  @MaxLength(50, { message: "Country must not exceed 50 characters!" })
  country?: string;

  @ValidateIf((o) => (o.country !== undefined || o.postal_code !== undefined))
  @IsNotEmpty({ message: "City is required if changing country!"})
  @IsString({ message: "City invalid!" })
  @MinLength(3, { message: "City must be at least 3 characters long!" })
  @MaxLength(50, { message: "City must not exceed 50 characters!" })
  city: string;
}
