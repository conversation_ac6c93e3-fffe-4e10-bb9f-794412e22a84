import { User } from './user.entity';
export declare class AuditFailure {
    failure_id: string;
    action: string;
    module: string;
    resource_type: string;
    resource_id: string;
    description: string;
    old_values: Record<string, any>;
    new_values: Record<string, any>;
    metadata: Record<string, any>;
    ip_address: string;
    user_agent: string;
    session_id: string;
    error_message: string;
    user: User;
    user_id: string;
    created_at: Date;
    generateId(): void;
}
