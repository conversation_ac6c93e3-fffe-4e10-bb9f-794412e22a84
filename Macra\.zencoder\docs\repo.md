# Repository Information Overview

## Repository Summary
This repository contains the MACRA Digital Portal, a comprehensive digital platform for the Malawi Communications Regulatory Authority. It consists of two main components: a Next.js frontend and a NestJS backend, along with database management scripts.

## Repository Structure
- **Macra-Digital-Portal-Frontend**: Next.js application with TypeScript
- **Macra-Digital-Portal-Backend**: NestJS application with TypeScript
- **Root Directory**: Contains database fix scripts and utility files

## Projects

### Macra-Digital-Portal-Frontend
**Configuration File**: package.json, next.config.js

#### Language & Runtime
**Language**: TypeScript
**Version**: TypeScript 5.x
**Framework**: Next.js 15.x
**Package Manager**: npm

#### Dependencies
**Main Dependencies**:
- next: ^15.3.3
- react: ^19.1.0
- react-dom: ^19.1.0
- axios: ^1.9.0
- echarts: ^5.6.0
- js-cookie: ^3.0.5

**Development Dependencies**:
- typescript: ^5
- eslint: ^9
- tailwindcss: ^3.4.0
- postcss: ^8.5.3
- autoprefixer: ^10.4.21

#### Build & Installation
```bash
npm install
npm run build
npm run start
```

#### Testing
No dedicated testing framework identified in the frontend project.

### Macra-Digital-Portal-Backend
**Configuration File**: package.json, nest-cli.json

#### Language & Runtime
**Language**: TypeScript
**Version**: TypeScript 5.7.x
**Framework**: NestJS 11.x
**Package Manager**: npm
**Database**: PostgreSQL (pg: ^8.16.0)

#### Dependencies
**Main Dependencies**:
- @nestjs/core: ^11.0.1
- @nestjs/common: ^11.0.1
- @nestjs/config: ^4.0.2
- @nestjs/typeorm: ^11.0.0
- @nestjs/swagger: ^11.2.0
- typeorm: ^0.3.24
- pg: ^8.16.0
- bcryptjs: ^3.0.2
- passport: ^0.7.0
- jsonwebtoken: ^9.0.2

**Development Dependencies**:
- typescript: ^5.7.3
- jest: ^29.7.0
- ts-jest: ^29.2.5
- @nestjs/testing: ^11.0.1
- supertest: ^7.0.0

#### Build & Installation
```bash
npm install
npm run build
npm run start:prod
```

#### Database Management
```bash
npm run seed           # Seed the database
npm run seed:reset     # Reset and reseed the database
npm run typeorm        # Run TypeORM CLI commands
```

#### Testing
**Framework**: Jest
**Test Location**: /test directory
**Naming Convention**: *.spec.ts for unit tests, *.e2e-spec.ts for e2e tests
**Configuration**: jest.config in package.json, jest-e2e.json for e2e tests
**Run Command**:
```bash
npm run test           # Run unit tests
npm run test:e2e       # Run end-to-end tests
```

### Database Management Scripts
The repository contains several SQL and JavaScript files for database management:
- comprehensive-database-fix.sql
- fix-audit-failures-foreign-key.sql
- fix-database-schema.sql
- fix-database.js
- fix-status.sql

These scripts appear to be utilities for fixing database issues and maintaining data integrity.