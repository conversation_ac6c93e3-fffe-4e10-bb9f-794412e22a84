{"version": 3, "file": "license-types.entity.js", "sourceRoot": "", "sources": ["../../src/entities/license-types.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAQ+B;AAC/B,+BAAoC;AACpC,+CAAqC;AAG9B,IAAM,YAAY,GAAlB,MAAM,YAAY;IAOvB,eAAe,CAAS;IAGxB,IAAI,CAAS;IAGb,WAAW,CAAS;IAGpB,QAAQ,CAAS;IAGjB,UAAU,CAAO;IAGjB,UAAU,CAAU;IAGpB,UAAU,CAAO;IAGjB,UAAU,CAAU;IAGpB,UAAU,CAAQ;IAKlB,OAAO,CAAO;IAId,OAAO,CAAQ;IAGf,UAAU;QACR,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,GAAG,IAAA,SAAM,GAAE,CAAC;QAClC,CAAC;IACH,CAAC;CACF,CAAA;AAhDY,oCAAY;AAOvB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,IAAI;KACb,CAAC;;qDACsB;AAGxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;0CAC1C;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;iDACL;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;;8CACP;AAGjB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;gDAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACrB;AAGpB;IADC,IAAA,0BAAgB,GAAE;8BACP,IAAI;gDAAC;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACrB;AAGpB;IADC,IAAA,0BAAgB,GAAE;8BACN,IAAI;gDAAC;AAKlB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAC,CAAC;8BACzB,kBAAI;6CAAC;AAId;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACzC,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BACzB,kBAAI;6CAAC;AAGf;IADC,IAAA,sBAAY,GAAE;;;;8CAKd;uBA/CU,YAAY;IADxB,IAAA,gBAAM,EAAC,eAAe,CAAC;GACX,YAAY,CAgDxB"}