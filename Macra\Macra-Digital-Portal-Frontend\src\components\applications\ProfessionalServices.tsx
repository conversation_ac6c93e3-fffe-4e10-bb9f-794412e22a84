'use client';

import React from 'react';
import { TextArea } from '@/components/forms';
import { ProfessionalServicesData, ApplicationFormComponentProps } from './index';

interface ProfessionalServicesProps extends ApplicationFormComponentProps {
  data: ProfessionalServicesData;
  onChange: (data: ProfessionalServicesData) => void;
}

const ProfessionalServices: React.FC<ProfessionalServicesProps> = ({
  data,
  onChange,
  errors = {},
  disabled = false
}) => {
  const handleInputChange = (field: keyof ProfessionalServicesData, value: string) => {
    onChange({
      ...data,
      [field]: value
    });
  };

  // Check if all required fields are filled

  return (
    <div className="space-y-8">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Professional Services
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Provide information about external consultants, service providers, and support arrangements
        </p>
      </div>

      {/* Consultants */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          External Consultants
        </h3>
        
        <TextArea
          label="Consultant Information"
          value={data.consultants}
          onChange={(e) => handleInputChange('consultants', e.target.value)}
          placeholder="List any external consultants, their areas of expertise, and their role in your operations"
          rows={5}
          required
          disabled={disabled}
          error={errors.consultants}
          helperText="Include consultant names, companies, specializations, and how they will support your business. This field is required."
        />
      </div>

      {/* Service Providers */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Service Providers
        </h3>
        
        <TextArea
          label="Third-Party Service Providers"
          value={data.serviceProviders}
          onChange={(e) => handleInputChange('serviceProviders', e.target.value)}
          placeholder="Describe any third-party service providers, vendors, or partners that will support your operations"
          rows={5}
          required
          disabled={disabled}
          error={errors.serviceProviders}
          helperText="Include details about outsourced services, vendor relationships, and partnership agreements. This field is required."
        />
      </div>

      {/* Technical Support */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Technical Support Arrangements
        </h3>
        
        <TextArea
          label="Technical Support"
          value={data.technicalSupport}
          onChange={(e) => handleInputChange('technicalSupport', e.target.value)}
          placeholder="Describe your technical support arrangements, including internal capabilities and external support contracts"
          rows={5}
          required
          disabled={disabled}
          error={errors.technicalSupport}
          helperText="Include information about technical expertise, support contracts, and emergency response capabilities"
        />
      </div>

      {/* Maintenance Arrangements */}
      <div className="space-y-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Maintenance and Support
        </h3>
        
        <TextArea
          label="Maintenance Arrangements"
          value={data.maintenanceArrangements}
          onChange={(e) => handleInputChange('maintenanceArrangements', e.target.value)}
          placeholder="Detail your maintenance arrangements for equipment, systems, and infrastructure"
          rows={5}
          required
          disabled={disabled}
          error={errors.maintenanceArrangements}
          helperText="Include preventive maintenance schedules, service level agreements, and backup arrangements"
        />
      </div>

      {/* Professional Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start">
            <i className="ri-user-star-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                Consultant Guidelines
              </h4>
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                Include consultant qualifications, experience in your industry, and specific 
                areas where they will provide expertise.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
          <div className="flex items-start">
            <i className="ri-tools-line text-green-600 dark:text-green-400 text-lg mr-3 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">
                Technical Support
              </h4>
              <p className="text-green-700 dark:text-green-300 text-sm">
                Demonstrate adequate technical support capabilities to ensure reliable 
                service delivery and compliance.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Information Notice */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
        <div className="flex items-start">
          <i className="ri-information-line text-purple-600 dark:text-purple-400 text-lg mr-3 mt-0.5"></i>
          <div>
            <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">
              Professional Services Requirements
            </h4>
            <ul className="text-purple-700 dark:text-purple-300 text-sm space-y-1">
              <li>• Provide detailed information about all external professional relationships</li>
              <li>• Include service level agreements and contract terms where applicable</li>
              <li>• Demonstrate adequate technical and maintenance support capabilities</li>
              <li>• Show how professional services align with your business objectives</li>
              <li>• Include backup arrangements for critical services</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfessionalServices;
