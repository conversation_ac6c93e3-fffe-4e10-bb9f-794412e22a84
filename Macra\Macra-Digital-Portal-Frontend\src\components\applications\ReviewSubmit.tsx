'use client';

import React from 'react';
import { ApplicationFormData, ApplicationFormComponentProps } from './index';

interface ReviewSubmitProps extends ApplicationFormComponentProps {
  data: ApplicationFormData;
  onSubmit: () => void;
  isSubmitting?: boolean;
}

const ReviewSubmit: React.FC<ReviewSubmitProps> = ({
  data,
  onSubmit,
  disabled = false,
  isSubmitting = false
}) => {
  const sections = [
    {
      title: 'Applicant Information',
      icon: 'ri-user-line',
      data: data.applicantInfo,
      fields: [
        { label: 'Applicant Name', value: data.applicantInfo.applicantName },
        { label: 'Email', value: data.applicantInfo.email },
        { label: 'Telephone', value: data.applicantInfo.telephone },
        { label: 'Physical Address', value: `${data.applicantInfo.physicalStreet}, ${data.applicantInfo.physicalCity}, ${data.applicantInfo.physicalCountry}` }
      ]
    },
    {
      title: 'Company Profile',
      icon: 'ri-building-line',
      data: data.companyProfile,
      fields: [
        { label: 'Business Registration No.', value: data.companyProfile.businessRegistrationNo },
        { label: 'TPIN', value: data.companyProfile.tpin },
        { label: 'Date of Incorporation', value: data.companyProfile.dateOfIncorporation },
        { label: 'Shareholders', value: `${data.companyProfile.shareholders.length} shareholders` },
        { label: 'Directors', value: `${data.companyProfile.directors.length} directors` }
      ]
    },
    {
      title: 'Management Team',
      icon: 'ri-team-line',
      data: data.management,
      fields: [
        { label: 'Management Team Members', value: `${data.management.managementTeam.length} members` },
        { label: 'Organizational Structure', value: data.management.organizationalStructure ? 'Provided' : 'Not provided' }
      ]
    },
    {
      title: 'Professional Services',
      icon: 'ri-service-line',
      data: data.professionalServices,
      fields: [
        { label: 'Technical Support', value: data.professionalServices.technicalSupport ? 'Provided' : 'Not provided' },
        { label: 'Maintenance Arrangements', value: data.professionalServices.maintenanceArrangements ? 'Provided' : 'Not provided' }
      ]
    },
    {
      title: 'Business Information',
      icon: 'ri-briefcase-line',
      data: data.businessInfo,
      fields: [
        { label: 'Business Description', value: data.businessInfo.businessDescription ? 'Provided' : 'Not provided' },
        { label: 'Business Model', value: data.businessInfo.businessModel ? 'Provided' : 'Not provided' }
      ]
    },
    {
      title: 'Service Scope',
      icon: 'ri-global-line',
      data: data.serviceScope,
      fields: [
        { label: 'Services Offered', value: data.serviceScope.servicesOffered ? 'Provided' : 'Not provided' },
        { label: 'Geographic Coverage', value: data.serviceScope.geographicCoverage ? 'Provided' : 'Not provided' }
      ]
    },
    {
      title: 'Business Plan',
      icon: 'ri-line-chart-line',
      data: data.businessPlan,
      fields: [
        { label: 'Market Analysis', value: data.businessPlan.marketAnalysis ? 'Provided' : 'Not provided' },
        { label: 'Financial Projections', value: data.businessPlan.financialProjections ? 'Provided' : 'Not provided' }
      ]
    },
    {
      title: 'Legal History',
      icon: 'ri-shield-check-line',
      data: data.legalHistory,
      fields: [
        { label: 'Previous Violations', value: data.legalHistory.previousViolations ? 'Disclosed' : 'Not disclosed' },
        { label: 'Compliance Record', value: data.legalHistory.complianceRecord ? 'Provided' : 'Not provided' }
      ]
    }
  ];

  const getCompletionStatus = (sectionData: any) => {
    if (!sectionData) return 'incomplete';
    
    const values = Object.values(sectionData);
    const hasRequiredFields = values.some(value => {
      if (Array.isArray(value)) return value.length > 0;
      return value && String(value).trim() !== '';
    });
    
    return hasRequiredFields ? 'complete' : 'incomplete';
  };

  const allSectionsComplete = sections.every(section => getCompletionStatus(section.data) === 'complete');

  return (
    <div className="space-y-8">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          Review & Submit Application
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Please review all information before submitting your application
        </p>
      </div>

      {/* Application Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {sections.map((section, index) => {
          const status = getCompletionStatus(section.data);
          return (
            <div
              key={index}
              className={`border rounded-lg p-4 ${
                status === 'complete'
                  ? 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20'
                  : 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <i className={`${section.icon} text-lg mr-2 ${
                    status === 'complete' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}></i>
                  <h3 className={`font-medium ${
                    status === 'complete' ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'
                  }`}>
                    {section.title}
                  </h3>
                </div>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  status === 'complete'
                    ? 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200'
                    : 'bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200'
                }`}>
                  {status === 'complete' ? 'Complete' : 'Incomplete'}
                </span>
              </div>
              
              <div className="space-y-1">
                {section.fields.map((field, fieldIndex) => (
                  <div key={fieldIndex} className="flex justify-between text-sm">
                    <span className={status === 'complete' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}>
                      {field.label}:
                    </span>
                    <span className={`font-medium ${status === 'complete' ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'}`}>
                      {field.value || 'Not provided'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Submission Status */}
      <div className={`rounded-lg p-4 border ${
        allSectionsComplete
          ? 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20'
          : 'border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20'
      }`}>
        <div className="flex items-start">
          <i className={`text-lg mr-3 mt-0.5 ${
            allSectionsComplete
              ? 'ri-checkbox-circle-line text-green-600 dark:text-green-400'
              : 'ri-error-warning-line text-yellow-600 dark:text-yellow-400'
          }`}></i>
          <div>
            <h4 className={`text-sm font-medium mb-1 ${
              allSectionsComplete
                ? 'text-green-900 dark:text-green-100'
                : 'text-yellow-900 dark:text-yellow-100'
            }`}>
              {allSectionsComplete ? 'Application Ready for Submission' : 'Application Incomplete'}
            </h4>
            <p className={`text-sm ${
              allSectionsComplete
                ? 'text-green-700 dark:text-green-300'
                : 'text-yellow-700 dark:text-yellow-300'
            }`}>
              {allSectionsComplete
                ? 'All required sections have been completed. You can now submit your application.'
                : 'Please complete all required sections before submitting your application.'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Terms and Conditions
        </h4>
        <div className="space-y-3 text-sm text-gray-700 dark:text-gray-300">
          <div className="flex items-start">
            <i className="ri-checkbox-line text-primary text-lg mr-3 mt-0.5"></i>
            <p>
              I certify that all information provided in this application is true, complete, and accurate.
            </p>
          </div>
          <div className="flex items-start">
            <i className="ri-checkbox-line text-primary text-lg mr-3 mt-0.5"></i>
            <p>
              I understand that providing false or misleading information may result in the rejection of this application.
            </p>
          </div>
          <div className="flex items-start">
            <i className="ri-checkbox-line text-primary text-lg mr-3 mt-0.5"></i>
            <p>
              I agree to comply with all applicable laws, regulations, and license conditions if this application is approved.
            </p>
          </div>
          <div className="flex items-start">
            <i className="ri-checkbox-line text-primary text-lg mr-3 mt-0.5"></i>
            <p>
              I authorize MACRA to verify the information provided and conduct necessary background checks.
            </p>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-center pt-6">
        <button
          type="button"
          onClick={onSubmit}
          disabled={disabled || !allSectionsComplete || isSubmitting}
          className={`px-8 py-3 rounded-lg font-medium text-white transition-colors duration-200 ${
            allSectionsComplete && !disabled && !isSubmitting
              ? 'bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2'
              : 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'
          }`}
        >
          {isSubmitting ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              Submitting Application...
            </>
          ) : (
            <>
              <i className="ri-send-plane-line mr-2"></i>
              Submit Application
            </>
          )}
        </button>
      </div>

      {/* Information Notice */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <div className="flex items-start">
          <i className="ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"></i>
          <div>
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
              After Submission
            </h4>
            <p className="text-blue-700 dark:text-blue-300 text-sm">
              Once submitted, your application will be reviewed by MACRA. You will receive email notifications 
              about the status of your application. The review process may take several weeks depending on 
              the license type and complexity of your application.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewSubmit;
