'use client';

import { customerApiClient, ApiResponse } from '@/lib/customer-api';

export interface DataBreachReport {
  report_id: string;
  title: string;
  description: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'under_review' | 'investigating' | 'resolved' | 'closed';
  incident_date: string;
  organization_involved: string;
  affected_data_types?: string;
  contact_attempts?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  documents?: Array<{
    id: string;
    filename: string;
    file_path: string;
    file_size: number;
    mime_type: string;
    uploaded_at: string;
  }>;
}

export interface CreateDataBreachReportData {
  title: string;
  description: string;
  category: string;
  severity: string;
  incident_date: string;
  organization_involved: string;
  affected_data_types?: string;
  contact_attempts?: string;
  attachments?: File[];
}

export interface UpdateDataBreachReportData {
  title?: string;
  description?: string;
  category?: string;
  severity?: string;
  incident_date?: string;
  organization_involved?: string;
  affected_data_types?: string;
  contact_attempts?: string;
}

export interface DataBreachReportFilters {
  status?: string;
  category?: string;
  severity?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

class DataBreachService {
  private baseUrl = '/api/data-breach-reports';

  async createReport(data: CreateDataBreachReportData): Promise<ApiResponse<DataBreachReport>> {
    try {
      const formData = new FormData();
      
      // Add text fields
      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('category', data.category);
      formData.append('severity', data.severity);
      formData.append('incident_date', data.incident_date);
      formData.append('organization_involved', data.organization_involved);
      
      if (data.affected_data_types) {
        formData.append('affected_data_types', data.affected_data_types);
      }
      
      if (data.contact_attempts) {
        formData.append('contact_attempts', data.contact_attempts);
      }

      // Add file attachments
      if (data.attachments && data.attachments.length > 0) {
        data.attachments.forEach((file, index) => {
          formData.append(`attachments`, file);
        });
      }

      const response = await customerApiClient.post(this.baseUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Data breach report created successfully'
      };
    } catch (error: any) {
      console.error('Error creating data breach report:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to create data breach report',
        errors: error.response?.data?.errors
      };
    }
  }

  async getReports(filters?: DataBreachReportFilters): Promise<ApiResponse<DataBreachReport[]>> {
    try {
      const params = new URLSearchParams();
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
      }

      const url = params.toString() ? `${this.baseUrl}?${params.toString()}` : this.baseUrl;
      const response = await customerApiClient.get(url);

      return {
        success: true,
        data: response.data.data || [],
        message: response.data.message || 'Data breach reports retrieved successfully'
      };
    } catch (error: any) {
      console.error('Error fetching data breach reports:', error);
      return {
        success: false,
        data: [],
        message: error.response?.data?.message || 'Failed to fetch data breach reports',
        errors: error.response?.data?.errors
      };
    }
  }

  async getReport(reportId: string): Promise<ApiResponse<DataBreachReport>> {
    try {
      const response = await customerApiClient.get(`${this.baseUrl}/${reportId}`);

      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Data breach report retrieved successfully'
      };
    } catch (error: any) {
      console.error('Error fetching data breach report:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch data breach report',
        errors: error.response?.data?.errors
      };
    }
  }

  async updateReport(reportId: string, data: UpdateDataBreachReportData): Promise<ApiResponse<DataBreachReport>> {
    try {
      const response = await customerApiClient.put(`${this.baseUrl}/${reportId}`, data);

      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Data breach report updated successfully'
      };
    } catch (error: any) {
      console.error('Error updating data breach report:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to update data breach report',
        errors: error.response?.data?.errors
      };
    }
  }

  async deleteReport(reportId: string): Promise<ApiResponse<void>> {
    try {
      const response = await customerApiClient.delete(`${this.baseUrl}/${reportId}`);

      return {
        success: true,
        message: response.data.message || 'Data breach report deleted successfully'
      };
    } catch (error: any) {
      console.error('Error deleting data breach report:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to delete data breach report',
        errors: error.response?.data?.errors
      };
    }
  }

  // Helper methods for status management
  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'under_review':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'investigating':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
      case 'resolved':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'closed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  }

  getStatusLabel(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending Review';
      case 'under_review':
        return 'Under Review';
      case 'investigating':
        return 'Investigating';
      case 'resolved':
        return 'Resolved';
      case 'closed':
        return 'Closed';
      default:
        return status;
    }
  }

  getSeverityColor(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  }

  getSeverityLabel(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'low':
        return 'Low Risk';
      case 'medium':
        return 'Medium Risk';
      case 'high':
        return 'High Risk';
      case 'critical':
        return 'Critical';
      default:
        return severity;
    }
  }

  formatDate(dateString: string): string {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return dateString;
    }
  }

  formatDateTime(dateString: string): string {
    try {
      return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  }
}

export const dataBreachService = new DataBreachService();
