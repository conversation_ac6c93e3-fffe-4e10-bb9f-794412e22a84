'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';

interface LicenseOption {
  id: string;
  name: string;
  description: string;
  icon: string;
  iconBg: string;
  iconColor: string;
  processingTime: string;
  applicationFee: number;
  currency: string;
  requirements: string[];
  subTypes: Array<{
    name: string;
    description: string;
    specificRequirements?: string[];
  }>;
  benefits: string[];
  validityPeriod: string;
  renewalRequired: boolean;
}

const StandardsLicenseApplicationPage = () => {
  const { isAuthenticated} = useAuth();
  const router = useRouter();
  
  const [showDetails, setShowDetails] = useState<string | null>(null);

  // Redirect to customer login if not authenticated
  useEffect(() => {
    if ( !isAuthenticated) {
      router.push('/customer/auth/login');
    }
  }, [isAuthenticated, router]);

  // License options data
  const licenseOptions: LicenseOption[] = [
    {
      id: 'type-approval',
      name: 'Type Approval Certificate',
      description: 'Certification for telecommunications and radio equipment to ensure compliance with technical standards and regulations.',
      icon: 'ri-shield-check-line',
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600',
      processingTime: '21-45 days',
      applicationFee: 150000,
      currency: 'MWK',
      validityPeriod: '5 years',
      renewalRequired: true,
      requirements: [
        'Valid business registration certificate',
        'Technical specifications and documentation',
        'Test reports from accredited laboratories (IEC, ITU-T, or equivalent)',
        'Equipment samples for testing and evaluation',
        'User manual and installation guide',
        'Declaration of conformity from manufacturer',
        'EMC (Electromagnetic Compatibility) test reports',
        'Safety compliance certificates',
        'RF (Radio Frequency) emission test reports',
        'Quality management system certification (ISO 9001 or equivalent)'
      ],
      subTypes: [
        {
          name: 'Telecommunications Equipment Type Approval',
          description: 'For fixed-line telecommunications equipment, PBX systems, and network infrastructure',
          specificRequirements: [
            'Network interface specifications',
            'Signaling protocol documentation',
            'Interoperability test results'
          ]
        },
        {
          name: 'Radio Equipment Type Approval',
          description: 'For radio transmitters, receivers, and transceivers operating in licensed spectrum',
          specificRequirements: [
            'Frequency allocation documentation',
            'Antenna specifications and radiation patterns',
            'Spurious emission measurements',
            'Frequency stability test reports'
          ]
        },
        {
          name: 'Mobile Device Type Approval',
          description: 'For mobile phones, smartphones, tablets, and other portable communication devices',
          specificRequirements: [
            'SAR (Specific Absorption Rate) test reports',
            'Battery safety certification',
            'Drop test and durability reports',
            'Software security assessment'
          ]
        },
        {
          name: 'Network Equipment Type Approval',
          description: 'For routers, switches, base stations, and other network infrastructure equipment',
          specificRequirements: [
            'Network security compliance documentation',
            'Performance benchmarking reports',
            'Environmental operating conditions certification',
            'Power consumption and efficiency reports'
          ]
        }
      ],
      benefits: [
        'Legal authorization to import, distribute, and sell approved equipment',
        'Compliance with national telecommunications standards',
        'Consumer protection through certified quality assurance',
        'Market access for telecommunications equipment',
        'Reduced liability through regulatory compliance',
        'Enhanced brand credibility and trust'
      ]
    },
    {
      id: 'short-codes',
      name: 'Short Codes Authorization',
      description: 'Authorization for premium SMS, USSD, and voice service short codes for commercial and service applications.',
      icon: 'ri-hashtag',
      iconBg: 'bg-green-100',
      iconColor: 'text-green-600',
      processingTime: '14-21 days',
      applicationFee: 50000,
      currency: 'MWK',
      validityPeriod: '2 years',
      renewalRequired: true,
      requirements: [
        'Valid business registration certificate',
        'Tax clearance certificate',
        'Service description and detailed use case',
        'Technical implementation plan',
        'Content moderation and compliance policy',
        'Customer service and support plan',
        'Billing and revenue sharing agreement (if applicable)',
        'Data protection and privacy policy',
        'Terms and conditions for end users',
        'Marketing and promotional material samples'
      ],
      subTypes: [
        {
          name: 'SMS Premium Service Short Codes',
          description: 'For premium SMS services, contests, voting, and information services',
          specificRequirements: [
            'Content approval and moderation procedures',
            'Pricing structure and billing methodology',
            'Opt-in/opt-out mechanisms',
            'Age verification procedures (if applicable)'
          ]
        },
        {
          name: 'USSD Service Short Codes',
          description: 'For interactive USSD services, mobile banking, and information queries',
          specificRequirements: [
            'Session management and timeout procedures',
            'Security protocols for sensitive transactions',
            'Multi-language support documentation',
            'Error handling and user guidance procedures'
          ]
        },
        {
          name: 'Voice Service Short Codes',
          description: 'For premium voice services, hotlines, and automated voice response systems',
          specificRequirements: [
            'Call routing and queue management procedures',
            'Voice quality and recording standards',
            'Accessibility features for disabled users',
            'Emergency service integration (if applicable)'
          ]
        },
        {
          name: 'Data Service Short Codes',
          description: 'For mobile data services, app downloads, and content delivery',
          specificRequirements: [
            'Data compression and optimization procedures',
            'Content delivery network specifications',
            'Bandwidth management and fair usage policies',
            'Mobile application security assessment'
          ]
        }
      ],
      benefits: [
        'Exclusive use of allocated short code numbers',
        'Revenue generation through premium services',
        'Enhanced customer engagement and interaction',
        'Brand recognition through memorable short codes',
        'Compliance with telecommunications regulations',
        'Access to mobile network operator partnerships'
      ]
    }
  ];

  // Handle license selection
  // Handle license selection
  // (Removed unused handleSelectLicense and selectedLicense state)
  // Handle starting application
  const handleStartApplication = (licenseId: string) => {
    // Navigate to the application form with the selected license type
    router.push(`/customer/applications/new?type=standards&subtype=${licenseId}`);
  };

  // Toggle details view
  const toggleDetails = (licenseId: string) => {
    setShowDetails(showDetails === licenseId ? null : licenseId);
  };

  // Breadcrumb items
  const breadcrumbs = [
    { label: 'Dashboard', href: '/customer' },
    { label: 'Applications', href: '/customer/applications' },
    { label: 'Standards' }
  ];

  // // Show loading state
  // if (authLoading) {
  //   return (
  //     <CustomerLayout breadcrumbs={breadcrumbs}>
  //       <div className="flex items-center justify-center min-h-96">
  //         <div className="text-center">
  //           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
  //           <p className="mt-4 text-gray-600">Loading...</p>
  //         </div>
  //       </div>
  //     </CustomerLayout>
  //   );
  // }

  return (
    <CustomerLayout breadcrumbs={breadcrumbs}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Standards License Application</h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">Choose the type of standards license you want to apply for</p>
            </div>
          </div>
        </div>

        {/* License Options */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {licenseOptions.map((license) => (
            <div key={license.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              {/* Header */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <div className={`w-10 h-10 ${license.iconBg} dark:bg-opacity-20 rounded-lg flex items-center justify-center mr-3`}>
                    <i className={`${license.icon} text-lg ${license.iconColor} dark:text-opacity-80`}></i>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{license.name}</h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">{license.description}</p>
                  </div>
                </div>
              </div>

              {/* Key Information */}
              <div className="p-4">
                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Processing Time</div>
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{license.processingTime}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Application Fee</div>
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {license.currency} {license.applicationFee.toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Validity Period</div>
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{license.validityPeriod}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Renewal Required</div>
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {license.renewalRequired ? 'Yes' : 'No'}
                    </div>
                  </div>
                </div>

                {/* Sub-types Preview */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Available Types:</h4>
                  <div className="space-y-1">
                    {license.subTypes.slice(0, 2).map((subType, index) => (
                      <div key={index} className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                        <i className="ri-check-line text-green-500 mr-2 text-sm"></i>
                        <span className="truncate">{subType.name}</span>
                      </div>
                    ))}
                    {license.subTypes.length > 2 && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        +{license.subTypes.length - 2} more types available
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-2">
                  <button
                    onClick={() => toggleDetails(license.id)}
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-md text-sm font-medium transition-colors"
                  >
                    <i className="ri-information-line mr-1"></i>
                    {showDetails === license.id ? 'Hide Details' : 'View Details'}
                  </button>
                  <button
                    onClick={() => handleStartApplication(license.id)}
                    className="flex-1 px-3 py-2 bg-primary text-white hover:bg-red-700 rounded-md text-sm font-medium transition-colors"
                  >
                    <i className="ri-file-add-line mr-1"></i>
                    Start Application
                  </button>
                </div>
              </div>

              {/* Detailed Information */}
              {showDetails === license.id && (
                <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
                  <div className="p-4 space-y-4">
                    {/* All Sub-types */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">All Available Types:</h4>
                      <div className="space-y-3">
                        {license.subTypes.map((subType, index) => (
                          <div key={index} className="bg-white dark:bg-gray-800 rounded-md p-3 border border-gray-200 dark:border-gray-600">
                            <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">{subType.name}</h5>
                            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">{subType.description}</p>
                            {subType.specificRequirements && (
                              <div>
                                <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1">
                                  Specific Requirements:
                                </div>
                                <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-0.5">
                                  {subType.specificRequirements.map((req, reqIndex) => (
                                    <li key={reqIndex} className="flex items-start">
                                      <i className="ri-arrow-right-s-line text-gray-400 mr-1 mt-0.5 flex-shrink-0 text-xs"></i>
                                      {req}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* General Requirements */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">General Requirements:</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
                        {license.requirements.map((requirement, index) => (
                          <div key={index} className="flex items-start text-xs text-gray-600 dark:text-gray-400">
                            <i className="ri-check-line text-green-500 mr-1 mt-0.5 flex-shrink-0 text-xs"></i>
                            {requirement}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Benefits */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Benefits:</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-1">
                        {license.benefits.map((benefit, index) => (
                          <div key={index} className="flex items-start text-xs text-gray-600 dark:text-gray-400">
                            <i className="ri-star-line text-yellow-500 mr-1 mt-0.5 flex-shrink-0 text-xs"></i>
                            {benefit}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Help Section */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/40 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
              <i className="ri-information-line text-blue-600 dark:text-blue-400 text-sm"></i>
            </div>
            <div>
              <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">Need Help Choosing?</h3>
              <p className="text-blue-700 dark:text-blue-300 text-xs mb-3">
                Not sure which license type is right for you? Our support team can help you determine the best option based on your specific needs and requirements.
              </p>
              <div className="flex flex-col sm:flex-row gap-2">
                <Link
                  href="/customer/help"
                  className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white hover:bg-blue-700 rounded-md text-xs font-medium transition-colors"
                >
                  <i className="ri-question-line mr-1"></i>
                  Visit Help Center
                </Link>
                <Link
                  href="/customer/resources"
                  className="inline-flex items-center px-3 py-1.5 border border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300 bg-white dark:bg-blue-900/20 hover:bg-blue-50 dark:hover:bg-blue-900/40 rounded-md text-xs font-medium transition-colors"
                >
                  <i className="ri-customer-service-2-line mr-1"></i>
                  Contact Support
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default StandardsLicenseApplicationPage;