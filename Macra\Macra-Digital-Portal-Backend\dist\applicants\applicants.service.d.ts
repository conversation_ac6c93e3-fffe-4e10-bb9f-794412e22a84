import { Repository } from 'typeorm';
import { Applicants } from '../entities/applicant.entity';
import { CreateApplicantDto } from '../dto/applicant/create-applicant.dto';
import { UpdateApplicantDto } from '../dto/applicant/update-applicant.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
export declare class ApplicantsService {
    private applicantsRepository;
    constructor(applicantsRepository: Repository<Applicants>);
    private readonly paginateConfig;
    create(createApplicantDto: CreateApplicantDto, createdBy: string): Promise<Applicants>;
    findAll(query: PaginateQuery): Promise<Paginated<Applicants>>;
    findOne(id: string): Promise<Applicants>;
    findByBusinessRegistrationNumber(businessRegistrationNumber: string): Promise<Applicants | null>;
    findByTpin(tpin: string): Promise<Applicants | null>;
    update(id: string, updateApplicantDto: UpdateApplicantDto, updatedBy: string): Promise<Applicants>;
    remove(id: string): Promise<void>;
    search(searchTerm: string): Promise<Applicants[]>;
}
