import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DataBreachReportService } from './data-breach-report.service';
import {
  CreateDataBreachReportDto,
  UpdateDataBreachReportDto,
  DataBreachReportFilterDto,
  UpdateDataBreachReportStatusDto,
} from './data-breach-report.dto';

@Controller('data-breach-reports')
@UseGuards(JwtAuthGuard)
export class DataBreachReportController {
  constructor(
    private readonly reportService: DataBreachReportService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(FilesInterceptor('attachments', 5)) // Allow up to 5 files
  async create(
    @Body(ValidationPipe) createDto: CreateDataBreachReportDto,
    @UploadedFiles() files: Express.Multer.File[],
    @Request() req: any,
  ) {
    const report = await this.reportService.create(createDto, req.user.user_id);

    // Handle file uploads if any
    if (files && files.length > 0) {
      // TODO: Implement file upload logic
      // This would typically involve saving files to storage and creating attachment records
    }

    return {
      success: true,
      message: 'Data breach report submitted successfully',
      data: report,
    };
  }

  @Get()
  async findAll(
    @Query(ValidationPipe) filterDto: DataBreachReportFilterDto,
    @Request() req: any,
  ) {
    const result = await this.reportService.findAll(
      filterDto,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Data breach reports retrieved successfully',
      ...result,
    };
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    const report = await this.reportService.findOne(
      id,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Data breach report retrieved successfully',
      data: report,
    };
  }

  @Put(':id')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateDto: UpdateDataBreachReportDto,
    @Request() req: any,
  ) {
    const report = await this.reportService.update(
      id,
      updateDto,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Data breach report updated successfully',
      data: report,
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async delete(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    await this.reportService.delete(
      id,
      req.user.user_id,
      req.user.isStaff || false
    );

    return {
      success: true,
      message: 'Data breach report deleted successfully',
    };
  }

  // Staff-only endpoints
  @Put(':id/status')
  async updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) statusDto: UpdateDataBreachReportStatusDto,
    @Request() req: any,
  ) {
    const report = await this.reportService.updateStatus(
      id,
      statusDto,
      req.user.user_id
    );

    return {
      success: true,
      message: 'Report status updated successfully',
      data: report,
    };
  }

  @Put(':id/assign')
  async assignReport(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('assigned_to', ParseUUIDPipe) assignedTo: string,
    @Request() req: any,
  ) {
    const report = await this.reportService.update(
      id,
      { assigned_to: assignedTo },
      req.user.user_id,
      true
    );

    return {
      success: true,
      message: 'Report assigned successfully',
      data: report,
    };
  }

  @Get('stats/summary')
  async getStatsSummary(@Request() req: any) {
    // TODO: Implement statistics summary
    // This would return counts by status, category, severity, etc.
    return {
      success: true,
      message: 'Statistics retrieved successfully',
      data: {
        total: 0,
        by_status: {},
        by_category: {},
        by_severity: {},
        by_priority: {},
      },
    };
  }

  @Get('export/csv')
  async exportToCsv(
    @Query(ValidationPipe) filterDto: DataBreachReportFilterDto,
    @Request() req: any,
  ) {
    // TODO: Implement CSV export functionality
    return {
      success: true,
      message: 'Export functionality not yet implemented',
    };
  }

  // File upload endpoint for adding attachments to existing reports
  @Post(':id/attachments')
  @UseInterceptors(FilesInterceptor('files', 5))
  async addAttachments(
    @Param('id', ParseUUIDPipe) id: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Request() req: any,
  ) {
    // TODO: Implement file upload and attachment creation
    return {
      success: true,
      message: 'File upload functionality not yet implemented',
    };
  }

  @Delete(':id/attachments/:attachmentId')
  async deleteAttachment(
    @Param('id', ParseUUIDPipe) id: string,
    @Param('attachmentId', ParseUUIDPipe) attachmentId: string,
    @Request() req: any,
  ) {
    // TODO: Implement attachment deletion
    return {
      success: true,
      message: 'Attachment deletion functionality not yet implemented',
    };
  }

  // Special endpoint for urgent/critical reports
  @Get('urgent/alerts')
  async getUrgentAlerts(@Request() req: any) {
    const urgentReports = await this.reportService.findAll(
      { 
        severity: 'critical' as any,
        status: 'submitted' as any,
        limit: 10 
      },
      req.user.user_id,
      true
    );

    return {
      success: true,
      message: 'Urgent data breach alerts retrieved successfully',
      data: urgentReports.data,
    };
  }
}
