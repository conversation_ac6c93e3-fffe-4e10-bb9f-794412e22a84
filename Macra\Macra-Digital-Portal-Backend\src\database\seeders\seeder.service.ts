import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { faker } from '@faker-js/faker';
import * as bcrypt from 'bcryptjs';
import { User, UserStatus } from '../../entities/user.entity';
import { Role, RoleName } from '../../entities/role.entity';
import { Permission, PERMISSION_NAMES } from '../../entities/permission.entity';
import { UserIdentification } from '../../entities/user-identification.entity';
import { IdentificationType } from '../../entities/identification-type.entity';
import { LicenseSeederService } from './license.seeder.service';
import { Organization } from 'src/entities/organization.entity';
import { Department } from 'src/entities/department.entity';
import { OrganizationSeederService } from './organizations.seeder';
import { DepartmentSeederService } from './departments.seeder';

@Injectable()
export class SeederService {
  constructor(
    private dataSource: DataSource,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Role)
    private rolesRepository: Repository<Role>,
    @InjectRepository(Permission)
    private permissionsRepository: Repository<Permission>,
    @InjectRepository(UserIdentification)
    private userIdentificationsRepository: Repository<UserIdentification>,
    @InjectRepository(IdentificationType)
    private identificationTypesRepository: Repository<IdentificationType>,
    private licenseSeederService: LicenseSeederService,
    private organizationSeederService: OrganizationSeederService,
    private departmentSeederService: DepartmentSeederService,
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) {}

  /**
   * Helper method to create custom permissions dynamically
   * @param name - Permission name (e.g., 'custom_entity:create')
   * @param description - Human readable description
   * @param category - Permission category
   */
  async createCustomPermission(name: string, description: string, category: string): Promise<Permission> {
    const existingPermission = await this.permissionsRepository.findOne({ where: { name } });
    if (existingPermission) {
      return existingPermission;
    }

    const permission = this.permissionsRepository.create({ name, description, category });
    return await this.permissionsRepository.save(permission);
  }

  /**
   * Helper method to generate CRUD permissions for an entity
   * @param entityName - Name of the entity (e.g., 'product', 'order')
   * @param category - Permission category (optional, defaults to entity name + ' Management')
   */
  async generateCrudPermissions(entityName: string, category?: string): Promise<Permission[]> {
    const permissionCategory = category || `${entityName.charAt(0).toUpperCase() + entityName.slice(1)} Management`;
    const permissions: Permission[] = [];

    const crudOperations = [
      { action: 'create', description: `Create new ${entityName}s` },
      { action: 'read', description: `View ${entityName} information` },
      { action: 'update', description: `Update ${entityName} information` },
      { action: 'delete', description: `Delete ${entityName}s` },
    ];

    for (const operation of crudOperations) {
      const permissionName = `${entityName}:${operation.action}`;
      const permission = await this.createCustomPermission(
        permissionName,
        operation.description,
        permissionCategory
      );
      permissions.push(permission);
    }

    return permissions;
  }

  async seedPermissions(): Promise<Permission[]> {
    console.log('🌱 Seeding permissions...');

    try {
      const existingPermissions = await this.permissionsRepository.find();
      if (existingPermissions.length > 0) {
        console.log('✅ Permissions already exist, skipping...');
        return existingPermissions;
      }
    } catch (error) {
      console.log('⚠️ Error checking existing permissions, proceeding with seeding...');
      // Continue with seeding even if we can't check existing permissions
    }

    const permissionsData = [
      // User Management
      { name: PERMISSION_NAMES.USER_CREATE, description: 'Create new users', category: 'User Management' },
      { name: PERMISSION_NAMES.USER_READ, description: 'View user information', category: 'User Management' },
      { name: PERMISSION_NAMES.USER_UPDATE, description: 'Update user information', category: 'User Management' },
      { name: PERMISSION_NAMES.USER_DELETE, description: 'Delete users', category: 'User Management' },

      // Role Management
      { name: PERMISSION_NAMES.ROLE_CREATE, description: 'Create new roles', category: 'Role Management' },
      { name: PERMISSION_NAMES.ROLE_READ, description: 'View role information', category: 'Role Management' },
      { name: PERMISSION_NAMES.ROLE_UPDATE, description: 'Update role information', category: 'Role Management' },
      { name: PERMISSION_NAMES.ROLE_DELETE, description: 'Delete roles', category: 'Role Management' },

      // Permission Management
      { name: PERMISSION_NAMES.PERMISSION_CREATE, description: 'Create new permissions', category: 'Permission Management' },
      { name: PERMISSION_NAMES.PERMISSION_READ, description: 'View permission information', category: 'Permission Management' },
      { name: PERMISSION_NAMES.PERMISSION_UPDATE, description: 'Update permission information', category: 'Permission Management' },
      { name: PERMISSION_NAMES.PERMISSION_DELETE, description: 'Delete permissions', category: 'Permission Management' },

      // License Management
      { name: PERMISSION_NAMES.LICENSE_CREATE, description: 'Create new licenses', category: 'License Management' },
      { name: PERMISSION_NAMES.LICENSE_READ, description: 'View license information', category: 'License Management' },
      { name: PERMISSION_NAMES.LICENSE_UPDATE, description: 'Update license information', category: 'License Management' },
      { name: PERMISSION_NAMES.LICENSE_DELETE, description: 'Delete licenses', category: 'License Management' },
      { name: PERMISSION_NAMES.LICENSE_APPROVE, description: 'Approve license applications', category: 'License Management' },
      { name: PERMISSION_NAMES.LICENSE_REJECT, description: 'Reject license applications', category: 'License Management' },

      // License Types Management
      { name: PERMISSION_NAMES.LICENSE_TYPE_CREATE, description: 'Create new license types', category: 'License Types Management' },
      { name: PERMISSION_NAMES.LICENSE_TYPE_READ, description: 'View license types', category: 'License Types Management' },
      { name: PERMISSION_NAMES.LICENSE_TYPE_UPDATE, description: 'Update license types', category: 'License Types Management' },
      { name: PERMISSION_NAMES.LICENSE_TYPE_DELETE, description: 'Delete license types', category: 'License Types Management' },

      // License Categories Management
      { name: PERMISSION_NAMES.LICENSE_CATEGORY_CREATE, description: 'Create new license categories', category: 'License Categories Management' },
      { name: PERMISSION_NAMES.LICENSE_CATEGORY_READ, description: 'View license categories', category: 'License Categories Management' },
      { name: PERMISSION_NAMES.LICENSE_CATEGORY_UPDATE, description: 'Update license categories', category: 'License Categories Management' },
      { name: PERMISSION_NAMES.LICENSE_CATEGORY_DELETE, description: 'Delete license categories', category: 'License Categories Management' },

      // Application Management
      { name: PERMISSION_NAMES.APPLICATION_CREATE, description: 'Create new applications', category: 'Application Management' },
      { name: PERMISSION_NAMES.APPLICATION_READ, description: 'View application information', category: 'Application Management' },
      { name: PERMISSION_NAMES.APPLICATION_UPDATE, description: 'Update application information', category: 'Application Management' },
      { name: PERMISSION_NAMES.APPLICATION_DELETE, description: 'Delete applications', category: 'Application Management' },
      { name: PERMISSION_NAMES.APPLICATION_EVALUATE, description: 'Evaluate applications', category: 'Application Management' },
      { name: PERMISSION_NAMES.APPLICATION_SUBMIT, description: 'Submit applications', category: 'Application Management' },
      { name: PERMISSION_NAMES.APPLICATION_APPROVE, description: 'Approve applications', category: 'Application Management' },
      { name: PERMISSION_NAMES.APPLICATION_REJECT, description: 'Reject applications', category: 'Application Management' },

      // Financial Management
      { name: PERMISSION_NAMES.FINANCIAL_READ, description: 'View financial information', category: 'Financial Management' },
      { name: PERMISSION_NAMES.FINANCIAL_UPDATE, description: 'Update financial information', category: 'Financial Management' },
      { name: PERMISSION_NAMES.FINANCIAL_REPORTS, description: 'Generate financial reports', category: 'Financial Management' },
      { name: PERMISSION_NAMES.INVOICE_CREATE, description: 'Create invoices', category: 'Financial Management' },
      { name: PERMISSION_NAMES.INVOICE_READ, description: 'View invoices', category: 'Financial Management' },
      { name: PERMISSION_NAMES.INVOICE_UPDATE, description: 'Update invoices', category: 'Financial Management' },
      { name: PERMISSION_NAMES.INVOICE_DELETE, description: 'Delete invoices', category: 'Financial Management' },
      { name: PERMISSION_NAMES.PAYMENT_CREATE, description: 'Create payments', category: 'Financial Management' },
      { name: PERMISSION_NAMES.PAYMENT_READ, description: 'View payments', category: 'Financial Management' },
      { name: PERMISSION_NAMES.PAYMENT_UPDATE, description: 'Update payments', category: 'Financial Management' },
      { name: PERMISSION_NAMES.PAYMENT_DELETE, description: 'Delete payments', category: 'Financial Management' },

      // Document Management
      { name: PERMISSION_NAMES.DOCUMENT_CREATE, description: 'Upload documents', category: 'Document Management' },
      { name: PERMISSION_NAMES.DOCUMENT_READ, description: 'View documents', category: 'Document Management' },
      { name: PERMISSION_NAMES.DOCUMENT_UPDATE, description: 'Update documents', category: 'Document Management' },
      { name: PERMISSION_NAMES.DOCUMENT_DELETE, description: 'Delete documents', category: 'Document Management' },
      { name: PERMISSION_NAMES.DOCUMENT_DOWNLOAD, description: 'Download documents', category: 'Document Management' },

      // Identification Types Management
      { name: PERMISSION_NAMES.IDENTIFICATION_TYPE_CREATE, description: 'Create identification types', category: 'Identification Types Management' },
      { name: PERMISSION_NAMES.IDENTIFICATION_TYPE_READ, description: 'View identification types', category: 'Identification Types Management' },
      { name: PERMISSION_NAMES.IDENTIFICATION_TYPE_UPDATE, description: 'Update identification types', category: 'Identification Types Management' },
      { name: PERMISSION_NAMES.IDENTIFICATION_TYPE_DELETE, description: 'Delete identification types', category: 'Identification Types Management' },

      // Contact Management
      { name: PERMISSION_NAMES.CONTACT_CREATE, description: 'Create contacts', category: 'Contact Management' },
      { name: PERMISSION_NAMES.CONTACT_READ, description: 'View contacts', category: 'Contact Management' },
      { name: PERMISSION_NAMES.CONTACT_UPDATE, description: 'Update contacts', category: 'Contact Management' },
      { name: PERMISSION_NAMES.CONTACT_DELETE, description: 'Delete contacts', category: 'Contact Management' },

      // Applicant Management
      { name: PERMISSION_NAMES.APPLICANT_CREATE, description: 'Create applicants', category: 'Applicant Management' },
      { name: PERMISSION_NAMES.APPLICANT_READ, description: 'View applicants', category: 'Applicant Management' },
      { name: PERMISSION_NAMES.APPLICANT_UPDATE, description: 'Update applicants', category: 'Applicant Management' },
      { name: PERMISSION_NAMES.APPLICANT_DELETE, description: 'Delete applicants', category: 'Applicant Management' },

      // Evaluation Management
      { name: PERMISSION_NAMES.EVALUATION_CREATE, description: 'Create evaluations', category: 'Evaluation Management' },
      { name: PERMISSION_NAMES.EVALUATION_READ, description: 'View evaluations', category: 'Evaluation Management' },
      { name: PERMISSION_NAMES.EVALUATION_UPDATE, description: 'Update evaluations', category: 'Evaluation Management' },
      { name: PERMISSION_NAMES.EVALUATION_DELETE, description: 'Delete evaluations', category: 'Evaluation Management' },
      { name: PERMISSION_NAMES.EVALUATION_SUBMIT, description: 'Submit evaluations', category: 'Evaluation Management' },

      // Notification Management
      { name: PERMISSION_NAMES.NOTIFICATION_CREATE, description: 'Create notifications', category: 'Notification Management' },
      { name: PERMISSION_NAMES.NOTIFICATION_READ, description: 'View notifications', category: 'Notification Management' },
      { name: PERMISSION_NAMES.NOTIFICATION_UPDATE, description: 'Update notifications', category: 'Notification Management' },
      { name: PERMISSION_NAMES.NOTIFICATION_DELETE, description: 'Delete notifications', category: 'Notification Management' },
      { name: PERMISSION_NAMES.NOTIFICATION_SEND, description: 'Send notifications', category: 'Notification Management' },

      // Reports Management
      { name: PERMISSION_NAMES.REPORT_GENERATE, description: 'Generate reports', category: 'Reports Management' },
      { name: PERMISSION_NAMES.REPORT_VIEW, description: 'View reports', category: 'Reports Management' },
      { name: PERMISSION_NAMES.REPORT_EXPORT, description: 'Export reports', category: 'Reports Management' },
      { name: PERMISSION_NAMES.REPORT_SCHEDULE, description: 'Schedule reports', category: 'Reports Management' },

      // System Administration
      { name: PERMISSION_NAMES.SYSTEM_SETTINGS, description: 'Manage system settings', category: 'System Administration' },
      { name: PERMISSION_NAMES.SYSTEM_AUDIT, description: 'View audit logs', category: 'System Administration' },
      { name: PERMISSION_NAMES.SYSTEM_BACKUP, description: 'Manage system backups', category: 'System Administration' },
      { name: PERMISSION_NAMES.SYSTEM_MAINTENANCE, description: 'Perform system maintenance', category: 'System Administration' },
    ];

    const permissions: Permission[] = [];
    for (const permissionData of permissionsData) {
      const permission = this.permissionsRepository.create(permissionData);
      const savedPermission = await this.permissionsRepository.save(permission);
      permissions.push(savedPermission);
      console.log(`✅ Created permission: ${permissionData.name}`);
    }

    // Example: Add custom permissions for additional entities
    // You can easily add more permissions here without modifying the enum
    const customPermissions = await this.seedCustomPermissions();
    permissions.push(...customPermissions);

    console.log(`✅ Total permissions created: ${permissions.length}`);
    return permissions;
  }

  /**
   * Seed additional custom permissions that might not be in the main list
   * This method can be easily extended to add new permissions without code changes
   */
  async seedCustomPermissions(): Promise<Permission[]> {
    const customPermissions: Permission[] = [];

    // Example: Add permissions for future entities
    const additionalPermissions = [
      { name: 'postal_code:create', description: 'Create postal codes', category: 'Postal Code Management' },
      { name: 'postal_code:read', description: 'View postal codes', category: 'Postal Code Management' },
      { name: 'postal_code:update', description: 'Update postal codes', category: 'Postal Code Management' },
      { name: 'postal_code:delete', description: 'Delete postal codes', category: 'Postal Code Management' },

      { name: 'employee:create', description: 'Create employees', category: 'Employee Management' },
      { name: 'employee:read', description: 'View employees', category: 'Employee Management' },
      { name: 'employee:update', description: 'Update employees', category: 'Employee Management' },
      { name: 'employee:delete', description: 'Delete employees', category: 'Employee Management' },

      { name: 'address:create', description: 'Create addresses', category: 'Address Management' },
      { name: 'address:read', description: 'View addresses', category: 'Address Management' },
      { name: 'address:update', description: 'Update addresses', category: 'Address Management' },
      { name: 'address:delete', description: 'Delete addresses', category: 'Address Management' },

      // Special permissions
      { name: 'bulk:import', description: 'Import data in bulk', category: 'Data Management' },
      { name: 'bulk:export', description: 'Export data in bulk', category: 'Data Management' },
      { name: 'data:archive', description: 'Archive old data', category: 'Data Management' },
      { name: 'data:restore', description: 'Restore archived data', category: 'Data Management' },
    ];

    for (const permissionData of additionalPermissions) {
      try {
        const permission = await this.createCustomPermission(
          permissionData.name,
          permissionData.description,
          permissionData.category
        );
        customPermissions.push(permission);
        console.log(`✅ Created custom permission: ${permissionData.name}`);
      } catch (error) {
        console.log(`⚠️ Permission ${permissionData.name} already exists or failed to create`);
      }
    }

    return customPermissions;
  }

  async seedRoles(permissions: Permission[]): Promise<Role[]> {
    console.log('🌱 Seeding roles...');

    const existingRoles = await this.rolesRepository.find();
    if (existingRoles.length > 0) {
      console.log('✅ Roles already exist, skipping...');
      return existingRoles;
    }

    // Define role permissions
    const adminPermissions = permissions; // Admin gets all permissions

    const evaluatorPermissions = permissions.filter(p =>
      p.name.includes('application:') ||
      p.name.includes('license:') ||
      p.name.includes('evaluation:') ||
      p.name.includes('document:') ||
      p.name === PERMISSION_NAMES.USER_READ ||
      p.name === PERMISSION_NAMES.CONTACT_READ ||
      p.name === PERMISSION_NAMES.APPLICANT_READ
    );

    const customerPermissions = permissions.filter(p =>
      p.name === PERMISSION_NAMES.APPLICATION_CREATE ||
      p.name === PERMISSION_NAMES.APPLICATION_READ ||
      p.name === PERMISSION_NAMES.APPLICATION_UPDATE ||
      p.name === PERMISSION_NAMES.APPLICATION_SUBMIT ||
      p.name === PERMISSION_NAMES.LICENSE_READ ||
      p.name === PERMISSION_NAMES.DOCUMENT_CREATE ||
      p.name === PERMISSION_NAMES.DOCUMENT_READ ||
      p.name === PERMISSION_NAMES.DOCUMENT_DOWNLOAD ||
      p.name === PERMISSION_NAMES.CONTACT_CREATE ||
      p.name === PERMISSION_NAMES.CONTACT_READ ||
      p.name === PERMISSION_NAMES.CONTACT_UPDATE ||
      p.name === PERMISSION_NAMES.PAYMENT_READ ||
      p.name === PERMISSION_NAMES.INVOICE_READ ||
      p.name === PERMISSION_NAMES.NOTIFICATION_READ
    );

    const legalPermissions = permissions.filter(p =>
      p.name.includes('license:') ||
      p.name.includes('application:') ||
      p.name.includes('evaluation:') ||
      p.name.includes('document:') ||
      p.name === PERMISSION_NAMES.USER_READ ||
      p.name === PERMISSION_NAMES.CONTACT_READ ||
      p.name === PERMISSION_NAMES.APPLICANT_READ ||
      p.name === PERMISSION_NAMES.REPORT_GENERATE ||
      p.name === PERMISSION_NAMES.REPORT_VIEW
    );

    const accountantPermissions = permissions.filter(p =>
      p.name.includes('financial:') ||
      p.name.includes('invoice:') ||
      p.name.includes('payment:') ||
      p.name.includes('report:') ||
      p.name === PERMISSION_NAMES.LICENSE_READ ||
      p.name === PERMISSION_NAMES.APPLICATION_READ ||
      p.name === PERMISSION_NAMES.USER_READ
    );

    const salesPermissions = permissions.filter(p =>
      p.name === PERMISSION_NAMES.APPLICATION_READ ||
      p.name === PERMISSION_NAMES.LICENSE_READ ||
      p.name === PERMISSION_NAMES.CONTACT_CREATE ||
      p.name === PERMISSION_NAMES.CONTACT_READ ||
      p.name === PERMISSION_NAMES.CONTACT_UPDATE ||
      p.name === PERMISSION_NAMES.APPLICANT_CREATE ||
      p.name === PERMISSION_NAMES.APPLICANT_READ ||
      p.name === PERMISSION_NAMES.APPLICANT_UPDATE ||
      p.name === PERMISSION_NAMES.REPORT_VIEW
    );

    // Department-specific permissions (similar to evaluator but department-focused)
    const departmentPermissions = permissions.filter(p =>
      p.name.includes('application:') ||
      p.name.includes('license:') ||
      p.name.includes('evaluation:') ||
      p.name.includes('document:') ||
      p.name === PERMISSION_NAMES.USER_READ ||
      p.name === PERMISSION_NAMES.CONTACT_READ ||
      p.name === PERMISSION_NAMES.APPLICANT_READ
    );

    const rolesData = [
      {
        name: RoleName.ADMINISTRATOR,
        description: 'Full system access with all permissions',
        permissions: adminPermissions,
      },
      {
        name: RoleName.CUSTOMER,
        description: 'Customer access for license applications and document management',
        permissions: customerPermissions,
      },
      {
        name: RoleName.EVALUATOR,
        description: 'Evaluator access for reviewing applications and managing evaluations',
        permissions: evaluatorPermissions,
      },
      {
        name: RoleName.LEGAL,
        description: 'Legal team access for license and application management',
        permissions: legalPermissions,
      },
      {
        name: RoleName.ACCOUNTANT,
        description: 'Financial management, invoicing, payments and reporting access',
        permissions: accountantPermissions,
      },
      {
        name: RoleName.SALES,
        description: 'Sales team access for customer and application management',
        permissions: salesPermissions,
      },
      {
        name: RoleName.OTHER,
        description: 'Other role with limited access',
        permissions: [],
      },
      // Department-specific roles
      {
        name: RoleName.POSTAL,
        description: 'Postal department access for postal service license applications',
        permissions: departmentPermissions,
      },
      {
        name: RoleName.TELECOMMUNICATIONS,
        description: 'Telecommunications department access for telecom license applications',
        permissions: departmentPermissions,
      },
      {
        name: RoleName.STANDARDS,
        description: 'Standards department access for standards compliance applications',
        permissions: departmentPermissions,
      },
      {
        name: RoleName.CLF,
        description: 'CLF department access for CLF license applications',
        permissions: departmentPermissions,
      },
    ];

    const roles: Role[] = [];
    for (const roleData of rolesData) {
      const role = this.rolesRepository.create({
        name: roleData.name,
        description: roleData.description,
        permissions: roleData.permissions,
      });
      const savedRole = await this.rolesRepository.save(role);
      roles.push(savedRole);
      console.log(`✅ Created role: ${roleData.name} with ${roleData.permissions.length} permissions`);
    }

    return roles;
  }

  async seedUsers(roles: Role[]): Promise<User[]> {
    console.log('🌱 Seeding users...');
    
    const existingUsers = await this.usersRepository.find();
    if (existingUsers.length > 0) {
      console.log('✅ Users already exist, skipping...');
      return existingUsers;
    }

    const adminRole = roles.find(role => role.name === RoleName.ADMINISTRATOR);
    const customerRole = roles.find(role => role.name === RoleName.CUSTOMER);
    const evaluatorRole = roles.find(role => role.name === RoleName.EVALUATOR);

    // Create admin user
    const adminUser = this.usersRepository.create({
      email: '<EMAIL>',
      password: await bcrypt.hash('Admin123!', 12),
      first_name: 'System',
      last_name: 'Administrator',
      phone: '+265999000001',
      status: UserStatus.ACTIVE,
      email_verified_at: new Date(),
      roles: adminRole ? [adminRole] : [],
    });

    // Create evaluator user
    const evaluatorUser = this.usersRepository.create({
      email: '<EMAIL>',
      password: await bcrypt.hash('Evaluator123!', 12),
      first_name: 'System',
      last_name: 'Evaluator',
      phone: '+265999000002',
      status: UserStatus.ACTIVE,
      email_verified_at: new Date(),
      roles: evaluatorRole ? [evaluatorRole] : [],
    });

    // Create test user
    const testUser = this.usersRepository.create({
      email: '<EMAIL>',
      password: await bcrypt.hash('Password123!', 12),
      first_name: 'Test',
      last_name: 'User',
      phone: '+265123456789',
      status: UserStatus.ACTIVE,
      email_verified_at: new Date(),
      roles: customerRole ? [customerRole] : [],
    });

    const users = [adminUser, evaluatorUser, testUser];

    // Generate additional random users
    for (let i = 0; i < 10; i++) {
      const randomRole = faker.helpers.arrayElement([customerRole, evaluatorRole]);
      const randomUser = this.usersRepository.create({
        email: faker.internet.email(),
        password: await bcrypt.hash('Password123!', 12),
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        middle_name: Math.random() > 0.5 ? faker.person.middleName() : undefined,
        phone: `+265${faker.string.numeric(9)}`,
        status: faker.helpers.arrayElement([UserStatus.ACTIVE, UserStatus.INACTIVE]),
        email_verified_at: Math.random() > 0.3 ? faker.date.past() : undefined,
        roles: randomRole ? [randomRole] : [],
      });
      users.push(randomUser);
    }

    const savedUsers: User[] = [];
    for (const user of users) {
      const savedUser = await this.usersRepository.save(user);
      savedUsers.push(savedUser);
      console.log(`✅ Created user: ${user.email}`);
    }

    return savedUsers;
  }


  async seedUserIdentifications(_users: User[]): Promise<UserIdentification[]> {
    console.log('🌱 Seeding user identifications...');

    const existingIdentifications = await this.userIdentificationsRepository.find();
    if (existingIdentifications.length > 0) {
      console.log('✅ User identifications already exist, skipping...');
      return existingIdentifications;
    }

    console.log('⚠️ User identification seeding skipped - requires identification types to be seeded first');
    return [];
  }

  /**
   * Handle foreign key constraint conflicts that may occur during database synchronization
   */
  async handleForeignKeyConstraints(): Promise<void> {
    console.log('🔧 Checking and handling foreign key constraints...');

    const dataSource = this.usersRepository.manager.connection;
    const dbType = dataSource.options.type;

    try {
      if (dbType === 'postgres') {
        // Check if the problematic constraint exists and drop it if necessary
        const constraintExists = await dataSource.query(`
          SELECT constraint_name
          FROM information_schema.table_constraints
          WHERE table_name = 'permissions'
          AND constraint_name = 'FK_58fae278276b7c2c6dde2bc19a5'
          AND constraint_type = 'FOREIGN KEY'
        `);

        if (constraintExists.length > 0) {
          console.log('⚠️ Found existing foreign key constraint, dropping it...');
          await dataSource.query(`
            ALTER TABLE "permissions"
            DROP CONSTRAINT IF EXISTS "FK_58fae278276b7c2c6dde2bc19a5"
          `);
          console.log('✅ Dropped existing foreign key constraint');
        }

        // Also check for the created_by constraint
        const createdByConstraintExists = await dataSource.query(`
          SELECT constraint_name
          FROM information_schema.table_constraints
          WHERE table_name = 'permissions'
          AND constraint_name LIKE 'FK_%'
          AND constraint_type = 'FOREIGN KEY'
        `);

        for (const constraint of createdByConstraintExists) {
          // Check if this constraint references the users table
          const constraintDetails = await dataSource.query(`
            SELECT
              tc.constraint_name,
              kcu.column_name,
              ccu.table_name AS foreign_table_name,
              ccu.column_name AS foreign_column_name
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_name = $1
          `, [constraint.constraint_name]);

          if (constraintDetails.length > 0 && constraintDetails[0].foreign_table_name === 'users') {
            console.log(`⚠️ Found existing foreign key constraint ${constraint.constraint_name}, dropping it...`);
            await dataSource.query(`
              ALTER TABLE "permissions"
              DROP CONSTRAINT IF EXISTS "${constraint.constraint_name}"
            `);
            console.log(`✅ Dropped existing foreign key constraint ${constraint.constraint_name}`);
          }
        }

      } else if (dbType === 'mysql' || dbType === 'mariadb') {
        // For MySQL/MariaDB, check and drop foreign key constraints
        const constraints = await dataSource.query(`
          SELECT CONSTRAINT_NAME
          FROM information_schema.KEY_COLUMN_USAGE
          WHERE TABLE_NAME = 'permissions'
          AND REFERENCED_TABLE_NAME = 'users'
          AND TABLE_SCHEMA = DATABASE()
        `);

        for (const constraint of constraints) {
          console.log(`⚠️ Found existing foreign key constraint ${constraint.CONSTRAINT_NAME}, dropping it...`);
          await dataSource.query(`
            ALTER TABLE permissions
            DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}
          `);
          console.log(`✅ Dropped existing foreign key constraint ${constraint.CONSTRAINT_NAME}`);
        }
      }

      console.log('✅ Foreign key constraint handling completed');

    } catch (error) {
      console.log('⚠️ Warning during foreign key constraint handling:', error.message);
      // Don't throw error here as this is a preventive measure
    }
  }

  /**
   * Ensure database schema is properly synchronized
   */
  async ensureDatabaseSchema(): Promise<void> {
    console.log('🔧 Ensuring database schema is synchronized...');

    const dataSource = this.usersRepository.manager.connection;

    try {
      // Force synchronization to ensure all tables and constraints are properly created
      await dataSource.synchronize(false); // false = don't drop existing data
      console.log('✅ Database schema synchronized successfully');

    } catch (error) {
      console.log('⚠️ Warning during schema synchronization:', error.message);

      // If synchronization fails due to constraint conflicts, try to handle them
      if (error.message.includes('already exists')) {
        console.log('🔧 Attempting to resolve constraint conflicts...');

        // Try to drop and recreate the problematic constraints
        await this.handleForeignKeyConstraints();

        // Retry synchronization
        try {
          await dataSource.synchronize(false);
          console.log('✅ Database schema synchronized successfully after constraint resolution');
        } catch (retryError) {
          console.log('⚠️ Schema synchronization still failed, continuing with seeding...');
        }
      }
    }
  }

  /**
   * Alternative seeding method that handles constraint conflicts more gracefully
   */
  async seedAllSafe(): Promise<void> {
    console.log('🚀 Starting safe database seeding...');

    try {
      // First, try to clear any existing constraint conflicts
      await this.resolveConstraintConflicts();

      const permissions = await this.seedPermissions();
      const roles = await this.seedRoles(permissions);
      const users = await this.seedUsers(roles);
      await this.seedUserIdentifications(users);

      // Seed license system
      await this.licenseSeederService.seedAll();

      // Seed organizations
      await this.organizationSeederService.seedAll();

      // Seed departments
      await this.departmentSeederService.seedAll();

      console.log('🎉 Safe database seeding completed successfully!');
      console.log('');
      console.log('📋 Default accounts created:');
      console.log('👤 Admin: <EMAIL> / Admin123!');
      console.log('👤 Evaluator: <EMAIL> / Evaluator123!');
      console.log('👤 Test User: <EMAIL> / Password123!');
      console.log('');

    } catch (error) {
      console.error('❌ Error during safe seeding:', error);
      throw error;
    }
  }

  /**
   * Resolve constraint conflicts by dropping and recreating problematic constraints
   */
  async resolveConstraintConflicts(): Promise<void> {
    console.log('🔧 Resolving constraint conflicts...');

    const dataSource = this.usersRepository.manager.connection;
    const dbType = dataSource.options.type;

    try {
      if (dbType === 'postgres') {
        // Drop all foreign key constraints on permissions table that reference users
        await dataSource.query(`
          DO $$
          DECLARE
            constraint_name text;
          BEGIN
            FOR constraint_name IN
              SELECT tc.constraint_name
              FROM information_schema.table_constraints tc
              JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
              JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
              WHERE tc.table_name = 'permissions'
              AND tc.constraint_type = 'FOREIGN KEY'
              AND ccu.table_name = 'users'
            LOOP
              EXECUTE 'ALTER TABLE permissions DROP CONSTRAINT IF EXISTS ' || constraint_name;
            END LOOP;
          END $$;
        `);

        console.log('✅ Dropped existing foreign key constraints on permissions table');

      } else if (dbType === 'mysql' || dbType === 'mariadb') {
        // For MySQL/MariaDB
        const constraints = await dataSource.query(`
          SELECT CONSTRAINT_NAME
          FROM information_schema.KEY_COLUMN_USAGE
          WHERE TABLE_NAME = 'permissions'
          AND REFERENCED_TABLE_NAME = 'users'
          AND TABLE_SCHEMA = DATABASE()
        `);

        for (const constraint of constraints) {
          await dataSource.query(`
            ALTER TABLE permissions
            DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}
          `);
        }

        console.log('✅ Dropped existing foreign key constraints on permissions table');
      }

    } catch (error) {
      console.log('⚠️ Warning during constraint conflict resolution:', error.message);
    }
  }

  async seedAll(): Promise<void> {
    console.log('🚀 Starting database seeding...');

    try {
      // Handle foreign key constraint issues before seeding
      await this.handleForeignKeyConstraints();

      // Ensure database schema is synchronized
      await this.ensureDatabaseSchema();

      const permissions = await this.seedPermissions();
      const roles = await this.seedRoles(permissions);
      const users = await this.seedUsers(roles);
      await this.seedUserIdentifications(users);

      // Seed license system
      await this.licenseSeederService.seedAll();

      // Seed organizations
      await this.organizationSeederService.seedAll()

      // Seed departments
      await this.departmentSeederService.seedAll();

      console.log('🎉 Database seeding completed successfully!');
      console.log('');
      console.log('📋 Default accounts created:');
      console.log('👤 Admin: <EMAIL> / Admin123!');
      console.log('👤 Operator: <EMAIL> / Operator123!');
      console.log('👤 Test User: <EMAIL> / Password123!');
      console.log('');

    } catch (error) {
      console.error('❌ Error during seeding:', error);
      throw error;
    }
  }

  async clearAll(): Promise<void> {
    console.log('🗑️ Clearing all seeded data...');

    try {
      // Clear in reverse dependency order to avoid foreign key constraint violations

      // 1. Clear user identifications first (depends on users and identification_types)
      const userIdentifications = await this.userIdentificationsRepository.find();
      if (userIdentifications.length > 0) {
        await this.userIdentificationsRepository.remove(userIdentifications);
        console.log('✅ Cleared user identifications');
      }

      // 2. Clear all entities that reference users via created_by/updated_by
      // We'll use raw SQL to avoid loading all entities into memory
      const dataSource = this.usersRepository.manager.connection;

      // Disable foreign key checks temporarily for MySQL
      if (dataSource.options.type === 'mysql') {
        await dataSource.query('SET FOREIGN_KEY_CHECKS = 0');
      }

      // Clear all tables that have foreign key references to users
      const tablesToClear = [
        'license_category_documents',
        'license_categories',
        'license_types',
        'identification_types',
        'permissions',
        'roles',
        'departments',
        'organizations',
        'user_identifications'
      ];

      for (const table of tablesToClear) {
        try {
          await dataSource.query(`DELETE FROM ${table}`);
          console.log(`✅ Cleared ${table}`);
        } catch (error) {
          console.log(`⚠️ Could not clear ${table}: ${error.message}`);
        }
      }

      // Clear junction tables
      await dataSource.query('DELETE FROM user_roles');
      await dataSource.query('DELETE FROM role_permissions');
      console.log('✅ Cleared junction tables');

      // Finally clear users
      await dataSource.query('DELETE FROM users');
      console.log('✅ Cleared users');

      // Re-enable foreign key checks for MySQL
      if (dataSource.options.type === 'mysql') {
        await dataSource.query('SET FOREIGN_KEY_CHECKS = 1');
      }

      console.log('✅ All seeded data cleared successfully!');
    } catch (error) {
      console.error('❌ Error during clearing:', error);
      throw error;
    }
  }
}
